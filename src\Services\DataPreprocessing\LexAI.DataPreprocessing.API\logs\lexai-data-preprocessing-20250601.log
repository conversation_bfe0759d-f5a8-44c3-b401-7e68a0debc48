2025-06-01 19:10:25.128 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-01 19:10:25.397 +04:00 [INF] Hangfire SQL objects installed.
2025-06-01 19:10:25.419 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-01 19:10:25.908 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-01 19:10:25.936 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 19:10:26.274 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-01 19:10:26.288 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-01 19:10:26.391 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-01 19:10:26.397 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-01 19:10:26.401 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-01 19:10:26.962 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-01 19:10:26.965 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-01 19:10:26.968 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-01 19:10:27.038 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 19:10:27.044 +04:00 [INF] Hosting environment: Development
2025-06-01 19:10:27.049 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-01 19:10:27.107 +04:00 [INF] Server datapreprocessing-kevin11:44580:b01f3406 successfully announced in 35.4096 ms
2025-06-01 19:10:27.117 +04:00 [INF] Server datapreprocessing-kevin11:44580:b01f3406 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-01 19:10:27.217 +04:00 [INF] 1 servers were removed due to timeout
2025-06-01 19:10:27.431 +04:00 [INF] Server datapreprocessing-kevin11:44580:b01f3406 all the dispatchers started
2025-06-01 19:10:27.891 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-01 19:10:28.079 +04:00 [INF] Generating processing statistics
2025-06-01 19:10:28.102 +04:00 [INF] Request GET / started with correlation ID 60e63e9a-8ed0-4456-a39f-063da8434a19
2025-06-01 19:10:28.406 +04:00 [INF] Request GET / completed in 297ms with status 404 (Correlation ID: 60e63e9a-8ed0-4456-a39f-063da8434a19)
2025-06-01 19:10:28.416 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 528.306ms
2025-06-01 19:10:28.426 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-01 19:12:32.802 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 19:12:32.803 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 19:12:32.849 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 991051bb-ecbd-43d1-84e9-da0f23d48983
2025-06-01 19:12:32.850 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID e5fbeea5-8240-4f99-983d-150dcd1f1932
2025-06-01 19:12:32.869 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:12:32.883 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:12:32.893 +04:00 [INF] Request OPTIONS /api/documents completed in 37ms with status 204 (Correlation ID: 991051bb-ecbd-43d1-84e9-da0f23d48983)
2025-06-01 19:12:32.894 +04:00 [INF] Request OPTIONS /api/documents completed in 12ms with status 204 (Correlation ID: e5fbeea5-8240-4f99-983d-150dcd1f1932)
2025-06-01 19:12:32.899 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 97.5088ms
2025-06-01 19:12:32.917 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 114.8921ms
2025-06-01 19:12:32.916 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 19:12:32.958 +04:00 [INF] Request GET /api/documents started with correlation ID 22cc92da-8e32-4248-b6ae-bd0d76e1dea4
2025-06-01 19:12:32.962 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:12:33.083 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:12:33.111 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:12:33.216 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 19:12:43.959 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:12:43.964 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:12:43.970 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:12:43.979 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:12:43.984 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:12:43.990 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:12:44.584 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 19:12:44.973 +04:00 [INF] Executed DbCommand (47ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 19:12:44.993 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 19:12:45.035 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 19:12:45.044 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 11809.2481ms
2025-06-01 19:12:45.056 +04:00 [INF] Request GET /api/documents started with correlation ID bb7f07fe-f651-46f7-b5f9-47595753adcc
2025-06-01 19:12:45.064 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:12:45.064 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:12:45.075 +04:00 [INF] Request GET /api/documents completed in 12113ms with status 200 (Correlation ID: 22cc92da-8e32-4248-b6ae-bd0d76e1dea4)
2025-06-01 19:12:45.080 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:12:45.090 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:12:45.098 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 19:12:45.116 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 12200.5241ms
2025-06-01 19:12:45.177 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 19:12:45.181 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 19:12:45.183 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 72.1485ms
2025-06-01 19:12:45.189 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:12:45.192 +04:00 [INF] Request GET /api/documents completed in 127ms with status 200 (Correlation ID: bb7f07fe-f651-46f7-b5f9-47595753adcc)
2025-06-01 19:12:45.195 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 160.3533ms
2025-06-01 19:14:20.702 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-01 19:14:20.724 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 38d4b172-59c7-4274-bd5e-1db7745933e0
2025-06-01 19:14:20.732 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:14:20.737 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 5ms with status 204 (Correlation ID: 38d4b172-59c7-4274-bd5e-1db7745933e0)
2025-06-01 19:14:20.744 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 41.6745ms
2025-06-01 19:14:20.786 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryiMmF2Rt9VGpVGo5b 473870
2025-06-01 19:14:20.799 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 7539e3cd-a0a4-4a9a-9f96-5e8dd8384ee8
2025-06-01 19:14:20.801 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:14:20.804 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:14:20.812 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 19:14:20.830 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(LexAI.DataPreprocessing.Application.DTOs.DocumentUploadRequestDto) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 19:14:20.985 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 19:14:21.040 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 203.6955ms
2025-06-01 19:14:21.046 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 19:14:21.053 +04:00 [INF] Request POST /api/documents/upload completed in 251ms with status 415 (Correlation ID: 7539e3cd-a0a4-4a9a-9f96-5e8dd8384ee8)
2025-06-01 19:14:21.077 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 415 null application/json; charset=utf-8 290.2827ms
2025-06-01 19:18:24.868 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-01 19:18:24.968 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID bfe2eca6-a837-486d-b618-805e6e208765
2025-06-01 19:18:24.976 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:18:24.979 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 3ms with status 204 (Correlation ID: bfe2eca6-a837-486d-b618-805e6e208765)
2025-06-01 19:18:24.987 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 117.9937ms
2025-06-01 19:18:24.992 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryU7tHnTRBQAFQ1ARz 473870
2025-06-01 19:18:25.043 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 310712be-3757-44b7-a0dd-6003968c8532
2025-06-01 19:18:25.045 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:18:25.047 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:18:25.049 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 19:18:25.050 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(LexAI.DataPreprocessing.Application.DTOs.DocumentUploadRequestDto) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 19:18:25.072 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 19:18:25.077 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 23.6987ms
2025-06-01 19:18:25.081 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 19:18:25.083 +04:00 [INF] Request POST /api/documents/upload completed in 37ms with status 415 (Correlation ID: 310712be-3757-44b7-a0dd-6003968c8532)
2025-06-01 19:18:25.087 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 415 null application/json; charset=utf-8 94.969ms
2025-06-01 19:52:04.353 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-01 19:52:04.471 +04:00 [INF] Hangfire SQL objects installed.
2025-06-01 19:52:04.481 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-01 19:52:04.684 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-01 19:52:04.696 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 19:52:04.784 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-01 19:52:04.786 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-01 19:52:04.792 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-01 19:52:04.794 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-01 19:52:04.795 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-01 19:52:04.796 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-01 19:52:04.797 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-01 19:52:04.798 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-01 19:52:04.812 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 19:52:04.815 +04:00 [INF] Hosting environment: Development
2025-06-01 19:52:04.817 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-01 19:52:04.836 +04:00 [INF] Server datapreprocessing-kevin11:51992:624f908f successfully announced in 15.6173 ms
2025-06-01 19:52:04.841 +04:00 [INF] Server datapreprocessing-kevin11:51992:624f908f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-01 19:52:04.856 +04:00 [INF] 1 servers were removed due to timeout
2025-06-01 19:52:04.858 +04:00 [INF] Server datapreprocessing-kevin11:51992:624f908f all the dispatchers started
2025-06-01 19:52:30.705 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 19:52:30.705 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 19:52:30.848 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID b93042ca-f6ec-43ca-a38c-217505147ee0
2025-06-01 19:52:30.848 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID a8d36086-f746-4944-8da3-84a8697b32b0
2025-06-01 19:52:30.859 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:52:30.859 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:52:30.864 +04:00 [INF] Request OPTIONS /api/documents completed in 5ms with status 204 (Correlation ID: a8d36086-f746-4944-8da3-84a8697b32b0)
2025-06-01 19:52:30.864 +04:00 [INF] Request OPTIONS /api/documents completed in 7ms with status 204 (Correlation ID: b93042ca-f6ec-43ca-a38c-217505147ee0)
2025-06-01 19:52:30.880 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 175.7942ms
2025-06-01 19:52:30.895 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 191.3865ms
2025-06-01 19:52:30.922 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 19:52:30.962 +04:00 [INF] Request GET /api/documents started with correlation ID da61069e-f3dc-4259-b416-e4f6b18a51dc
2025-06-01 19:52:30.968 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:52:31.175 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:52:31.191 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:52:31.279 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 19:52:35.333 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:52:35.340 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:52:35.343 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:52:35.354 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:52:35.359 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:52:35.367 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:52:37.684 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 19:52:38.657 +04:00 [INF] Executed DbCommand (69ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 19:52:38.709 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 19:52:38.834 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 19:52:38.836 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 7538.6761ms
2025-06-01 19:52:38.840 +04:00 [INF] Request GET /api/documents started with correlation ID 72689a78-bb5a-4ed4-a31c-b5da066fb6dd
2025-06-01 19:52:38.843 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:52:38.844 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:52:38.848 +04:00 [INF] Request GET /api/documents completed in 7881ms with status 200 (Correlation ID: da61069e-f3dc-4259-b416-e4f6b18a51dc)
2025-06-01 19:52:38.850 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:52:38.858 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:52:38.861 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 19:52:38.869 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 7947.3777ms
2025-06-01 19:52:38.952 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 19:52:39.063 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 19:52:39.068 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 203.2067ms
2025-06-01 19:52:39.074 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:52:39.077 +04:00 [INF] Request GET /api/documents completed in 233ms with status 200 (Correlation ID: 72689a78-bb5a-4ed4-a31c-b5da066fb6dd)
2025-06-01 19:52:39.082 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 247.678ms
2025-06-01 19:54:50.337 +04:00 [INF] Server datapreprocessing-kevin11:51992:624f908f caught stopping signal...
2025-06-01 19:54:50.339 +04:00 [INF] Application is shutting down...
2025-06-01 19:54:50.342 +04:00 [INF] Server datapreprocessing-kevin11:51992:624f908f All dispatchers stopped
2025-06-01 19:54:50.664 +04:00 [INF] Server datapreprocessing-kevin11:51992:624f908f successfully reported itself as stopped in 319.1308 ms
2025-06-01 19:54:50.666 +04:00 [INF] Server datapreprocessing-kevin11:51992:624f908f has been stopped in total 326.9679 ms
2025-06-01 19:56:16.852 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-01 19:56:16.998 +04:00 [INF] Hangfire SQL objects installed.
2025-06-01 19:56:17.014 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-01 19:56:17.544 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-01 19:56:17.581 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 19:56:17.894 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-01 19:56:17.896 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-01 19:56:17.958 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-01 19:56:17.961 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-01 19:56:17.976 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-01 19:56:17.978 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-01 19:56:17.980 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-01 19:56:17.982 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-01 19:56:18.023 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 19:56:18.026 +04:00 [INF] Hosting environment: Development
2025-06-01 19:56:18.028 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-01 19:56:18.692 +04:00 [INF] Server datapreprocessing-kevin11:42516:f7e749f1 successfully announced in 29.3413 ms
2025-06-01 19:56:18.727 +04:00 [INF] Server datapreprocessing-kevin11:42516:f7e749f1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-01 19:56:18.987 +04:00 [INF] Server datapreprocessing-kevin11:42516:f7e749f1 all the dispatchers started
2025-06-01 19:56:19.396 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-01 19:56:19.530 +04:00 [INF] Request GET / started with correlation ID a0dd42c8-fd17-4313-9f37-d68de25ed6a5
2025-06-01 19:56:19.641 +04:00 [INF] Request GET / completed in 106ms with status 404 (Correlation ID: a0dd42c8-fd17-4313-9f37-d68de25ed6a5)
2025-06-01 19:56:19.651 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 256.0381ms
2025-06-01 19:56:19.661 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-01 19:56:31.940 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 19:56:31.940 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 19:56:31.949 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 82105921-1106-4c33-8f50-6180aa72a968
2025-06-01 19:56:31.950 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID ed7d9e1a-0886-4e08-9f2c-d29e2582c954
2025-06-01 19:56:31.957 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:56:31.958 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:56:31.963 +04:00 [INF] Request OPTIONS /api/documents completed in 10ms with status 204 (Correlation ID: 82105921-1106-4c33-8f50-6180aa72a968)
2025-06-01 19:56:31.963 +04:00 [INF] Request OPTIONS /api/documents completed in 6ms with status 204 (Correlation ID: ed7d9e1a-0886-4e08-9f2c-d29e2582c954)
2025-06-01 19:56:31.966 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 26.8882ms
2025-06-01 19:56:31.971 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 31.9048ms
2025-06-01 19:56:32.024 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 19:56:32.046 +04:00 [INF] Request GET /api/documents started with correlation ID 799b7e13-4540-4395-8d72-7ae172698b92
2025-06-01 19:56:32.048 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:56:32.121 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:56:32.129 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:56:32.146 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 19:56:32.941 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:56:32.945 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:56:32.946 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:56:32.950 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:56:32.954 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:56:32.957 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:56:33.433 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 19:56:33.617 +04:00 [INF] Executed DbCommand (15ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 19:56:33.637 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 19:56:33.677 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 1525.0286ms
2025-06-01 19:56:33.678 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 19:56:33.680 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:56:33.684 +04:00 [INF] Request GET /api/documents started with correlation ID 144c69b2-18ae-4013-9e05-c09a780d244c
2025-06-01 19:56:33.685 +04:00 [INF] Request GET /api/documents completed in 1637ms with status 200 (Correlation ID: 799b7e13-4540-4395-8d72-7ae172698b92)
2025-06-01 19:56:33.687 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:56:33.696 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:56:33.698 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:56:33.699 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 1678.5691ms
2025-06-01 19:56:33.700 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 19:56:33.719 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 19:56:33.726 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 19:56:33.728 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 18.7851ms
2025-06-01 19:56:33.730 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:56:33.732 +04:00 [INF] Request GET /api/documents completed in 44ms with status 200 (Correlation ID: 144c69b2-18ae-4013-9e05-c09a780d244c)
2025-06-01 19:56:33.735 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 56.1798ms
2025-06-01 19:57:45.696 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-01 19:57:45.712 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 9deb3600-b450-4cdf-81d7-1dff36269b89
2025-06-01 19:57:45.721 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:57:45.723 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 2ms with status 204 (Correlation ID: 9deb3600-b450-4cdf-81d7-1dff36269b89)
2025-06-01 19:57:45.727 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 30.524ms
2025-06-01 19:57:45.733 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryZpwFiWRIiNJU2s5M 473860
2025-06-01 19:57:45.743 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 078694e7-1402-47d9-8431-a411589a83e0
2025-06-01 19:57:45.744 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:57:45.746 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:57:45.753 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 19:57:45.768 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 19:58:28.742 +04:00 [INF] Document upload request from user "fc4ebb70-007f-43d4-873b-c865709b364d": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-01 19:58:28.814 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "fc4ebb70-007f-43d4-873b-c865709b364d"
2025-06-01 19:58:28.862 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 19:58:28.878 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-01 19:58:28.890 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> dc15c4a0-b421-49e0-8c84-b3a1fd2ab062.pdf
2025-06-01 19:58:29.140 +04:00 [ERR] Failed executing DbCommand (17ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Int64), @p26='?' (DbType = Guid), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Guid), @p30='?', @p31='?' (DbType = Boolean), @p32='?' (DbType = Object), @p33='?' (DbType = DateTime), @p34='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase", xmin)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25);
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
2025-06-01 19:58:29.169 +04:00 [ERR] An exception occurred in the database while saving changes for context type 'LexAI.DataPreprocessing.Infrastructure.Data.DataPreprocessingDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 42703: column "xmin" of relation "Documents" does not exist

POSITION: 389
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column "xmin" of relation "Documents" does not exist
    Position: 389
    File: parse_target.c
    Line: 1066
    Routine: checkInsertTargets
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 42703: column "xmin" of relation "Documents" does not exist

POSITION: 389
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column "xmin" of relation "Documents" does not exist
    Position: 389
    File: parse_target.c
    Line: 1066
    Routine: checkInsertTargets
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-06-01 19:58:29.294 +04:00 [ERR] Error adding document "b0c20943-209f-419c-a583-5c07cf00c4aa"
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 42703: column "xmin" of relation "Documents" does not exist

POSITION: 389
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column "xmin" of relation "Documents" does not exist
    Position: 389
    File: parse_target.c
    Line: 1066
    Routine: checkInsertTargets
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.AddAsync(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 151
2025-06-01 19:58:29.411 +04:00 [ERR] Error uploading document INTRODUCTION AU DROIT DU TRAVAIL.pdf
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 42703: column "xmin" of relation "Documents" does not exist

POSITION: 389
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column "xmin" of relation "Documents" does not exist
    Position: 389
    File: parse_target.c
    Line: 1066
    Routine: checkInsertTargets
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.AddAsync(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 151
   at LexAI.DataPreprocessing.Application.Commands.UploadDocumentCommandHandler.Handle(UploadDocumentCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Application\Commands\DocumentCommands.cs:line 119
2025-06-01 19:58:29.540 +04:00 [ERR] Error uploading document for user "fc4ebb70-007f-43d4-873b-c865709b364d"
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 42703: column "xmin" of relation "Documents" does not exist

POSITION: 389
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column "xmin" of relation "Documents" does not exist
    Position: 389
    File: parse_target.c
    Line: 1066
    Routine: checkInsertTargets
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.AddAsync(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 151
   at LexAI.DataPreprocessing.Application.Commands.UploadDocumentCommandHandler.Handle(UploadDocumentCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Application\Commands\DocumentCommands.cs:line 119
   at LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument(IFormFile file, String metadata) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API\Controllers\DocumentsController.cs:line 124
2025-06-01 19:58:29.559 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 19:58:29.575 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 43793.3279ms
2025-06-01 19:58:29.579 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 19:58:29.581 +04:00 [INF] Request POST /api/documents/upload completed in 43836ms with status 500 (Correlation ID: 078694e7-1402-47d9-8431-a411589a83e0)
2025-06-01 19:58:29.587 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 500 null application/json; charset=utf-8 43853.6517ms
2025-06-01 20:00:14.322 +04:00 [INF] Generating processing statistics
2025-06-01 20:13:01.604 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-01 20:13:02.542 +04:00 [INF] Hangfire SQL objects installed.
2025-06-01 20:13:02.552 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-01 20:13:02.951 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-01 20:13:02.967 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 20:13:03.160 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-01 20:13:03.162 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-01 20:13:03.215 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-01 20:13:03.218 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-01 20:13:03.220 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-01 20:13:03.231 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-01 20:13:03.232 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-01 20:13:03.234 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-01 20:13:03.264 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 20:13:03.265 +04:00 [INF] Hosting environment: Development
2025-06-01 20:13:03.267 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-01 20:13:03.369 +04:00 [INF] Server datapreprocessing-kevin11:48944:42348b35 successfully announced in 80.2647 ms
2025-06-01 20:13:03.393 +04:00 [INF] Server datapreprocessing-kevin11:48944:42348b35 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-01 20:13:03.633 +04:00 [INF] Server datapreprocessing-kevin11:48944:42348b35 all the dispatchers started
2025-06-01 20:13:04.349 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-01 20:13:04.641 +04:00 [INF] Request GET / started with correlation ID 64798c81-6cc1-43ec-8ce5-de990672407b
2025-06-01 20:13:04.774 +04:00 [INF] Request GET / completed in 126ms with status 404 (Correlation ID: 64798c81-6cc1-43ec-8ce5-de990672407b)
2025-06-01 20:13:04.785 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 436.2022ms
2025-06-01 20:13:04.794 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-01 20:13:37.022 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-01 20:13:37.054 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 039fe297-9466-42aa-b2af-1ec68d4dd601
2025-06-01 20:13:37.068 +04:00 [INF] CORS policy execution successful.
2025-06-01 20:13:37.080 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 20ms with status 204 (Correlation ID: 039fe297-9466-42aa-b2af-1ec68d4dd601)
2025-06-01 20:13:37.085 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 63.9383ms
2025-06-01 20:13:37.094 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundarydDiLtZ5nB4LPBfBw 473860
2025-06-01 20:13:37.110 +04:00 [INF] Request POST /api/documents/upload started with correlation ID e1a4055e-5d3e-498a-802b-c3a562bf1928
2025-06-01 20:13:37.117 +04:00 [INF] CORS policy execution successful.
2025-06-01 20:13:37.209 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 20:13:37.217 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 20:13:37.240 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 20:13:38.022 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 20:13:38.024 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 20:13:38.026 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 20:13:38.028 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 20:13:38.030 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 20:13:38.031 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 20:13:48.534 +04:00 [INF] Document upload request from user "fc4ebb70-007f-43d4-873b-c865709b364d": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-01 20:13:48.576 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "fc4ebb70-007f-43d4-873b-c865709b364d"
2025-06-01 20:13:48.968 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 20:13:49.085 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-01 20:13:49.099 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> 5cb95414-1224-43ba-8f45-e5b321ab3394.pdf
2025-06-01 20:13:49.303 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-01 20:13:49.330 +04:00 [INF] Document added successfully: "1e088fa5-9a25-4833-8235-f0660033afe1"
2025-06-01 20:13:49.331 +04:00 [INF] Document uploaded successfully: "1e088fa5-9a25-4833-8235-f0660033afe1" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-01 20:13:49.334 +04:00 [INF] Document uploaded successfully for user "fc4ebb70-007f-43d4-873b-c865709b364d". Document: "1e088fa5-9a25-4833-8235-f0660033afe1", Processing started: false
2025-06-01 20:13:49.340 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-01 20:13:49.359 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 12110.901ms
2025-06-01 20:13:49.364 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 20:13:49.370 +04:00 [INF] Request POST /api/documents/upload completed in 12254ms with status 200 (Correlation ID: e1a4055e-5d3e-498a-802b-c3a562bf1928)
2025-06-01 20:13:49.399 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 12304.8092ms
2025-06-01 20:14:07.503 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-01 20:14:07.507 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-01 20:14:07.521 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID e45b73da-1bab-458a-b368-ba62995e3854
2025-06-01 20:14:07.532 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID 74f6bb78-2859-4161-9491-7f9b86b7d2d7
2025-06-01 20:14:07.538 +04:00 [INF] CORS policy execution successful.
2025-06-01 20:14:07.544 +04:00 [INF] CORS policy execution successful.
2025-06-01 20:14:07.548 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 10ms with status 204 (Correlation ID: e45b73da-1bab-458a-b368-ba62995e3854)
2025-06-01 20:14:07.553 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 9ms with status 204 (Correlation ID: 74f6bb78-2859-4161-9491-7f9b86b7d2d7)
2025-06-01 20:14:07.575 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 71.6819ms
2025-06-01 20:14:07.584 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-01 20:14:07.592 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 85.1865ms
2025-06-01 20:14:07.628 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID 9025908f-6404-49a1-a967-a82b5f35c67b
2025-06-01 20:14:07.642 +04:00 [INF] CORS policy execution successful.
2025-06-01 20:14:07.655 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 20:14:07.658 +04:00 [INF] Request GET /api/documents/undefined completed in 15ms with status 404 (Correlation ID: 9025908f-6404-49a1-a967-a82b5f35c67b)
2025-06-01 20:14:07.665 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 80.3726ms
2025-06-01 20:14:07.672 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-01 20:14:07.677 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-01 20:14:07.689 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID 349c4318-66b2-4bb7-be5e-2bf4603aebb4
2025-06-01 20:14:07.703 +04:00 [INF] CORS policy execution successful.
2025-06-01 20:14:07.706 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 20:14:07.710 +04:00 [INF] Request GET /api/documents/undefined completed in 7ms with status 404 (Correlation ID: 349c4318-66b2-4bb7-be5e-2bf4603aebb4)
2025-06-01 20:14:07.720 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 48.6405ms
2025-06-01 20:14:07.749 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-01 20:15:42.120 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 20:15:42.134 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 20:15:42.138 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID faf01fdb-68ac-400f-8e59-929ac6ebc285
2025-06-01 20:15:42.141 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 7144ca75-b87c-46a3-9761-e403f01a2e29
2025-06-01 20:15:42.143 +04:00 [INF] CORS policy execution successful.
2025-06-01 20:15:42.146 +04:00 [INF] CORS policy execution successful.
2025-06-01 20:15:42.148 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: faf01fdb-68ac-400f-8e59-929ac6ebc285)
2025-06-01 20:15:42.151 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: 7144ca75-b87c-46a3-9761-e403f01a2e29)
2025-06-01 20:15:42.155 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 34.115ms
2025-06-01 20:15:42.218 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 83.3573ms
2025-06-01 20:15:42.217 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 20:15:42.263 +04:00 [INF] Request GET /api/documents started with correlation ID 015edcfc-1bee-4405-8a68-e6de7b135f3f
2025-06-01 20:15:42.268 +04:00 [INF] CORS policy execution successful.
2025-06-01 20:15:42.271 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 20:15:42.280 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 20:15:42.312 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 20:15:42.538 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 20:15:42.604 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 20:15:42.672 +04:00 [ERR] An exception occurred while iterating over the results of a query for context type 'LexAI.DataPreprocessing.Infrastructure.Data.DataPreprocessingDbContext'.
System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'.
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException(ReadStack& state, Utf8JsonReader& reader, Exception innerException)
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_DeserializeNoConstructor(JsonTypeInfo typeInfo, Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at lambda_method479(Closure, String)
   at lambda_method457(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'.
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException(ReadStack& state, Utf8JsonReader& reader, Exception innerException)
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_DeserializeNoConstructor(JsonTypeInfo typeInfo, Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at lambda_method479(Closure, String)
   at lambda_method457(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-01 20:15:43.059 +04:00 [ERR] Error getting documents by user "fc4ebb70-007f-43d4-873b-c865709b364d"
System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'.
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException(ReadStack& state, Utf8JsonReader& reader, Exception innerException)
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_DeserializeNoConstructor(JsonTypeInfo typeInfo, Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at lambda_method479(Closure, String)
   at lambda_method457(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.GetByUserAsync(Guid userId, Int32 limit, Int32 offset, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 123
2025-06-01 20:15:43.398 +04:00 [ERR] Error retrieving documents for user "fc4ebb70-007f-43d4-873b-c865709b364d"
System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'.
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException(ReadStack& state, Utf8JsonReader& reader, Exception innerException)
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_DeserializeNoConstructor(JsonTypeInfo typeInfo, Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at lambda_method479(Closure, String)
   at lambda_method457(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.GetByUserAsync(Guid userId, Int32 limit, Int32 offset, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 123
   at LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments(Int32 limit, Int32 offset) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API\Controllers\DocumentsController.cs:line 388
2025-06-01 20:15:43.421 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 20:15:43.481 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 1157.22ms
2025-06-01 20:15:43.485 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 20:15:43.491 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 20:15:43.502 +04:00 [INF] Request GET /api/documents started with correlation ID 1324f0b5-8f26-438f-a718-96427bf4efc7
2025-06-01 20:15:43.506 +04:00 [INF] Request GET /api/documents completed in 1237ms with status 500 (Correlation ID: 015edcfc-1bee-4405-8a68-e6de7b135f3f)
2025-06-01 20:15:43.512 +04:00 [INF] CORS policy execution successful.
2025-06-01 20:15:43.519 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 500 null application/json; charset=utf-8 1302.6518ms
2025-06-01 20:15:43.525 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 20:15:43.551 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 20:15:43.557 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 20:15:43.807 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 20:15:43.812 +04:00 [ERR] An exception occurred while iterating over the results of a query for context type 'LexAI.DataPreprocessing.Infrastructure.Data.DataPreprocessingDbContext'.
System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'.
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException(ReadStack& state, Utf8JsonReader& reader, Exception innerException)
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_DeserializeNoConstructor(JsonTypeInfo typeInfo, Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at lambda_method479(Closure, String)
   at lambda_method457(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'.
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException(ReadStack& state, Utf8JsonReader& reader, Exception innerException)
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_DeserializeNoConstructor(JsonTypeInfo typeInfo, Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at lambda_method479(Closure, String)
   at lambda_method457(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-01 20:15:43.913 +04:00 [ERR] Error getting documents by user "fc4ebb70-007f-43d4-873b-c865709b364d"
System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'.
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException(ReadStack& state, Utf8JsonReader& reader, Exception innerException)
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_DeserializeNoConstructor(JsonTypeInfo typeInfo, Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at lambda_method479(Closure, String)
   at lambda_method457(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.GetByUserAsync(Guid userId, Int32 limit, Int32 offset, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 123
2025-06-01 20:15:44.108 +04:00 [ERR] Error retrieving documents for user "fc4ebb70-007f-43d4-873b-c865709b364d"
System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.NotSupportedException: Deserialization of types without a parameterless constructor, a singular parameterized constructor, or a parameterized constructor annotated with 'JsonConstructorAttribute' is not supported. Type 'LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata'.
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException(ReadStack& state, Utf8JsonReader& reader, Exception innerException)
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_DeserializeNoConstructor(JsonTypeInfo typeInfo, Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at lambda_method479(Closure, String)
   at lambda_method457(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.GetByUserAsync(Guid userId, Int32 limit, Int32 offset, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 123
   at LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments(Int32 limit, Int32 offset) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API\Controllers\DocumentsController.cs:line 388
2025-06-01 20:15:44.119 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 20:15:44.124 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 557.9085ms
2025-06-01 20:15:44.126 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 20:15:44.129 +04:00 [INF] Request GET /api/documents completed in 616ms with status 500 (Correlation ID: 1324f0b5-8f26-438f-a718-96427bf4efc7)
2025-06-01 20:15:44.134 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 500 null application/json; charset=utf-8 648.9402ms
2025-06-01 20:18:18.132 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire - null null
2025-06-01 20:18:18.429 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire - 200 null text/html 296.2861ms
2025-06-01 20:18:18.444 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/css18140312644497 - null null
2025-06-01 20:18:18.444 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/css-dark18140549433121 - null null
2025-06-01 20:18:18.458 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 20:18:18.456 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/js18140912627127 - null null
2025-06-01 20:18:18.661 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 202.8527ms
2025-06-01 20:18:18.662 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/css-dark18140549433121 - 200 null text/css 218.0162ms
2025-06-01 20:18:18.662 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/css18140312644497 - 200 null text/css 217.998ms
2025-06-01 20:18:18.669 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-01 20:18:18.684 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/js18140912627127 - 200 null application/javascript 227.7589ms
2025-06-01 20:18:18.813 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 144.2687ms
2025-06-01 20:18:20.835 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:20.898 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 62.8736ms
2025-06-01 20:18:22.915 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:22.933 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.4131ms
2025-06-01 20:18:25.890 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:25.938 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 47.7899ms
2025-06-01 20:18:28.902 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:28.928 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.5573ms
2025-06-01 20:18:31.898 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:31.926 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 27.2367ms
2025-06-01 20:18:34.887 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:34.904 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.7492ms
2025-06-01 20:18:37.897 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:37.911 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.482ms
2025-06-01 20:18:40.887 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:40.899 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.6811ms
2025-06-01 20:18:43.900 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:43.929 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 28.1894ms
2025-06-01 20:18:46.891 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:46.913 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.6542ms
2025-06-01 20:18:49.898 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:49.923 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.8488ms
2025-06-01 20:18:52.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:52.910 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.0441ms
2025-06-01 20:18:55.899 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:55.918 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.8008ms
2025-06-01 20:18:58.899 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:18:58.918 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.151ms
2025-06-01 20:19:01.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:01.919 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.7054ms
2025-06-01 20:19:04.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:04.912 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.9439ms
2025-06-01 20:19:07.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:07.932 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 35.2745ms
2025-06-01 20:19:10.886 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:10.903 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.2876ms
2025-06-01 20:19:13.899 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:13.919 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.4992ms
2025-06-01 20:19:16.898 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:16.918 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.8407ms
2025-06-01 20:19:19.898 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:19.918 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.65ms
2025-06-01 20:19:22.897 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:22.913 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.1997ms
2025-06-01 20:19:25.893 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:25.908 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.5195ms
2025-06-01 20:19:28.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:28.912 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.4585ms
2025-06-01 20:19:31.895 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:31.915 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.3465ms
2025-06-01 20:19:34.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:34.912 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.9737ms
2025-06-01 20:19:37.897 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:37.908 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.6099ms
2025-06-01 20:19:40.892 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:40.908 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.9722ms
2025-06-01 20:19:43.893 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:43.906 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.9027ms
2025-06-01 20:19:46.900 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:46.929 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 29.6524ms
2025-06-01 20:19:49.887 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:49.912 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.2516ms
2025-06-01 20:19:52.901 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:52.927 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.448ms
2025-06-01 20:19:55.888 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:55.902 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.2054ms
2025-06-01 20:19:58.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:19:58.918 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.8824ms
2025-06-01 20:20:01.886 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:01.895 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.2092ms
2025-06-01 20:20:04.899 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:04.910 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.132ms
2025-06-01 20:20:07.895 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:07.917 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.4465ms
2025-06-01 20:20:10.897 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:10.910 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.5418ms
2025-06-01 20:20:13.887 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:13.895 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.4491ms
2025-06-01 20:20:16.895 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:16.921 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.1493ms
2025-06-01 20:20:19.905 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:19.925 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.0898ms
2025-06-01 20:20:22.900 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:22.913 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.9607ms
2025-06-01 20:20:25.897 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:25.915 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.6416ms
2025-06-01 20:20:28.899 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:28.912 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.3589ms
2025-06-01 20:20:31.892 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:31.908 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.5229ms
2025-06-01 20:20:34.891 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:34.914 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.4614ms
2025-06-01 20:20:37.900 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:37.917 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.0534ms
2025-06-01 20:20:40.898 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:40.915 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.5743ms
2025-06-01 20:20:43.894 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:43.913 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.0757ms
2025-06-01 20:20:46.886 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:46.913 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 27.7404ms
2025-06-01 20:20:49.889 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:49.902 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.585ms
2025-06-01 20:20:52.893 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:52.905 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.4109ms
2025-06-01 20:20:55.894 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:55.904 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.4477ms
2025-06-01 20:20:58.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:20:58.908 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.4516ms
2025-06-01 20:21:01.888 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:01.905 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.8065ms
2025-06-01 20:21:04.892 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:04.910 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.2348ms
2025-06-01 20:21:07.892 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:07.917 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.1386ms
2025-06-01 20:21:10.892 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:10.905 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.1232ms
2025-06-01 20:21:13.895 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:13.908 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.4138ms
2025-06-01 20:21:16.899 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:16.916 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.1896ms
2025-06-01 20:21:19.895 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:19.912 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.3746ms
2025-06-01 20:21:22.897 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:22.911 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.4034ms
2025-06-01 20:21:25.906 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:25.920 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.4617ms
2025-06-01 20:21:28.894 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:28.905 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.0496ms
2025-06-01 20:21:31.886 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:31.905 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.3594ms
2025-06-01 20:21:34.895 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:34.912 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.0353ms
2025-06-01 20:21:37.894 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:37.911 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.5504ms
2025-06-01 20:21:40.886 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:40.900 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.3358ms
2025-06-01 20:21:43.894 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:43.907 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.0501ms
2025-06-01 20:21:46.887 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:46.911 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.3254ms
2025-06-01 20:21:49.895 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:49.910 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.7994ms
2025-06-01 20:21:52.899 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:52.908 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.9732ms
2025-06-01 20:21:55.890 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:55.909 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.0598ms
2025-06-01 20:21:58.893 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:21:58.908 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.1676ms
2025-06-01 20:22:01.895 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:01.910 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.3055ms
2025-06-01 20:22:04.889 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:04.909 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.0321ms
2025-06-01 20:22:07.887 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:07.899 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.4552ms
2025-06-01 20:22:10.893 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:10.914 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.9356ms
2025-06-01 20:22:13.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:13.913 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.8298ms
2025-06-01 20:22:16.888 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:16.904 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.403ms
2025-06-01 20:22:19.887 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:19.905 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.412ms
2025-06-01 20:22:22.891 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:22.902 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.3311ms
2025-06-01 20:22:25.894 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:25.921 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.7936ms
2025-06-01 20:22:28.886 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:28.899 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.3565ms
2025-06-01 20:22:31.892 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:31.911 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.9448ms
2025-06-01 20:22:34.890 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:34.916 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.2682ms
2025-06-01 20:22:37.899 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:37.911 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.5924ms
2025-06-01 20:22:40.899 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:40.914 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.7198ms
2025-06-01 20:22:43.889 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:43.904 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.8694ms
2025-06-01 20:22:46.897 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:46.939 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 42.194ms
2025-06-01 20:22:49.897 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:49.907 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.8187ms
2025-06-01 20:22:52.883 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:52.892 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.9886ms
2025-06-01 20:22:55.895 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:55.911 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.9421ms
2025-06-01 20:22:58.888 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:22:58.903 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.0338ms
2025-06-01 20:23:01.887 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:23:01.904 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.1141ms
2025-06-01 20:23:04.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:23:04.919 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.2252ms
2025-06-01 20:23:07.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:23:07.917 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.9994ms
2025-06-01 20:23:09.937 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:23:09.976 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 39.7971ms
2025-06-01 20:23:11.990 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:23:12.012 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.9322ms
2025-06-01 20:23:14.022 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:23:14.041 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.282ms
2025-06-01 20:23:16.050 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:23:16.068 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.2181ms
2025-06-01 20:23:18.102 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:23:18.103 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/recurring - null null
2025-06-01 20:23:18.132 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 29.5914ms
2025-06-01 20:23:18.265 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/recurring - 200 null text/html 162.7908ms
2025-06-01 20:23:18.317 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 20:23:18.317 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-01 20:23:18.323 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 6.1851ms
2025-06-01 20:23:18.345 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 27.5315ms
2025-06-01 20:23:20.392 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:20.406 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.6713ms
2025-06-01 20:23:22.418 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:22.432 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.8325ms
2025-06-01 20:23:24.456 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:24.482 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.2048ms
2025-06-01 20:23:26.497 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:26.533 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 35.497ms
2025-06-01 20:23:28.550 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:28.571 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.8212ms
2025-06-01 20:23:30.580 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:30.593 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.7242ms
2025-06-01 20:23:32.601 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:32.613 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.0514ms
2025-06-01 20:23:34.628 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:34.642 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.5137ms
2025-06-01 20:23:36.648 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:36.660 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.0604ms
2025-06-01 20:23:38.672 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:38.683 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.1831ms
2025-06-01 20:23:40.687 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:40.704 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.51ms
2025-06-01 20:23:42.715 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:42.733 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.3655ms
2025-06-01 20:23:44.740 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:44.749 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.7322ms
2025-06-01 20:23:46.757 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:46.773 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.776ms
2025-06-01 20:23:48.785 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:48.797 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.9794ms
2025-06-01 20:23:50.803 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:50.815 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.7672ms
2025-06-01 20:23:52.835 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:52.850 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.0068ms
2025-06-01 20:23:54.873 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:54.891 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.792ms
2025-06-01 20:23:56.910 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:56.928 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.2407ms
2025-06-01 20:23:58.933 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:23:58.945 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.5954ms
2025-06-01 20:24:00.957 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:00.976 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.0341ms
2025-06-01 20:24:02.988 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:03.004 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.4904ms
2025-06-01 20:24:05.022 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:05.033 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.245ms
2025-06-01 20:24:07.046 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:07.057 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.3491ms
2025-06-01 20:24:09.071 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:09.089 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.6513ms
2025-06-01 20:24:11.105 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:11.150 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 44.1932ms
2025-06-01 20:24:13.162 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:13.181 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.3277ms
2025-06-01 20:24:15.188 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:15.198 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.6052ms
2025-06-01 20:24:16.330 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/recurring/trigger - application/x-www-form-urlencoded; charset=UTF-8 36
2025-06-01 20:24:16.567 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/recurring/trigger - 204 null null 236.6325ms
2025-06-01 20:24:16.599 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/recurring - null null
2025-06-01 20:24:16.676 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/recurring - 200 null text/html 77.1932ms
2025-06-01 20:24:16.700 +04:00 [INF] Generating processing statistics
2025-06-01 20:24:16.734 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-01 20:24:16.739 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 20:24:16.744 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 5.2607ms
2025-06-01 20:24:16.746 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 11.9153ms
2025-06-01 20:24:18.795 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:18.818 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.9279ms
2025-06-01 20:24:20.837 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:20.848 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.2432ms
2025-06-01 20:24:22.861 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:22.875 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.4434ms
2025-06-01 20:24:24.895 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:24.912 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.8761ms
2025-06-01 20:24:26.930 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:26.948 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.0243ms
2025-06-01 20:24:28.962 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:28.980 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.3033ms
2025-06-01 20:24:30.985 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:30.995 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.7496ms
2025-06-01 20:24:33.012 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:33.022 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.9199ms
2025-06-01 20:24:35.033 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:35.050 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.3023ms
2025-06-01 20:24:37.071 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:37.095 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.899ms
2025-06-01 20:24:39.103 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:39.127 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.5647ms
2025-06-01 20:24:41.147 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:41.158 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.0863ms
2025-06-01 20:24:43.166 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:43.183 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.8045ms
2025-06-01 20:24:45.206 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:45.236 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 30.5058ms
2025-06-01 20:24:47.246 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:47.272 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.6036ms
2025-06-01 20:24:49.284 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:49.295 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.894ms
2025-06-01 20:24:51.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:51.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.7253ms
2025-06-01 20:24:52.390 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/servers - null null
2025-06-01 20:24:52.475 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/servers - 200 null text/html 84.8591ms
2025-06-01 20:24:52.509 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-01 20:24:52.520 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 20:24:52.543 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 33.404ms
2025-06-01 20:24:52.544 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 24.5704ms
2025-06-01 20:24:54.582 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:54.599 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.3319ms
2025-06-01 20:24:56.608 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:56.634 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.0847ms
2025-06-01 20:24:58.658 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:24:58.682 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.4038ms
2025-06-01 20:25:00.703 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:25:00.721 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.5413ms
2025-06-01 20:25:02.738 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:25:02.758 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.8905ms
2025-06-01 20:25:04.765 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:25:04.774 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.3394ms
2025-06-01 20:25:06.780 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:25:06.789 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.115ms
2025-06-01 20:25:06.850 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/jobs/enqueued/processing - null null
2025-06-01 20:25:06.931 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/jobs/enqueued/processing - 200 null text/html 81.0222ms
2025-06-01 20:25:06.974 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-01 20:25:06.981 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 20:25:06.995 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 13.6323ms
2025-06-01 20:25:06.996 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 22.5778ms
2025-06-01 20:25:09.037 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-01 20:25:09.058 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.5959ms
2025-06-01 20:25:11.070 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-01 20:25:11.087 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.3593ms
2025-06-01 20:25:12.476 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/jobs/succeeded - null null
2025-06-01 20:25:12.579 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/jobs/succeeded - 200 null text/html 102.3969ms
2025-06-01 20:25:12.639 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-01 20:25:12.650 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 20:25:12.671 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 32.0163ms
2025-06-01 20:25:12.672 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 23.1276ms
2025-06-01 20:25:14.719 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-01 20:25:14.757 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 38.6162ms
2025-06-01 20:25:16.766 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-01 20:25:16.786 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.534ms
2025-06-01 20:25:18.187 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/jobs/details/1 - null null
2025-06-01 20:25:18.276 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/jobs/details/1 - 200 null text/html 89.0883ms
2025-06-01 20:25:18.325 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-01 20:25:18.328 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 20:25:18.343 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 14.9673ms
2025-06-01 20:25:18.345 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 19.9144ms
2025-06-01 20:25:20.375 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-01 20:25:20.391 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.5577ms
2025-06-01 20:25:22.400 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-01 20:25:22.412 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.6594ms
2025-06-01 20:25:24.428 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-01 20:25:24.440 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.5579ms
2025-06-01 20:25:26.450 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-01 20:25:26.465 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.9634ms
2025-06-01 20:25:28.481 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-01 20:25:28.503 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.3772ms
2025-06-01 20:25:30.512 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-01 20:25:30.526 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.5939ms
2025-06-01 20:25:32.534 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-01 20:25:32.545 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.8056ms
2025-06-01 20:25:34.558 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-01 20:25:34.582 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.3482ms
2025-06-01 20:25:36.260 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/ - null null
2025-06-01 20:25:36.297 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/ - 200 null text/html 37.3237ms
2025-06-01 20:25:36.379 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-01 20:25:36.384 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 20:25:36.399 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 14.656ms
2025-06-01 20:25:36.399 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 20.1937ms
2025-06-01 20:25:38.444 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:25:38.458 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.1921ms
2025-06-01 20:25:40.469 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:25:40.494 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.4158ms
2025-06-01 20:25:42.506 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:25:42.535 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 29.8336ms
2025-06-01 20:25:44.549 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:25:44.589 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 39.1986ms
2025-06-01 20:25:46.607 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:25:46.632 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.0288ms
2025-06-01 20:25:48.652 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:25:48.673 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.9784ms
2025-06-01 20:25:50.680 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:25:50.713 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 33.2905ms
2025-06-01 20:25:52.733 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:25:52.753 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.3762ms
2025-06-01 20:25:54.767 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:25:54.786 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.17ms
2025-06-01 20:25:56.947 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:25:56.975 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 28.474ms
2025-06-01 20:25:59.898 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:25:59.910 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.7714ms
2025-06-01 20:26:02.899 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:26:02.934 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 35.3524ms
2025-06-01 20:26:05.901 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:26:05.919 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.9943ms
2025-06-01 20:26:08.902 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:26:08.916 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.8236ms
2025-06-01 20:26:11.894 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:26:11.907 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.2627ms
2025-06-01 20:26:14.890 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:26:14.917 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 27.2283ms
2025-06-01 20:26:17.910 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:26:17.940 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 29.3468ms
2025-06-01 20:26:20.900 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:26:20.933 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 33.1163ms
2025-06-01 20:26:23.538 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:26:23.578 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 39.8667ms
2025-06-01 20:26:25.592 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 20:26:25.608 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.2628ms
2025-06-01 20:26:27.074 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/recurring - null null
2025-06-01 20:26:27.112 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/recurring - 200 null text/html 38.8092ms
2025-06-01 20:26:27.184 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-01 20:26:27.186 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 20:26:27.197 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 11.7009ms
2025-06-01 20:26:27.198 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 14.186ms
2025-06-01 20:26:29.254 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:29.270 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.6868ms
2025-06-01 20:26:31.278 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:31.298 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.8444ms
2025-06-01 20:26:33.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:33.340 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.3354ms
2025-06-01 20:26:35.349 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:35.365 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.8752ms
2025-06-01 20:26:37.381 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:37.402 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.3635ms
2025-06-01 20:26:39.420 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:39.440 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.1476ms
2025-06-01 20:26:41.451 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:41.472 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.8454ms
2025-06-01 20:26:43.478 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:43.490 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.4372ms
2025-06-01 20:26:45.502 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:45.521 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.747ms
2025-06-01 20:26:47.540 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:47.556 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.9934ms
2025-06-01 20:26:49.567 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:49.595 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 28.0112ms
2025-06-01 20:26:51.603 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:51.615 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.1849ms
2025-06-01 20:26:53.626 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:53.640 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.3395ms
2025-06-01 20:26:55.888 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:55.908 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.9705ms
2025-06-01 20:26:58.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:26:58.915 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.7723ms
2025-06-01 20:27:01.894 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:01.918 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.2289ms
2025-06-01 20:27:04.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:04.914 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.6409ms
2025-06-01 20:27:07.899 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:07.933 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 34.1922ms
2025-06-01 20:27:10.898 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:10.920 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.2339ms
2025-06-01 20:27:13.895 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:13.915 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.5661ms
2025-06-01 20:27:16.897 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:16.913 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.8066ms
2025-06-01 20:27:19.890 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:19.907 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.4483ms
2025-06-01 20:27:22.888 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:22.920 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 32.6903ms
2025-06-01 20:27:25.894 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:25.923 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 29.6594ms
2025-06-01 20:27:28.901 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:28.916 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.5361ms
2025-06-01 20:27:31.908 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:31.929 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.9214ms
2025-06-01 20:27:34.890 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:34.908 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.2839ms
2025-06-01 20:27:37.889 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:37.905 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.839ms
2025-06-01 20:27:40.894 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-01 20:27:40.914 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.899ms
