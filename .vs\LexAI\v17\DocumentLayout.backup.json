{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|solutionrelative:src\\services\\identity\\lexai.identity.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.api\\logs\\lexai-ai-assistant-20250530.log||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.api\\logs\\lexai-ai-assistant-20250530.log||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{1CFED82B-BB52-4162-98D4-463014013FF5}|src\\Services\\Identity\\LexAI.Identity.Application\\LexAI.Identity.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.application\\commands\\authenticationcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1CFED82B-BB52-4162-98D4-463014013FF5}|src\\Services\\Identity\\LexAI.Identity.Application\\LexAI.Identity.Application.csproj|solutionrelative:src\\services\\identity\\lexai.identity.application\\commands\\authenticationcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.infrastructure\\repositories\\userrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|solutionrelative:src\\services\\identity\\lexai.identity.infrastructure\\repositories\\userrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.infrastructure\\data\\identitydbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|solutionrelative:src\\services\\identity\\lexai.identity.infrastructure\\data\\identitydbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|solutionrelative:src\\services\\identity\\lexai.identity.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{843CDE3F-5014-4D54-8F86-7EE31FE3403B}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.domain\\valueobjects\\conversationcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{843CDE3F-5014-4D54-8F86-7EE31FE3403B}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.domain\\valueobjects\\conversationcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2D333B21-37FA-4201-A868-5A56261A570A}|src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\shared\\lexai.shared.domain\\common\\baseentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2D333B21-37FA-4201-A868-5A56261A570A}|src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj|solutionrelative:src\\shared\\lexai.shared.domain\\common\\baseentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2D333B21-37FA-4201-A868-5A56261A570A}|src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\shared\\lexai.shared.domain\\common\\auditableentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2D333B21-37FA-4201-A868-5A56261A570A}|src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj|solutionrelative:src\\shared\\lexai.shared.domain\\common\\auditableentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8A995A0E-8D75-4175-B267-341F64F27B12}|src\\Services\\Identity\\LexAI.Identity.Domain\\LexAI.Identity.Domain.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.domain\\entities\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8A995A0E-8D75-4175-B267-341F64F27B12}|src\\Services\\Identity\\LexAI.Identity.Domain\\LexAI.Identity.Domain.csproj|solutionrelative:src\\services\\identity\\lexai.identity.domain\\entities\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|solutionrelative:src\\services\\identity\\lexai.identity.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.json", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.json", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-29T22:17:50.75Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "lexai-ai-assistant-20250530.log", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\logs\\lexai-ai-assistant-20250530.log", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\logs\\lexai-ai-assistant-20250530.log", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\logs\\lexai-ai-assistant-20250530.log", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\logs\\lexai-ai-assistant-20250530.log", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-05-29T22:16:23.088Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\appsettings.json", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.API\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\appsettings.json", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-29T22:13:04.658Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.json", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.json", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-29T22:13:00.087Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Program.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Program.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Program.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-29T22:09:19.898Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "AuthenticationCommands.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\AuthenticationCommands.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\AuthenticationCommands.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\AuthenticationCommands.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\AuthenticationCommands.cs", "ViewState": "AgIAAGcAAAAAAAAAAAAAAHMAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-29T21:41:31.987Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "BaseEntity.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\Common\\BaseEntity.cs", "RelativeDocumentMoniker": "src\\Shared\\LexAI.Shared.Domain\\Common\\BaseEntity.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\Common\\BaseEntity.cs", "RelativeToolTip": "src\\Shared\\LexAI.Shared.Domain\\Common\\BaseEntity.cs", "ViewState": "AgIAACIAAAAAAAAAAAAkwDYAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-29T20:44:46.776Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "IdentityDbContext.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "ViewState": "AgIAABABAAAAAAAAAAAAwCQBAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-29T20:23:49.666Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ConversationContext.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\ValueObjects\\ConversationContext.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\ValueObjects\\ConversationContext.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\ValueObjects\\ConversationContext.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\ValueObjects\\ConversationContext.cs", "ViewState": "AgIAAJ4BAAAAAAAAAAAAAKQBAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-29T20:03:16.49Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "UserRepository.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Repositories\\UserRepository.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Repositories\\UserRepository.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Repositories\\UserRepository.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Repositories\\UserRepository.cs", "ViewState": "AgIAAKgAAAAAAAAAAAAqwL4AAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-29T19:57:20.539Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "AuditableEntity.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\Common\\AuditableEntity.cs", "RelativeDocumentMoniker": "src\\Shared\\LexAI.Shared.Domain\\Common\\AuditableEntity.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\Common\\AuditableEntity.cs", "RelativeToolTip": "src\\Shared\\LexAI.Shared.Domain\\Common\\AuditableEntity.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-29T19:48:38.92Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "User.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Domain\\Entities\\User.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Domain\\Entities\\User.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Domain\\Entities\\User.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Domain\\Entities\\User.cs", "ViewState": "AgIAAI4AAAAAAAAAAAAqwJ0AAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-29T19:46:47.302Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "AuthController.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.API\\Controllers\\AuthController.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\Controllers\\AuthController.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.API\\Controllers\\AuthController.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAAwDMAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-29T19:27:43.839Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\Program.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.API\\Program.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\Program.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.API\\Program.cs", "ViewState": "AgIAAKMAAAAAAAAAAAAhwK8AAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-29T19:22:07.07Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Program.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Program.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Program.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Program.cs", "ViewState": "AgIAANoAAAAAAAAAAAAAAOAAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-29T18:29:03.125Z", "EditorCaption": ""}]}]}]}