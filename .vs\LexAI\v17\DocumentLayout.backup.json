{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2B3C4D5-E6F7-8901-1234-567890123456}|src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\shared\\lexai.shared.infrastructure\\configuration\\jwtsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2B3C4D5-E6F7-8901-1234-567890123456}|src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj|solutionrelative:src\\shared\\lexai.shared.infrastructure\\configuration\\jwtsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|solutionrelative:src\\services\\identity\\lexai.identity.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|solutionrelative:src\\services\\identity\\lexai.identity.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\ApiGateway\\LexAI.ApiGateway.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\apigateway\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\ApiGateway\\LexAI.ApiGateway.csproj|solutionrelative:src\\apigateway\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.api\\logs\\lexai-identity-20250527.log||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|solutionrelative:src\\services\\identity\\lexai.identity.api\\logs\\lexai-identity-20250527.log||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\ApiGateway\\LexAI.ApiGateway.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\apigateway\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\ApiGateway\\LexAI.ApiGateway.csproj|solutionrelative:src\\apigateway\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.infrastructure\\data\\designtimedbcontextfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|solutionrelative:src\\services\\identity\\lexai.identity.infrastructure\\data\\designtimedbcontextfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\ApiGateway\\LexAI.ApiGateway.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\apigateway\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\ApiGateway\\LexAI.ApiGateway.csproj|solutionrelative:src\\apigateway\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.infrastructure\\data\\identitydbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|solutionrelative:src\\services\\identity\\lexai.identity.infrastructure\\data\\identitydbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1CFED82B-BB52-4162-98D4-463014013FF5}|src\\Services\\Identity\\LexAI.Identity.Application\\LexAI.Identity.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.application\\commands\\changepasswordcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1CFED82B-BB52-4162-98D4-463014013FF5}|src\\Services\\Identity\\LexAI.Identity.Application\\LexAI.Identity.Application.csproj|solutionrelative:src\\services\\identity\\lexai.identity.application\\commands\\changepasswordcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|solutionrelative:src\\services\\identity\\lexai.identity.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "JwtSettings.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\Configuration\\JwtSettings.cs", "RelativeDocumentMoniker": "src\\Shared\\LexAI.Shared.Infrastructure\\Configuration\\JwtSettings.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\Configuration\\JwtSettings.cs", "RelativeToolTip": "src\\Shared\\LexAI.Shared.Infrastructure\\Configuration\\JwtSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAoAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T20:13:48.818Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\appsettings.json", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.API\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\appsettings.json", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-27T19:59:49.144Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "lexai-identity-20250527.log", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\logs\\lexai-identity-20250527.log", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.API\\logs\\lexai-identity-20250527.log", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\logs\\lexai-identity-20250527.log", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.API\\logs\\lexai-identity-20250527.log", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAADfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-05-27T19:54:56.884Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\ApiGateway\\Program.cs", "RelativeDocumentMoniker": "src\\ApiGateway\\Program.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\ApiGateway\\Program.cs", "RelativeToolTip": "src\\ApiGateway\\Program.cs", "ViewState": "AgIAAKEAAAAAAAAAAAAQwK0AAABoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T19:35:56.009Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\ApiGateway\\appsettings.json", "RelativeDocumentMoniker": "src\\ApiGateway\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\ApiGateway\\appsettings.json", "RelativeToolTip": "src\\ApiGateway\\appsettings.json", "ViewState": "AgIAAAkAAAAAAAAAAAAAABAAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-27T19:35:45.423Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\ApiGateway\\appsettings.Development.json", "RelativeDocumentMoniker": "src\\ApiGateway\\appsettings.Development.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\ApiGateway\\appsettings.Development.json", "RelativeToolTip": "src\\ApiGateway\\appsettings.Development.json", "ViewState": "AgIAACQAAAAAAAAAAAAAABEAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-27T19:35:03.635Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\Program.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.API\\Program.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\Program.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.API\\Program.cs", "ViewState": "AgIAABkAAAAAAAAAAAAAACQAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T19:30:54.901Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "IdentityDbContext.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "ViewState": "AgIAADgAAAAAAAAAAAAswAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T18:51:20.558Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ChangePasswordCommand.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\ChangePasswordCommand.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\ChangePasswordCommand.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\ChangePasswordCommand.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\ChangePasswordCommand.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAAwBUAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T18:48:23.847Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "AuthController.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.API\\Controllers\\AuthController.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\Controllers\\AuthController.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.API\\Controllers\\AuthController.cs", "ViewState": "AgIAAPwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T18:47:57.648Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "DesignTimeDbContextFactory.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\DesignTimeDbContextFactory.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\DesignTimeDbContextFactory.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\DesignTimeDbContextFactory.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\DesignTimeDbContextFactory.cs", "ViewState": "AgIAABMAAAAAAAAAAAAWwBgAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T18:46:06.989Z", "EditorCaption": ""}]}]}]}