using AutoFixture;
using FluentAssertions;
using LexAI.Identity.Application.Commands;
using LexAI.Identity.Application.Interfaces;
using LexAI.Identity.Domain.Entities;
using LexAI.Shared.Domain.Enums;
using LexAI.Shared.Domain.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace LexAI.Identity.UnitTests.Application.Commands;

/// <summary>
/// Unit tests for CreateUserCommandHandler
/// </summary>
public class CreateUserCommandHandlerTests
{
    private readonly Mock<IUserRepository> _userRepositoryMock;
    private readonly Mock<IPasswordService> _passwordServiceMock;
    private readonly Mock<ILogger<CreateUserCommandHandler>> _loggerMock;
    private readonly CreateUserCommandHandler _handler;
    private readonly Fixture _fixture;

    public CreateUserCommandHandlerTests()
    {
        _userRepositoryMock = new Mock<IUserRepository>();
        _passwordServiceMock = new Mock<IPasswordService>();
        _loggerMock = new Mock<ILogger<CreateUserCommandHandler>>();
        _handler = new CreateUserCommandHandler(_userRepositoryMock.Object, _passwordServiceMock.Object, _loggerMock.Object);
        _fixture = new Fixture();
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateUser()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Password = "SecurePassword123!",
            Role = UserRole.Lawyer,
            CreatedBy = "admin"
        };

        _userRepositoryMock
            .Setup(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        _passwordServiceMock
            .Setup(x => x.ValidatePasswordStrength(command.Password))
            .Returns(new PasswordValidationResult { IsValid = true, Score = 80 });

        var createdUser = User.Create(command.Email, command.FirstName, command.LastName, command.Role, command.CreatedBy);
        _userRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(createdUser);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Email.Should().Be(command.Email);
        result.FirstName.Should().Be(command.FirstName);
        result.LastName.Should().Be(command.LastName);
        result.Role.Should().Be(command.Role);
        result.IsActive.Should().BeTrue();
        result.IsEmailVerified.Should().BeFalse();

        _userRepositoryMock.Verify(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()), Times.Once);
        _passwordServiceMock.Verify(x => x.ValidatePasswordStrength(command.Password), Times.Once);
        _userRepositoryMock.Verify(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithExistingEmail_ShouldThrowEntityAlreadyExistsException()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Password = "SecurePassword123!",
            Role = UserRole.Lawyer,
            CreatedBy = "admin"
        };

        var existingUser = User.Create(command.Email, "Jane", "Smith", UserRole.Lawyer, "admin");
        _userRepositoryMock
            .Setup(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingUser);

        // Act & Assert
        var action = async () => await _handler.Handle(command, CancellationToken.None);
        await action.Should().ThrowAsync<EntityAlreadyExistsException>()
            .WithMessage("*already exists*");

        _userRepositoryMock.Verify(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()), Times.Once);
        _passwordServiceMock.Verify(x => x.ValidatePasswordStrength(It.IsAny<string>()), Times.Never);
        _userRepositoryMock.Verify(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WithWeakPassword_ShouldThrowInvalidDataException()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Password = "weak",
            Role = UserRole.Lawyer,
            CreatedBy = "admin"
        };

        _userRepositoryMock
            .Setup(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        _passwordServiceMock
            .Setup(x => x.ValidatePasswordStrength(command.Password))
            .Returns(new PasswordValidationResult 
            { 
                IsValid = false, 
                Score = 20,
                Errors = new List<string> { "Password is too weak" }
            });

        // Act & Assert
        var action = async () => await _handler.Handle(command, CancellationToken.None);
        await action.Should().ThrowAsync<Shared.Domain.Exceptions.InvalidDataException>()
            .WithMessage("*Password validation failed*");

        _userRepositoryMock.Verify(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()), Times.Once);
        _passwordServiceMock.Verify(x => x.ValidatePasswordStrength(command.Password), Times.Once);
        _userRepositoryMock.Verify(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WithPhoneNumber_ShouldCreateUserWithPhoneNumber()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Password = "SecurePassword123!",
            PhoneNumber = "+33123456789",
            Role = UserRole.Lawyer,
            CreatedBy = "admin"
        };

        _userRepositoryMock
            .Setup(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        _passwordServiceMock
            .Setup(x => x.ValidatePasswordStrength(command.Password))
            .Returns(new PasswordValidationResult { IsValid = true, Score = 80 });

        var createdUser = User.Create(command.Email, command.FirstName, command.LastName, command.Role, command.CreatedBy);
        createdUser.UpdateProfile(command.FirstName, command.LastName, command.PhoneNumber, command.CreatedBy);
        
        _userRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(createdUser);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PhoneNumber.Should().Be(command.PhoneNumber);
    }

    [Theory]
    [InlineData(UserRole.Administrator)]
    [InlineData(UserRole.SeniorLawyer)]
    [InlineData(UserRole.Lawyer)]
    [InlineData(UserRole.LegalAssistant)]
    [InlineData(UserRole.Client)]
    public async Task Handle_WithDifferentRoles_ShouldCreateUserWithCorrectRole(UserRole role)
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Password = "SecurePassword123!",
            Role = role,
            CreatedBy = "admin"
        };

        _userRepositoryMock
            .Setup(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        _passwordServiceMock
            .Setup(x => x.ValidatePasswordStrength(command.Password))
            .Returns(new PasswordValidationResult { IsValid = true, Score = 80 });

        var createdUser = User.Create(command.Email, command.FirstName, command.LastName, command.Role, command.CreatedBy);
        _userRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(createdUser);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Role.Should().Be(role);
    }

    [Fact]
    public async Task Handle_WhenRepositoryThrows_ShouldPropagateException()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Password = "SecurePassword123!",
            Role = UserRole.Lawyer,
            CreatedBy = "admin"
        };

        _userRepositoryMock
            .Setup(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        _passwordServiceMock
            .Setup(x => x.ValidatePasswordStrength(command.Password))
            .Returns(new PasswordValidationResult { IsValid = true, Score = 80 });

        _userRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act & Assert
        var action = async () => await _handler.Handle(command, CancellationToken.None);
        await action.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Database error");
    }
}
