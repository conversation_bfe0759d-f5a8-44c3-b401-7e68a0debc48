using LexAI.DataPreprocessing.Domain.ValueObjects;
using LexAI.Shared.Domain.Common;

namespace LexAI.DataPreprocessing.Domain.Entities;

/// <summary>
/// Represents a document in the preprocessing pipeline
/// </summary>
public class Document : BaseEntity
{
    /// <summary>
    /// Original file name
    /// </summary>
    public string FileName { get; private set; } = string.Empty;

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long FileSize { get; private set; }

    /// <summary>
    /// MIME type of the file
    /// </summary>
    public string MimeType { get; private set; } = string.Empty;

    /// <summary>
    /// File hash for deduplication
    /// </summary>
    public string FileHash { get; private set; } = string.Empty;

    /// <summary>
    /// Storage path or URL
    /// </summary>
    public string StoragePath { get; private set; } = string.Empty;

    /// <summary>
    /// Document processing status
    /// </summary>
    public DocumentStatus Status { get; private set; }

    /// <summary>
    /// Detected legal domain
    /// </summary>
    public LegalDomain? DetectedDomain { get; private set; }

    /// <summary>
    /// Classification confidence score
    /// </summary>
    public double? ClassificationConfidence { get; private set; }

    /// <summary>
    /// Extracted text content
    /// </summary>
    public string? ExtractedText { get; private set; }

    /// <summary>
    /// Document metadata
    /// </summary>
    public DocumentMetadata Metadata { get; private set; } = null!;

    /// <summary>
    /// Processing pipeline steps
    /// </summary>
    public List<ProcessingStep> ProcessingSteps { get; private set; } = new();

    /// <summary>
    /// Document chunks created during processing
    /// </summary>
    public List<DocumentChunk> Chunks { get; private set; } = new();

    /// <summary>
    /// Processing errors if any
    /// </summary>
    public List<ProcessingError> Errors { get; private set; } = new();

    /// <summary>
    /// Total processing time
    /// </summary>
    public TimeSpan? ProcessingTime { get; private set; }

    /// <summary>
    /// Number of chunks created
    /// </summary>
    public int ChunkCount { get; private set; }

    /// <summary>
    /// Total tokens in the document
    /// </summary>
    public int TotalTokens { get; private set; }

    /// <summary>
    /// Estimated processing cost
    /// </summary>
    public decimal EstimatedCost { get; private set; }

    /// <summary>
    /// Whether the document is vectorized
    /// </summary>
    public bool IsVectorized { get; private set; }

    /// <summary>
    /// Vector database where chunks are stored
    /// </summary>
    public string? VectorDatabase { get; private set; }

    /// <summary>
    /// Collection/index name in vector database
    /// </summary>
    public string? VectorCollection { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private Document() { }

    /// <summary>
    /// Creates a new document for processing
    /// </summary>
    /// <param name="fileName">Original file name</param>
    /// <param name="fileSize">File size in bytes</param>
    /// <param name="mimeType">MIME type</param>
    /// <param name="fileHash">File hash</param>
    /// <param name="storagePath">Storage path</param>
    /// <param name="uploadedBy">User who uploaded the document</param>
    /// <returns>New document instance</returns>
    public static Document Create(
        string fileName,
        long fileSize,
        string mimeType,
        string fileHash,
        string storagePath,
        string uploadedBy)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("File name cannot be empty", nameof(fileName));

        if (fileSize <= 0)
            throw new ArgumentException("File size must be positive", nameof(fileSize));

        if (string.IsNullOrWhiteSpace(mimeType))
            throw new ArgumentException("MIME type cannot be empty", nameof(mimeType));

        if (string.IsNullOrWhiteSpace(fileHash))
            throw new ArgumentException("File hash cannot be empty", nameof(fileHash));

        if (string.IsNullOrWhiteSpace(storagePath))
            throw new ArgumentException("Storage path cannot be empty", nameof(storagePath));

        var document = new Document
        {
            Id = Guid.NewGuid(),
            FileName = fileName.Trim(),
            FileSize = fileSize,
            MimeType = mimeType.Trim().ToLowerInvariant(),
            FileHash = fileHash.Trim(),
            StoragePath = storagePath.Trim(),
            Status = DocumentStatus.Uploaded,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = uploadedBy,
            ChunkCount = 0,
            TotalTokens = 0,
            EstimatedCost = 0m,
            IsVectorized = false
        };

        document.Metadata = DocumentMetadata.Create(document.Id, fileName, fileSize, mimeType);

        // Add initial processing step
        document.AddProcessingStep("Upload", "Document uploaded successfully", true);

        return document;
    }

    /// <summary>
    /// Starts the extraction process
    /// </summary>
    /// <param name="agentName">Name of the extraction agent</param>
    public void StartExtraction(string agentName)
    {
        if (Status != DocumentStatus.Uploaded)
            throw new InvalidOperationException($"Cannot start extraction. Current status: {Status}");

        Status = DocumentStatus.Extracting;
        UpdatedAt = DateTime.UtcNow;

        AddProcessingStep("Extraction", $"Text extraction started by {agentName}", null);
    }

    /// <summary>
    /// Completes the extraction process
    /// </summary>
    /// <param name="extractedText">Extracted text content</param>
    /// <param name="agentName">Name of the extraction agent</param>
    /// <param name="extractionTime">Time taken for extraction</param>
    public void CompleteExtraction(string extractedText, string agentName, TimeSpan extractionTime)
    {
        if (Status != DocumentStatus.Extracting)
            throw new InvalidOperationException($"Cannot complete extraction. Current status: {Status}");

        if (string.IsNullOrWhiteSpace(extractedText))
            throw new ArgumentException("Extracted text cannot be empty", nameof(extractedText));

        ExtractedText = extractedText.Trim();
        Status = DocumentStatus.Extracted;
        UpdatedAt = DateTime.UtcNow;

        // Update metadata with text statistics
        Metadata = Metadata.WithTextStatistics(ExtractedText);
        TotalTokens = EstimateTokenCount(ExtractedText);

        AddProcessingStep("Extraction", $"Text extraction completed by {agentName} in {extractionTime.TotalSeconds:F2}s", true);
    }

    /// <summary>
    /// Starts the classification process
    /// </summary>
    /// <param name="agentName">Name of the classification agent</param>
    public void StartClassification(string agentName)
    {
        if (Status != DocumentStatus.Extracted)
            throw new InvalidOperationException($"Cannot start classification. Current status: {Status}");

        Status = DocumentStatus.Classifying;
        UpdatedAt = DateTime.UtcNow;

        AddProcessingStep("Classification", $"Domain classification started by {agentName}", null);
    }

    /// <summary>
    /// Completes the classification process
    /// </summary>
    /// <param name="domain">Detected legal domain</param>
    /// <param name="confidence">Classification confidence</param>
    /// <param name="agentName">Name of the classification agent</param>
    /// <param name="classificationTime">Time taken for classification</param>
    public void CompleteClassification(LegalDomain domain, double confidence, string agentName, TimeSpan classificationTime)
    {
        if (Status != DocumentStatus.Classifying)
            throw new InvalidOperationException($"Cannot complete classification. Current status: {Status}");

        if (confidence < 0.0 || confidence > 1.0)
            throw new ArgumentException("Confidence must be between 0 and 1", nameof(confidence));

        DetectedDomain = domain;
        ClassificationConfidence = confidence;
        Status = DocumentStatus.Classified;
        UpdatedAt = DateTime.UtcNow;

        AddProcessingStep("Classification",
            $"Domain classification completed by {agentName}: {domain} (confidence: {confidence:F2}) in {classificationTime.TotalSeconds:F2}s",
            true);
    }

    /// <summary>
    /// Starts the chunking process
    /// </summary>
    /// <param name="agentName">Name of the chunking agent</param>
    public void StartChunking(string agentName)
    {
        if (Status != DocumentStatus.Classified)
            throw new InvalidOperationException($"Cannot start chunking. Current status: {Status}");

        Status = DocumentStatus.Chunking;
        UpdatedAt = DateTime.UtcNow;

        AddProcessingStep("Chunking", $"Document chunking started by {agentName}", null);
    }

    /// <summary>
    /// Adds a chunk to the document
    /// </summary>
    /// <param name="chunk">Document chunk to add</param>
    public void AddChunk(DocumentChunk chunk)
    {
        if (chunk == null)
            throw new ArgumentNullException(nameof(chunk));

        if (chunk.DocumentId != Id)
            throw new ArgumentException("Chunk document ID does not match", nameof(chunk));

        Chunks.Add(chunk);
        ChunkCount = Chunks.Count;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Completes the chunking process
    /// </summary>
    /// <param name="agentName">Name of the chunking agent</param>
    /// <param name="chunkingTime">Time taken for chunking</param>
    public void CompleteChunking(string agentName, TimeSpan chunkingTime)
    {
        if (Status != DocumentStatus.Chunking)
            throw new InvalidOperationException($"Cannot complete chunking. Current status: {Status}");

        if (Chunks.Count == 0)
            throw new InvalidOperationException("Cannot complete chunking without any chunks");

        Status = DocumentStatus.Chunked;
        UpdatedAt = DateTime.UtcNow;

        AddProcessingStep("Chunking",
            $"Document chunking completed by {agentName}: {ChunkCount} chunks created in {chunkingTime.TotalSeconds:F2}s",
            true);
    }

    /// <summary>
    /// Starts the vectorization process
    /// </summary>
    /// <param name="agentName">Name of the vectorization agent</param>
    public void StartVectorization(string agentName)
    {
        if (Status != DocumentStatus.Chunked)
            throw new InvalidOperationException($"Cannot start vectorization. Current status: {Status}");

        Status = DocumentStatus.Vectorizing;
        UpdatedAt = DateTime.UtcNow;

        AddProcessingStep("Vectorization", $"Document vectorization started by {agentName}", null);
    }

    /// <summary>
    /// Completes the vectorization process
    /// </summary>
    /// <param name="vectorDatabase">Vector database name</param>
    /// <param name="vectorCollection">Vector collection name</param>
    /// <param name="agentName">Name of the vectorization agent</param>
    /// <param name="vectorizationTime">Time taken for vectorization</param>
    /// <param name="estimatedCost">Estimated cost for vectorization</param>
    public void CompleteVectorization(
        string vectorDatabase,
        string vectorCollection,
        string agentName,
        TimeSpan vectorizationTime,
        decimal estimatedCost)
    {
        if (Status != DocumentStatus.Vectorizing)
            throw new InvalidOperationException($"Cannot complete vectorization. Current status: {Status}");

        VectorDatabase = vectorDatabase?.Trim();
        VectorCollection = vectorCollection?.Trim();
        IsVectorized = true;
        EstimatedCost = estimatedCost;
        Status = DocumentStatus.Completed;
        UpdatedAt = DateTime.UtcNow;

        // Calculate total processing time
        var firstStep = ProcessingSteps.FirstOrDefault();
        if (firstStep != null)
        {
            ProcessingTime = DateTime.UtcNow - firstStep.StartedAt;
        }

        AddProcessingStep("Vectorization",
            $"Document vectorization completed by {agentName}: stored in {vectorDatabase}/{vectorCollection} in {vectorizationTime.TotalSeconds:F2}s (cost: ${estimatedCost:F4})",
            true);
    }

    /// <summary>
    /// Marks the document as failed
    /// </summary>
    /// <param name="error">Processing error</param>
    /// <param name="agentName">Name of the agent that failed</param>
    public void MarkAsFailed(ProcessingError error, string agentName)
    {
        if (error == null)
            throw new ArgumentNullException(nameof(error));

        Status = DocumentStatus.Failed;
        UpdatedAt = DateTime.UtcNow;

        Errors.Add(error);

        AddProcessingStep("Error", $"Processing failed in {agentName}: {error.ErrorMessage}", false);
    }

    /// <summary>
    /// Retries processing from a specific step
    /// </summary>
    /// <param name="fromStep">Step to retry from</param>
    /// <param name="retriedBy">User who initiated the retry</param>
    public void RetryProcessing(string fromStep, string retriedBy)
    {
        if (Status != DocumentStatus.Failed)
            throw new InvalidOperationException($"Cannot retry processing. Current status: {Status}");

        // Reset status based on retry step
        Status = fromStep.ToLowerInvariant() switch
        {
            "extraction" => DocumentStatus.Uploaded,
            "classification" => DocumentStatus.Extracted,
            "chunking" => DocumentStatus.Classified,
            "vectorization" => DocumentStatus.Chunked,
            _ => DocumentStatus.Uploaded
        };

        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = retriedBy;

        // Clear errors
        Errors.Clear();

        AddProcessingStep("Retry", $"Processing retry initiated from {fromStep} by {retriedBy}", true);
    }

    /// <summary>
    /// Gets the current processing step
    /// </summary>
    /// <returns>Current processing step</returns>
    public ProcessingStep? GetCurrentStep()
    {
        return ProcessingSteps.LastOrDefault();
    }

    /// <summary>
    /// Checks if the document is ready for a specific step
    /// </summary>
    /// <param name="step">Step to check</param>
    /// <returns>True if ready for the step</returns>
    public bool IsReadyForStep(string step)
    {
        return step.ToLowerInvariant() switch
        {
            "extraction" => Status == DocumentStatus.Uploaded,
            "classification" => Status == DocumentStatus.Extracted,
            "chunking" => Status == DocumentStatus.Classified,
            "vectorization" => Status == DocumentStatus.Chunked,
            _ => false
        };
    }

    /// <summary>
    /// Gets processing statistics
    /// </summary>
    /// <returns>Processing statistics</returns>
    public ProcessingStatistics GetStatistics()
    {
        return new ProcessingStatistics
        {
            DocumentId = Id,
            FileName = FileName,
            FileSize = FileSize,
            Status = Status,
            ChunkCount = ChunkCount,
            TotalTokens = TotalTokens,
            ProcessingTime = ProcessingTime,
            EstimatedCost = EstimatedCost,
            StepCount = ProcessingSteps.Count,
            ErrorCount = Errors.Count,
            IsVectorized = IsVectorized
        };
    }

    private void AddProcessingStep(string stepName, string description, bool? success)
    {
        var step = new ProcessingStep
        {
            DocumentId = Id,
            StepName = stepName,
            AgentName = "System",
            StartedAt = DateTime.UtcNow,
            CompletedAt = DateTime.UtcNow,
            IsSuccessful = success ?? true,
            ErrorMessage = success == false ? description : null,
            Metadata = new Dictionary<string, object>
            {
                ["description"] = description
            }
        };
        ProcessingSteps.Add(step);
    }

    private static int EstimateTokenCount(string text)
    {
        // Simple estimation: ~4 characters per token
        return Math.Max(1, text.Length / 4);
    }
}
