using LexAI.Shared.Domain.Common;

namespace LexAI.AIAssistant.Domain.ValueObjects;

/// <summary>
/// Represents the context and configuration for a conversation
/// </summary>
public class ConversationContext : ValueObject
{
    /// <summary>
    /// AI model to use for this conversation
    /// </summary>
    public AIModelType ModelType { get; private set; }

    /// <summary>
    /// Conversation mode
    /// </summary>
    public ConversationMode Mode { get; private set; }

    /// <summary>
    /// System prompt for the AI
    /// </summary>
    public string SystemPrompt { get; private set; }

    /// <summary>
    /// Maximum tokens per response
    /// </summary>
    public int MaxTokens { get; private set; }

    /// <summary>
    /// Temperature for AI responses (0-1)
    /// </summary>
    public double Temperature { get; private set; }

    /// <summary>
    /// Whether to include legal research in responses
    /// </summary>
    public bool IncludeLegalResearch { get; private set; }

    /// <summary>
    /// Whether to include citations in responses
    /// </summary>
    public bool IncludeCitations { get; private set; }

    /// <summary>
    /// Preferred language for responses
    /// </summary>
    public string Language { get; private set; }

    /// <summary>
    /// User's legal jurisdiction
    /// </summary>
    public string? Jurisdiction { get; private set; }

    /// <summary>
    /// User's role/profession
    /// </summary>
    public string? UserRole { get; private set; }

    /// <summary>
    /// Conversation preferences
    /// </summary>
    public Dictionary<string, object> Preferences { get; private set; }

    /// <summary>
    /// Context creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private ConversationContext() 
    {
        SystemPrompt = string.Empty;
        Language = string.Empty;
        Preferences = new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates a new conversation context
    /// </summary>
    /// <param name="modelType">AI model type</param>
    /// <param name="mode">Conversation mode</param>
    /// <param name="systemPrompt">System prompt</param>
    /// <param name="maxTokens">Maximum tokens</param>
    /// <param name="temperature">Temperature</param>
    /// <param name="includeLegalResearch">Include legal research</param>
    /// <param name="includeCitations">Include citations</param>
    /// <param name="language">Language</param>
    /// <param name="jurisdiction">Jurisdiction</param>
    /// <param name="userRole">User role</param>
    /// <returns>New conversation context</returns>
    public static ConversationContext Create(
        AIModelType modelType = AIModelType.GPT4Turbo,
        ConversationMode mode = ConversationMode.Standard,
        string? systemPrompt = null,
        int maxTokens = 4000,
        double temperature = 0.7,
        bool includeLegalResearch = true,
        bool includeCitations = true,
        string language = "fr",
        string? jurisdiction = null,
        string? userRole = null)
    {
        if (maxTokens <= 0 || maxTokens > 32000)
            throw new ArgumentException("MaxTokens must be between 1 and 32000", nameof(maxTokens));

        if (temperature < 0.0 || temperature > 1.0)
            throw new ArgumentException("Temperature must be between 0 and 1", nameof(temperature));

        if (string.IsNullOrWhiteSpace(language))
            throw new ArgumentException("Language cannot be empty", nameof(language));

        return new ConversationContext
        {
            ModelType = modelType,
            Mode = mode,
            SystemPrompt = systemPrompt ?? GetDefaultSystemPrompt(mode, language),
            MaxTokens = maxTokens,
            Temperature = temperature,
            IncludeLegalResearch = includeLegalResearch,
            IncludeCitations = includeCitations,
            Language = language.Trim().ToLowerInvariant(),
            Jurisdiction = jurisdiction?.Trim(),
            UserRole = userRole?.Trim(),
            Preferences = new Dictionary<string, object>(),
            CreatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Creates default conversation context
    /// </summary>
    /// <returns>Default conversation context</returns>
    public static ConversationContext CreateDefault()
    {
        return Create();
    }

    /// <summary>
    /// Creates context for legal research mode
    /// </summary>
    /// <param name="jurisdiction">Legal jurisdiction</param>
    /// <param name="userRole">User role</param>
    /// <returns>Research mode context</returns>
    public static ConversationContext CreateForResearch(string? jurisdiction = null, string? userRole = null)
    {
        return Create(
            modelType: AIModelType.GPT4Turbo,
            mode: ConversationMode.Research,
            maxTokens: 6000,
            temperature: 0.3,
            includeLegalResearch: true,
            includeCitations: true,
            jurisdiction: jurisdiction,
            userRole: userRole);
    }

    /// <summary>
    /// Creates context for document analysis mode
    /// </summary>
    /// <param name="jurisdiction">Legal jurisdiction</param>
    /// <param name="userRole">User role</param>
    /// <returns>Document analysis mode context</returns>
    public static ConversationContext CreateForDocumentAnalysis(string? jurisdiction = null, string? userRole = null)
    {
        return Create(
            modelType: AIModelType.GPT4Turbo,
            mode: ConversationMode.DocumentAnalysis,
            maxTokens: 8000,
            temperature: 0.2,
            includeLegalResearch: true,
            includeCitations: true,
            jurisdiction: jurisdiction,
            userRole: userRole);
    }

    /// <summary>
    /// Creates context for legal advice mode
    /// </summary>
    /// <param name="jurisdiction">Legal jurisdiction</param>
    /// <param name="userRole">User role</param>
    /// <returns>Legal advice mode context</returns>
    public static ConversationContext CreateForLegalAdvice(string? jurisdiction = null, string? userRole = null)
    {
        return Create(
            modelType: AIModelType.GPT4Turbo,
            mode: ConversationMode.LegalAdvice,
            maxTokens: 5000,
            temperature: 0.4,
            includeLegalResearch: true,
            includeCitations: true,
            jurisdiction: jurisdiction,
            userRole: userRole);
    }

    /// <summary>
    /// Updates the system prompt
    /// </summary>
    /// <param name="systemPrompt">New system prompt</param>
    public ConversationContext WithSystemPrompt(string systemPrompt)
    {
        if (string.IsNullOrWhiteSpace(systemPrompt))
            throw new ArgumentException("System prompt cannot be empty", nameof(systemPrompt));

        return new ConversationContext
        {
            ModelType = ModelType,
            Mode = Mode,
            SystemPrompt = systemPrompt.Trim(),
            MaxTokens = MaxTokens,
            Temperature = Temperature,
            IncludeLegalResearch = IncludeLegalResearch,
            IncludeCitations = IncludeCitations,
            Language = Language,
            Jurisdiction = Jurisdiction,
            UserRole = UserRole,
            Preferences = new Dictionary<string, object>(Preferences),
            CreatedAt = CreatedAt
        };
    }

    /// <summary>
    /// Updates the model type
    /// </summary>
    /// <param name="modelType">New model type</param>
    public ConversationContext WithModelType(AIModelType modelType)
    {
        return new ConversationContext
        {
            ModelType = modelType,
            Mode = Mode,
            SystemPrompt = SystemPrompt,
            MaxTokens = MaxTokens,
            Temperature = Temperature,
            IncludeLegalResearch = IncludeLegalResearch,
            IncludeCitations = IncludeCitations,
            Language = Language,
            Jurisdiction = Jurisdiction,
            UserRole = UserRole,
            Preferences = new Dictionary<string, object>(Preferences),
            CreatedAt = CreatedAt
        };
    }

    /// <summary>
    /// Updates the conversation mode
    /// </summary>
    /// <param name="mode">New conversation mode</param>
    public ConversationContext WithMode(ConversationMode mode)
    {
        return new ConversationContext
        {
            ModelType = ModelType,
            Mode = mode,
            SystemPrompt = GetDefaultSystemPrompt(mode, Language),
            MaxTokens = MaxTokens,
            Temperature = Temperature,
            IncludeLegalResearch = IncludeLegalResearch,
            IncludeCitations = IncludeCitations,
            Language = Language,
            Jurisdiction = Jurisdiction,
            UserRole = UserRole,
            Preferences = new Dictionary<string, object>(Preferences),
            CreatedAt = CreatedAt
        };
    }

    /// <summary>
    /// Updates generation parameters
    /// </summary>
    /// <param name="maxTokens">Maximum tokens</param>
    /// <param name="temperature">Temperature</param>
    public ConversationContext WithGenerationParams(int maxTokens, double temperature)
    {
        if (maxTokens <= 0 || maxTokens > 32000)
            throw new ArgumentException("MaxTokens must be between 1 and 32000", nameof(maxTokens));

        if (temperature < 0.0 || temperature > 1.0)
            throw new ArgumentException("Temperature must be between 0 and 1", nameof(temperature));

        return new ConversationContext
        {
            ModelType = ModelType,
            Mode = Mode,
            SystemPrompt = SystemPrompt,
            MaxTokens = maxTokens,
            Temperature = temperature,
            IncludeLegalResearch = IncludeLegalResearch,
            IncludeCitations = IncludeCitations,
            Language = Language,
            Jurisdiction = Jurisdiction,
            UserRole = UserRole,
            Preferences = new Dictionary<string, object>(Preferences),
            CreatedAt = CreatedAt
        };
    }

    /// <summary>
    /// Adds or updates a preference
    /// </summary>
    /// <param name="key">Preference key</param>
    /// <param name="value">Preference value</param>
    public ConversationContext WithPreference(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Preference key cannot be empty", nameof(key));

        var newPreferences = new Dictionary<string, object>(Preferences)
        {
            [key] = value
        };

        return new ConversationContext
        {
            ModelType = ModelType,
            Mode = Mode,
            SystemPrompt = SystemPrompt,
            MaxTokens = MaxTokens,
            Temperature = Temperature,
            IncludeLegalResearch = IncludeLegalResearch,
            IncludeCitations = IncludeCitations,
            Language = Language,
            Jurisdiction = Jurisdiction,
            UserRole = UserRole,
            Preferences = newPreferences,
            CreatedAt = CreatedAt
        };
    }

    /// <summary>
    /// Gets a preference value
    /// </summary>
    /// <typeparam name="T">Preference type</typeparam>
    /// <param name="key">Preference key</param>
    /// <param name="defaultValue">Default value if not found</param>
    /// <returns>Preference value</returns>
    public T GetPreference<T>(string key, T defaultValue = default!)
    {
        if (Preferences.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    /// <summary>
    /// Gets the effective system prompt with context
    /// </summary>
    /// <returns>System prompt with context</returns>
    public string GetEffectiveSystemPrompt()
    {
        var prompt = SystemPrompt;

        if (!string.IsNullOrEmpty(Jurisdiction))
        {
            prompt += $"\n\nJuridiction: {Jurisdiction}";
        }

        if (!string.IsNullOrEmpty(UserRole))
        {
            prompt += $"\nUser Role: {UserRole}";
        }

        if (Language != "en")
        {
            prompt += $"\nPlease respond in {GetLanguageName(Language)}.";
        }

        return prompt;
    }

    private static string GetDefaultSystemPrompt(ConversationMode mode, string language)
    {
        var basePrompt = language.ToLowerInvariant() switch
        {
            "fr" => "Vous êtes LexAI, un assistant juridique IA spécialisé dans le droit français. Vous fournissez des informations juridiques précises, des analyses de documents et des conseils basés sur la législation et la jurisprudence françaises.",
            _ => "You are LexAI, an AI legal assistant specialized in French law. You provide accurate legal information, document analysis, and advice based on French legislation and case law."
        };

        return mode switch
        {
            ConversationMode.Research => basePrompt + (language == "fr" 
                ? " Vous excellez dans la recherche juridique approfondie et l'analyse de sources multiples."
                : " You excel at in-depth legal research and multi-source analysis."),
            
            ConversationMode.DocumentAnalysis => basePrompt + (language == "fr"
                ? " Vous spécialisez dans l'analyse détaillée de documents juridiques et contractuels."
                : " You specialize in detailed analysis of legal and contractual documents."),
            
            ConversationMode.LegalAdvice => basePrompt + (language == "fr"
                ? " Vous fournissez des conseils juridiques pratiques tout en rappelant les limites de votre assistance."
                : " You provide practical legal advice while noting the limitations of your assistance."),
            
            ConversationMode.QuickQuestion => basePrompt + (language == "fr"
                ? " Vous répondez de manière concise et directe aux questions juridiques simples."
                : " You provide concise and direct answers to simple legal questions."),
            
            _ => basePrompt
        };
    }

    private static string GetLanguageName(string languageCode)
    {
        return languageCode.ToLowerInvariant() switch
        {
            "fr" => "French",
            "en" => "English",
            "es" => "Spanish",
            "de" => "German",
            "it" => "Italian",
            _ => "the requested language"
        };
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return ModelType;
        yield return Mode;
        yield return SystemPrompt;
        yield return MaxTokens;
        yield return Temperature;
        yield return IncludeLegalResearch;
        yield return IncludeCitations;
        yield return Language;
        yield return Jurisdiction ?? string.Empty;
        yield return UserRole ?? string.Empty;
    }
}
