using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace LexAI.ApiGateway.Controllers;

/// <summary>
/// Contrôleur pour les informations de l'API Gateway
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class GatewayController : ControllerBase
{
    private readonly ILogger<GatewayController> _logger;
    private readonly IConfiguration _configuration;

    /// <summary>
    /// Initialise une nouvelle instance du contrôleur Gateway
    /// </summary>
    /// <param name="logger">Logger pour les traces</param>
    /// <param name="configuration">Configuration de l'application</param>
    public GatewayController(ILogger<GatewayController> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// Obtient les informations de version et de santé de l'API Gateway
    /// </summary>
    /// <returns>Informations sur l'API Gateway</returns>
    /// <response code="200">Informations récupérées avec succès</response>
    [HttpGet("info")]
    [ProducesResponseType(typeof(GatewayInfoResponse), StatusCodes.Status200OK)]
    public ActionResult<GatewayInfoResponse> GetInfo()
    {
        var assembly = Assembly.GetExecutingAssembly();
        var version = assembly.GetName().Version?.ToString() ?? "1.0.0";
        var buildDate = GetBuildDate(assembly);

        var info = new GatewayInfoResponse
        {
            Name = "LexAI API Gateway",
            Version = version,
            BuildDate = buildDate,
            Environment = _configuration["ASPNETCORE_ENVIRONMENT"] ?? "Unknown",
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Services = GetServicesInfo()
        };

        _logger.LogInformation("Gateway info requested - Version: {Version}, Environment: {Environment}", 
            version, info.Environment);

        return Ok(info);
    }

    /// <summary>
    /// Obtient la liste des services disponibles via l'API Gateway
    /// </summary>
    /// <returns>Liste des services et leurs endpoints</returns>
    /// <response code="200">Liste des services récupérée avec succès</response>
    [HttpGet("services")]
    [ProducesResponseType(typeof(IEnumerable<ServiceInfo>), StatusCodes.Status200OK)]
    public ActionResult<IEnumerable<ServiceInfo>> GetServices()
    {
        var services = GetServicesInfo();
        
        _logger.LogInformation("Services list requested - {ServiceCount} services available", services.Count());
        
        return Ok(services);
    }

    /// <summary>
    /// Endpoint de vérification de santé simple
    /// </summary>
    /// <returns>Statut de santé</returns>
    /// <response code="200">Service en bonne santé</response>
    [HttpGet("ping")]
    [ProducesResponseType(typeof(PingResponse), StatusCodes.Status200OK)]
    public ActionResult<PingResponse> Ping()
    {
        var response = new PingResponse
        {
            Message = "Pong",
            Timestamp = DateTime.UtcNow,
            Version = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "1.0.0"
        };

        return Ok(response);
    }

    /// <summary>
    /// Obtient les métriques de base de l'API Gateway
    /// </summary>
    /// <returns>Métriques de performance</returns>
    /// <response code="200">Métriques récupérées avec succès</response>
    [HttpGet("metrics")]
    [ProducesResponseType(typeof(GatewayMetrics), StatusCodes.Status200OK)]
    public ActionResult<GatewayMetrics> GetMetrics()
    {
        var metrics = new GatewayMetrics
        {
            Uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime.ToUniversalTime(),
            MemoryUsage = GC.GetTotalMemory(false),
            ProcessorCount = Environment.ProcessorCount,
            WorkingSet = Environment.WorkingSet,
            Timestamp = DateTime.UtcNow
        };

        return Ok(metrics);
    }

    private static DateTime GetBuildDate(Assembly assembly)
    {
        const string BuildVersionMetadataPrefix = "+build";
        var attribute = assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>();
        if (attribute?.InformationalVersion != null)
        {
            var value = attribute.InformationalVersion;
            var index = value.IndexOf(BuildVersionMetadataPrefix);
            if (index > 0)
            {
                value = value.Substring(index + BuildVersionMetadataPrefix.Length);
                if (DateTime.TryParseExact(value, "yyyyMMddHHmmss", null, DateTimeStyles.None, out var result))
                {
                    return result;
                }
            }
        }

        return File.GetCreationTime(assembly.Location);
    }

    private static IEnumerable<ServiceInfo> GetServicesInfo()
    {
        return new List<ServiceInfo>
        {
            new()
            {
                Name = "Identity Service",
                Description = "Service d'authentification et de gestion des utilisateurs",
                BaseUrl = "/api/identity",
                Version = "v1",
                SwaggerUrl = "/swagger/identity/swagger.json",
                HealthCheckUrl = "/api/identity/health"
            },
            new()
            {
                Name = "Legal Research Service",
                Description = "Service de recherche juridique assistée par IA",
                BaseUrl = "/api/legal-research",
                Version = "v1",
                SwaggerUrl = "/swagger/legal-research/swagger.json",
                HealthCheckUrl = "/api/legal-research/health"
            },
            new()
            {
                Name = "AI Assistant Service",
                Description = "Service de chatbot juridique intelligent",
                BaseUrl = "/api/ai-assistant",
                Version = "v1",
                SwaggerUrl = "/swagger/ai-assistant/swagger.json",
                HealthCheckUrl = "/api/ai-assistant/health"
            },
            new()
            {
                Name = "Document Analysis Service",
                Description = "Service d'analyse automatique de documents juridiques",
                BaseUrl = "/api/document-analysis",
                Version = "v1",
                SwaggerUrl = "/swagger/document-analysis/swagger.json",
                HealthCheckUrl = "/api/document-analysis/health"
            },
            new()
            {
                Name = "Document Generator Service",
                Description = "Service de génération de documents juridiques",
                BaseUrl = "/api/document-generator",
                Version = "v1",
                SwaggerUrl = "/swagger/document-generator/swagger.json",
                HealthCheckUrl = "/api/document-generator/health"
            },
            new()
            {
                Name = "Client Management Service",
                Description = "Service de gestion des clients et dossiers (CRM)",
                BaseUrl = "/api/client-management",
                Version = "v1",
                SwaggerUrl = "/swagger/client-management/swagger.json",
                HealthCheckUrl = "/api/client-management/health"
            },
            new()
            {
                Name = "Notification Service",
                Description = "Service de notifications et communications",
                BaseUrl = "/api/notifications",
                Version = "v1",
                SwaggerUrl = "/swagger/notifications/swagger.json",
                HealthCheckUrl = "/api/notifications/health"
            },
            new()
            {
                Name = "File Storage Service",
                Description = "Service de stockage sécurisé des fichiers",
                BaseUrl = "/api/files",
                Version = "v1",
                SwaggerUrl = "/swagger/file-storage/swagger.json",
                HealthCheckUrl = "/api/files/health"
            }
        };
    }
}

/// <summary>
/// Réponse contenant les informations de l'API Gateway
/// </summary>
public class GatewayInfoResponse
{
    /// <summary>
    /// Nom de l'API Gateway
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Version de l'API Gateway
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Date de build
    /// </summary>
    public DateTime BuildDate { get; set; }

    /// <summary>
    /// Environnement d'exécution
    /// </summary>
    public string Environment { get; set; } = string.Empty;

    /// <summary>
    /// Statut de santé
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp de la réponse
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Liste des services disponibles
    /// </summary>
    public IEnumerable<ServiceInfo> Services { get; set; } = new List<ServiceInfo>();
}

/// <summary>
/// Informations sur un service
/// </summary>
public class ServiceInfo
{
    /// <summary>
    /// Nom du service
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description du service
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// URL de base du service
    /// </summary>
    public string BaseUrl { get; set; } = string.Empty;

    /// <summary>
    /// Version du service
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// URL de la documentation Swagger
    /// </summary>
    public string SwaggerUrl { get; set; } = string.Empty;

    /// <summary>
    /// URL du health check
    /// </summary>
    public string HealthCheckUrl { get; set; } = string.Empty;
}

/// <summary>
/// Réponse du ping
/// </summary>
public class PingResponse
{
    /// <summary>
    /// Message de réponse
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp de la réponse
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Version du service
    /// </summary>
    public string Version { get; set; } = string.Empty;
}

/// <summary>
/// Métriques de l'API Gateway
/// </summary>
public class GatewayMetrics
{
    /// <summary>
    /// Temps de fonctionnement
    /// </summary>
    public TimeSpan Uptime { get; set; }

    /// <summary>
    /// Utilisation mémoire en bytes
    /// </summary>
    public long MemoryUsage { get; set; }

    /// <summary>
    /// Nombre de processeurs
    /// </summary>
    public int ProcessorCount { get; set; }

    /// <summary>
    /// Working set en bytes
    /// </summary>
    public long WorkingSet { get; set; }

    /// <summary>
    /// Timestamp des métriques
    /// </summary>
    public DateTime Timestamp { get; set; }
}
