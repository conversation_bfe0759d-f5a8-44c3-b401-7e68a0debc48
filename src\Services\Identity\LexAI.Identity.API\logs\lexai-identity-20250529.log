2025-05-29 22:40:33.675 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-29 22:40:33.710 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-29 22:40:33.716 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-05-29 22:40:34.500 +04:00 [INF] Executed DbCommand (67ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-05-29 22:40:34.514 +04:00 [INF] LexAI Identity Service started successfully
2025-05-29 22:40:34.555 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-29 22:40:34.895 +04:00 [INF] Now listening on: https://localhost:59998
2025-05-29 22:40:34.897 +04:00 [INF] Now listening on: http://localhost:59999
2025-05-29 22:40:34.942 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-29 22:40:34.945 +04:00 [INF] Hosting environment: Development
2025-05-29 22:40:34.947 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-05-29 22:40:37.721 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-05-29 22:40:38.060 +04:00 [INF] Request GET / started with correlation ID 7e2977df-ee1b-4b2e-890a-62c7e9cb900d
2025-05-29 22:40:38.133 +04:00 [INF] Request GET / completed in 66ms with status 404 (Correlation ID: 7e2977df-ee1b-4b2e-890a-62c7e9cb900d)
2025-05-29 22:40:38.148 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 447.706ms
2025-05-29 22:40:38.163 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-05-29 22:40:48.687 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.html - null null
2025-05-29 22:40:48.866 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.html - 200 null text/html;charset=utf-8 179.6457ms
2025-05-29 22:40:48.910 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-05-29 22:40:48.910 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.js - null null
2025-05-29 22:40:48.930 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.js - 200 null application/javascript;charset=utf-8 19.8975ms
2025-05-29 22:40:48.946 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-05-29 22:40:48.952 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 42.5179ms
2025-05-29 22:40:49.082 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 136.2092ms
2025-05-29 22:40:49.161 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - null null
2025-05-29 22:40:49.218 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 57.5815ms
2025-05-29 23:18:08.595 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998//api/auth/register - null null
2025-05-29 23:18:08.607 +04:00 [INF] Request OPTIONS //api/auth/register started with correlation ID b56a9186-835b-47fd-89c7-4ec4470dc3aa
2025-05-29 23:18:08.623 +04:00 [INF] CORS policy execution failed.
2025-05-29 23:18:08.627 +04:00 [INF] Request origin http://localhost:5173 does not have permission to access the resource.
2025-05-29 23:18:08.634 +04:00 [INF] Request OPTIONS //api/auth/register completed in 22ms with status 204 (Correlation ID: b56a9186-835b-47fd-89c7-4ec4470dc3aa)
2025-05-29 23:18:08.647 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998//api/auth/register - 204 null null 51.9804ms
2025-05-29 23:19:47.460 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998//api/auth/login - null null
2025-05-29 23:19:47.490 +04:00 [INF] Request OPTIONS //api/auth/login started with correlation ID 1179a64e-0364-455d-9b44-78ed2a9b602f
2025-05-29 23:19:47.503 +04:00 [INF] CORS policy execution failed.
2025-05-29 23:19:47.509 +04:00 [INF] Request origin http://localhost:5173 does not have permission to access the resource.
2025-05-29 23:19:47.514 +04:00 [INF] Request OPTIONS //api/auth/login completed in 14ms with status 204 (Correlation ID: 1179a64e-0364-455d-9b44-78ed2a9b602f)
2025-05-29 23:19:47.529 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998//api/auth/login - 204 null null 70.8771ms
2025-05-29 23:20:51.135 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/login - application/json 82
2025-05-29 23:20:51.155 +04:00 [INF] Request POST /api/Auth/login started with correlation ID 0611080e-6b1b-4f34-8f00-175d5bc87518
2025-05-29 23:20:51.160 +04:00 [INF] CORS policy execution failed.
2025-05-29 23:20:51.162 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-29 23:20:51.177 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-29 23:20:51.284 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-29 23:20:51.440 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-05-29 23:20:51.472 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-05-29 23:20:52.536 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-29 23:20:52.853 +04:00 [INF] Executed DbCommand (52ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-29 23:20:52.885 +04:00 [WRN] Login attempt with non-<NAME_EMAIL>
2025-05-29 23:20:53.549 +04:00 [WRN] Login failed <NAME_EMAIL>
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid email or password
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 83
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-05-29 23:20:53.649 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-29 23:20:53.706 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 2403.6513ms
2025-05-29 23:20:53.716 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-29 23:20:53.724 +04:00 [INF] Request POST /api/Auth/login completed in 2564ms with status 401 (Correlation ID: 0611080e-6b1b-4f34-8f00-175d5bc87518)
2025-05-29 23:20:53.741 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/login - 401 null application/json; charset=utf-8 2606.542ms
2025-05-29 23:23:09.150 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-29 23:23:09.213 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-29 23:23:09.221 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-05-29 23:23:09.720 +04:00 [INF] Executed DbCommand (94ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-05-29 23:23:09.737 +04:00 [INF] LexAI Identity Service started successfully
2025-05-29 23:23:09.792 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-29 23:23:10.154 +04:00 [INF] Now listening on: https://localhost:59998
2025-05-29 23:23:10.157 +04:00 [INF] Now listening on: http://localhost:59999
2025-05-29 23:23:10.209 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-29 23:23:10.213 +04:00 [INF] Hosting environment: Development
2025-05-29 23:23:10.218 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-05-29 23:23:11.551 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-05-29 23:23:11.694 +04:00 [INF] Request GET / started with correlation ID c0fe8fe9-f0d2-4d3f-b8dd-7702df560117
2025-05-29 23:23:11.782 +04:00 [INF] Request GET / completed in 82ms with status 404 (Correlation ID: c0fe8fe9-f0d2-4d3f-b8dd-7702df560117)
2025-05-29 23:23:11.793 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 255.3325ms
2025-05-29 23:23:11.801 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-05-29 23:24:29.221 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998//api/auth/login - null null
2025-05-29 23:24:29.247 +04:00 [INF] Request OPTIONS //api/auth/login started with correlation ID 6b0a66e0-6757-423b-aea9-d368f1b28bc8
2025-05-29 23:24:29.276 +04:00 [INF] CORS policy execution failed.
2025-05-29 23:24:29.286 +04:00 [INF] Request origin http://localhost:5173 does not have permission to access the resource.
2025-05-29 23:24:29.303 +04:00 [INF] Request OPTIONS //api/auth/login completed in 41ms with status 204 (Correlation ID: 6b0a66e0-6757-423b-aea9-d368f1b28bc8)
2025-05-29 23:24:29.317 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998//api/auth/login - 204 null null 96.4399ms
2025-05-29 23:25:10.485 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998//api/auth/login - null null
2025-05-29 23:25:10.501 +04:00 [INF] Request OPTIONS //api/auth/login started with correlation ID 6742e9e5-a219-4251-bd67-de66b161b633
2025-05-29 23:25:10.504 +04:00 [INF] CORS policy execution failed.
2025-05-29 23:25:10.508 +04:00 [INF] Request origin http://localhost:5173 does not have permission to access the resource.
2025-05-29 23:25:10.515 +04:00 [INF] Request OPTIONS //api/auth/login completed in 11ms with status 204 (Correlation ID: 6742e9e5-a219-4251-bd67-de66b161b633)
2025-05-29 23:25:10.527 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998//api/auth/login - 204 null null 42.3427ms
2025-05-29 23:26:14.951 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-05-29 23:26:14.978 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 259ce0b2-0962-4391-bf54-5f3946937677
2025-05-29 23:26:14.987 +04:00 [INF] CORS policy execution failed.
2025-05-29 23:26:14.993 +04:00 [INF] Request origin http://localhost:5173 does not have permission to access the resource.
2025-05-29 23:26:14.995 +04:00 [INF] Request OPTIONS /api/auth/login completed in 9ms with status 204 (Correlation ID: 259ce0b2-0962-4391-bf54-5f3946937677)
2025-05-29 23:26:14.998 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 47.5175ms
2025-05-29 23:27:07.836 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swager - null null
2025-05-29 23:27:07.905 +04:00 [INF] Request GET /swager started with correlation ID 464331f3-c9bc-4cff-9937-02d64df05926
2025-05-29 23:27:07.914 +04:00 [INF] Request GET /swager completed in 1ms with status 404 (Correlation ID: 464331f3-c9bc-4cff-9937-02d64df05926)
2025-05-29 23:27:07.918 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swager - 404 0 null 83.183ms
2025-05-29 23:27:07.929 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/swager, Response status code: 404
2025-05-29 23:27:14.693 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger - null null
2025-05-29 23:27:14.714 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger - 301 0 null 20.5618ms
2025-05-29 23:27:14.724 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.html - null null
2025-05-29 23:27:14.812 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.html - 200 null text/html;charset=utf-8 87.9103ms
2025-05-29 23:27:14.851 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/swagger-ui.css - null null
2025-05-29 23:27:14.857 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.css - null null
2025-05-29 23:27:14.880 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/swagger-ui-bundle.js - null null
2025-05-29 23:27:14.985 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.js - null null
2025-05-29 23:27:14.982 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/swagger-ui-standalone-preset.js - null null
2025-05-29 23:27:14.999 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-05-29 23:27:15.063 +04:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-05-29 23:27:15.065 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.js - 200 null application/javascript;charset=utf-8 80.9515ms
2025-05-29 23:27:15.140 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.css - 200 202 text/css 283.3548ms
2025-05-29 23:27:15.149 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 150.0862ms
2025-05-29 23:27:15.163 +04:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-05-29 23:27:15.167 +04:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-05-29 23:27:15.191 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/swagger-ui.css - 200 152035 text/css 340.2806ms
2025-05-29 23:27:15.192 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/swagger-ui-standalone-preset.js - 200 230007 text/javascript 209.2336ms
2025-05-29 23:27:15.199 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-05-29 23:27:15.246 +04:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-05-29 23:27:15.248 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/swagger-ui-bundle.js - 200 1426001 text/javascript 389.3957ms
2025-05-29 23:27:15.275 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 75.7866ms
2025-05-29 23:27:15.411 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - null null
2025-05-29 23:27:15.444 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/favicon-32x32.png - null null
2025-05-29 23:27:15.453 +04:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-05-29 23:27:15.457 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/favicon-32x32.png - 200 628 image/png 12.9383ms
2025-05-29 23:27:15.462 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 50.5138ms
2025-05-29 23:30:59.218 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/register - application/json 300
2025-05-29 23:30:59.237 +04:00 [INF] Request POST /api/Auth/register started with correlation ID d3e5ee05-9403-42e4-b900-36ecee7ea714
2025-05-29 23:30:59.240 +04:00 [INF] CORS policy execution failed.
2025-05-29 23:30:59.241 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-29 23:30:59.247 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-29 23:30:59.288 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-29 23:30:59.382 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-05-29 23:30:59.392 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-05-29 23:30:59.395 +04:00 [WRN] Attempt to register with Administrator role <NAME_EMAIL>
2025-05-29 23:30:59.632 +04:00 [WRN] Registration failed - validation error <NAME_EMAIL>
LexAI.Shared.Domain.Exceptions.InvalidDataException: Administrator role cannot be selected during public registration
   at LexAI.Identity.Application.Commands.RegisterUserCommandHandler.Handle(RegisterUserCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\RegisterUserCommand.cs:line 118
   at LexAI.Identity.API.Controllers.AuthController.Register(RegisterUserDto registerDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 325
2025-05-29 23:30:59.687 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-29 23:30:59.717 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 408.5954ms
2025-05-29 23:30:59.721 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-29 23:30:59.723 +04:00 [INF] Request POST /api/Auth/register completed in 483ms with status 400 (Correlation ID: d3e5ee05-9403-42e4-b900-36ecee7ea714)
2025-05-29 23:30:59.734 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/register - 400 null application/json; charset=utf-8 516.5335ms
2025-05-29 23:32:09.419 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/register - application/json 307
2025-05-29 23:32:09.435 +04:00 [INF] Request POST /api/Auth/register started with correlation ID 3ad9b445-1022-40a2-a2fe-220cd348eb9f
2025-05-29 23:32:09.440 +04:00 [INF] CORS policy execution failed.
2025-05-29 23:32:09.445 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-29 23:32:09.451 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-29 23:32:09.457 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-29 23:32:09.468 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-05-29 23:32:09.479 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-05-29 23:32:09.918 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-29 23:32:10.057 +04:00 [INF] Executed DbCommand (36ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-29 23:32:10.116 +04:00 [WRN] Password validation failed <NAME_EMAIL>: 
2025-05-29 23:32:10.247 +04:00 [WRN] Registration failed - validation error <NAME_EMAIL>
LexAI.Shared.Domain.Exceptions.InvalidDataException: Password validation failed: 
   at LexAI.Identity.Application.Commands.RegisterUserCommandHandler.Handle(RegisterUserCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\RegisterUserCommand.cs:line 146
   at LexAI.Identity.API.Controllers.AuthController.Register(RegisterUserDto registerDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 325
2025-05-29 23:32:10.262 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-29 23:32:10.269 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 804.7178ms
2025-05-29 23:32:10.274 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-29 23:32:10.282 +04:00 [INF] Request POST /api/Auth/register completed in 841ms with status 400 (Correlation ID: 3ad9b445-1022-40a2-a2fe-220cd348eb9f)
2025-05-29 23:32:10.308 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/register - 400 null application/json; charset=utf-8 889.3553ms
2025-05-29 23:34:15.769 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/register - application/json 309
2025-05-29 23:34:15.780 +04:00 [INF] Request POST /api/Auth/register started with correlation ID 2509ba1d-e53c-4ec7-9059-2cef41013975
2025-05-29 23:34:15.787 +04:00 [INF] CORS policy execution failed.
2025-05-29 23:34:15.790 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-29 23:34:15.794 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-29 23:34:15.798 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-29 23:34:15.802 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-05-29 23:34:15.811 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-05-29 23:34:15.903 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-29 23:34:15.911 +04:00 [WRN] Password validation failed <NAME_EMAIL>: 
2025-05-29 23:34:16.031 +04:00 [WRN] Registration failed - validation error <NAME_EMAIL>
LexAI.Shared.Domain.Exceptions.InvalidDataException: Password validation failed: 
   at LexAI.Identity.Application.Commands.RegisterUserCommandHandler.Handle(RegisterUserCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\RegisterUserCommand.cs:line 146
   at LexAI.Identity.API.Controllers.AuthController.Register(RegisterUserDto registerDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 325
2025-05-29 23:34:16.037 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-29 23:34:16.038 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 237.2851ms
2025-05-29 23:34:16.040 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-29 23:34:16.042 +04:00 [INF] Request POST /api/Auth/register completed in 255ms with status 400 (Correlation ID: 2509ba1d-e53c-4ec7-9059-2cef41013975)
2025-05-29 23:34:16.046 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/register - 400 null application/json; charset=utf-8 278.0413ms
2025-05-29 23:35:08.696 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/register - application/json 307
2025-05-29 23:35:08.719 +04:00 [INF] Request POST /api/Auth/register started with correlation ID f128d459-ab9a-4d99-9cf2-10b0d4180325
2025-05-29 23:35:08.721 +04:00 [INF] CORS policy execution failed.
2025-05-29 23:35:08.722 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-29 23:35:08.724 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-29 23:35:08.726 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-29 23:35:08.731 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-05-29 23:35:08.733 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-05-29 23:35:08.739 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-29 23:35:08.744 +04:00 [WRN] Password validation failed <NAME_EMAIL>: Password must contain at least one special character (@$!%*?&)
2025-05-29 23:35:08.842 +04:00 [WRN] Registration failed - validation error <NAME_EMAIL>
LexAI.Shared.Domain.Exceptions.InvalidDataException: Password validation failed: Password must contain at least one special character (@$!%*?&)
   at LexAI.Identity.Application.Commands.RegisterUserCommandHandler.Handle(RegisterUserCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\RegisterUserCommand.cs:line 146
   at LexAI.Identity.API.Controllers.AuthController.Register(RegisterUserDto registerDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 325
2025-05-29 23:35:08.847 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-29 23:35:08.849 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 118.6055ms
2025-05-29 23:35:08.852 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-29 23:35:08.853 +04:00 [INF] Request POST /api/Auth/register completed in 132ms with status 400 (Correlation ID: f128d459-ab9a-4d99-9cf2-10b0d4180325)
2025-05-29 23:35:08.856 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/register - 400 null application/json; charset=utf-8 160.0575ms
2025-05-29 23:35:57.665 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/register - application/json 309
2025-05-29 23:35:57.671 +04:00 [INF] Request POST /api/Auth/register started with correlation ID 35471c35-e75e-4398-abd6-94563d9fd08e
2025-05-29 23:35:57.673 +04:00 [INF] CORS policy execution failed.
2025-05-29 23:35:57.674 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-29 23:35:57.675 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-29 23:35:57.677 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-29 23:35:57.680 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-05-29 23:35:57.682 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-05-29 23:35:57.688 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-29 23:35:58.375 +04:00 [INF] Executed DbCommand (67ms) [Parameters=[@p0='6e182a69-112f-4652-993b-d7f6720d3fc8', @p1='2025-05-29T19:35:58.2188688Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Wilfried' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$UMTAeLcED2XzEu5LkfDmeu4xHvAohNoQUb8PAbYDIw6pcLo3kkd.C' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='SeniorLawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-05-29T19:35:57.9922808Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+237', @p24='+237000', @p25='5b725f61-ebe1-4326-8108-57b0e4d3964b', @p26='ProfileUpdated' (Nullable = false), @p27=NULL (DbType = Object), @p28='2025-05-29T19:35:58.2189991Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='6e182a69-112f-4652-993b-d7f6720d3fc8', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-05-29T19:35:57.9919943Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='6f221fcb-adf9-4826-8ded-fa4cda87432d', @p42='PasswordChanged' (Nullable = false), @p43=NULL (DbType = Object), @p44='2025-05-29T19:35:58.2189986Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='6e182a69-112f-4652-993b-d7f6720d3fc8', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-05-29T19:35:57.9901637Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='c353f596-cbea-450f-8e39-f1f3f11fc029', @p58='PreferencesUpdated' (Nullable = false), @p59='Language: fr-FR, TimeZone: Europe/Paris' (DbType = Object), @p60='2025-05-29T19:35:58.2189995Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='6e182a69-112f-4652-993b-d7f6720d3fc8', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-05-29T19:35:57.9922828Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='ff06ae95-2107-4e50-a398-f7ec5129d29e', @p74='Created' (Nullable = false), @p75='User created with role SeniorLawyer' (DbType = Object), @p76='2025-05-29T19:35:58.2189978Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='6e182a69-112f-4652-993b-d7f6720d3fc8', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-05-29T19:35:57.7119956Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING "Version";
2025-05-29 23:35:58.445 +04:00 [ERR] An exception occurred in the database while saving changes for context type 'LexAI.Identity.Infrastructure.Data.IdentityDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 22P02: invalid input syntax for type json

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 22P02
    MessageText: invalid input syntax for type json
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    Where: JSON data, line 1: Language...
unnamed portal parameter $3
    File: jsonfuncs.c
    Line: 646
    Routine: json_errsave_error
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 22P02: invalid input syntax for type json

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 22P02
    MessageText: invalid input syntax for type json
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    Where: JSON data, line 1: Language...
unnamed portal parameter $3
    File: jsonfuncs.c
    Line: 646
    Routine: json_errsave_error
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-05-29 23:35:58.822 +04:00 [ERR] Error registering user <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 22P02: invalid input syntax for type json

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 22P02
    MessageText: invalid input syntax for type json
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    Where: JSON data, line 1: Language...
unnamed portal parameter $3
    File: jsonfuncs.c
    Line: 646
    Routine: json_errsave_error
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.AddAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 175
   at LexAI.Identity.Application.Commands.RegisterUserCommandHandler.Handle(RegisterUserCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\RegisterUserCommand.cs:line 176
2025-05-29 23:35:58.983 +04:00 [ERR] Registration failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 22P02: invalid input syntax for type json

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 22P02
    MessageText: invalid input syntax for type json
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    Where: JSON data, line 1: Language...
unnamed portal parameter $3
    File: jsonfuncs.c
    Line: 646
    Routine: json_errsave_error
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.AddAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 175
   at LexAI.Identity.Application.Commands.RegisterUserCommandHandler.Handle(RegisterUserCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\RegisterUserCommand.cs:line 176
   at LexAI.Identity.API.Controllers.AuthController.Register(RegisterUserDto registerDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 325
2025-05-29 23:35:58.996 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-29 23:35:58.998 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 1318.1092ms
2025-05-29 23:35:59.035 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-29 23:35:59.036 +04:00 [INF] Request POST /api/Auth/register completed in 1363ms with status 400 (Correlation ID: 35471c35-e75e-4398-abd6-94563d9fd08e)
2025-05-29 23:35:59.040 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/register - 400 null application/json; charset=utf-8 1375.833ms
2025-05-29 23:56:31.279 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.html - null null
2025-05-29 23:56:31.306 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.html - 200 null text/html;charset=utf-8 26.7761ms
2025-05-29 23:56:31.367 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-05-29 23:56:31.368 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-05-29 23:56:31.381 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 14.1495ms
2025-05-29 23:56:31.367 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.js - null null
2025-05-29 23:56:31.435 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.js - 200 null application/javascript;charset=utf-8 68.147ms
2025-05-29 23:56:31.441 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 73.1308ms
2025-05-29 23:56:31.584 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - null null
2025-05-29 23:56:31.633 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 49.9841ms
2025-05-29 23:58:59.531 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/register - application/json 309
2025-05-29 23:58:59.558 +04:00 [INF] Request POST /api/Auth/register started with correlation ID 5f8c215b-b741-476c-8647-91c8996efabe
2025-05-29 23:58:59.559 +04:00 [INF] CORS policy execution failed.
2025-05-29 23:58:59.560 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-29 23:58:59.562 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-29 23:58:59.569 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-29 23:58:59.575 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-05-29 23:58:59.578 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-05-29 23:58:59.624 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-29 23:58:59.757 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='c59f970a-dd2e-402b-9c69-84503db4787e', @p1='2025-05-29T19:58:59.7514011Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Wilfried' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$inGFkGIadpCPU1I3TIVDWOhwQ5dLSKL3BEHqUHgu4OoXfryZScEe6' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='SeniorLawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-05-29T19:58:59.7506089Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+237', @p24='+237000', @p25='3396ca95-aa60-4b75-8b91-1892d7b277d6', @p26='ProfileUpdated' (Nullable = false), @p27='null' (DbType = Object), @p28='2025-05-29T19:58:59.7514017Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='c59f970a-dd2e-402b-9c69-84503db4787e', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-05-29T19:58:59.7506050Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='7e40cc16-db1e-478b-8082-f5c882fbcb3e', @p42='PasswordChanged' (Nullable = false), @p43='null' (DbType = Object), @p44='2025-05-29T19:58:59.7514016Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='c59f970a-dd2e-402b-9c69-84503db4787e', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-05-29T19:58:59.7505201Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='a04d948a-d909-4613-a438-e0141e5cf956', @p58='PreferencesUpdated' (Nullable = false), @p59='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p60='2025-05-29T19:58:59.7514018Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='c59f970a-dd2e-402b-9c69-84503db4787e', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-05-29T19:58:59.7506096Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='a3870f6c-8f7d-4df7-8201-0325aaefa10e', @p74='Created' (Nullable = false), @p75='"User created with role SeniorLawyer"' (DbType = Object), @p76='2025-05-29T19:58:59.7514015Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='c59f970a-dd2e-402b-9c69-84503db4787e', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-05-29T19:58:59.6300021Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING "Version";
2025-05-29 23:58:59.775 +04:00 [INF] User added successfully: "c59f970a-dd2e-402b-9c69-84503db4787e"
2025-05-29 23:58:59.777 +04:00 [INF] User "c59f970a-dd2e-402b-9c69-84503db4787e" registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-05-29 23:58:59.780 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-05-29 23:58:59.784 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-05-29 23:58:59.826 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 254.5665ms
2025-05-29 23:58:59.829 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-29 23:58:59.831 +04:00 [INF] Request POST /api/Auth/register completed in 272ms with status 201 (Correlation ID: 5f8c215b-b741-476c-8647-91c8996efabe)
2025-05-29 23:58:59.835 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/register - 201 null application/json; charset=utf-8 304.2613ms
