using System.Text.Json;

namespace LexAI.Shared.Domain.Common;

/// <summary>
/// Base auditable entity with audit trail functionality
/// </summary>
public abstract class AuditableEntity : BaseEntity
{
    /// <summary>
    /// Collection of audit entries for this entity
    /// </summary>
    public virtual ICollection<AuditEntry> AuditEntries { get; set; } = new List<AuditEntry>();

    /// <summary>
    /// Adds an audit entry to track changes
    /// </summary>
    /// <param name="action">The action performed (Create, Update, Delete)</param>
    /// <param name="userId">The user who performed the action</param>
    /// <param name="changes">Description of changes made</param>
    public void AddAuditEntry(string action, string userId, string? changes = null)
    {
        AuditEntries.Add(new AuditEntry
        {
            EntityId = Id,
            EntityType = GetType().Name,
            Action = action,
            UserId = userId,
            Timestamp = DateTime.UtcNow,
            Changes = JsonSerializer.Serialize(changes)
        });
    }
}

/// <summary>
/// Represents an audit entry for tracking entity changes
/// </summary>
public class AuditEntry : BaseEntity
{
    /// <summary>
    /// The ID of the entity being audited
    /// </summary>
    public Guid EntityId { get; set; }

    /// <summary>
    /// The type of entity being audited
    /// </summary>
    public string EntityType { get; set; } = string.Empty;

    /// <summary>
    /// The action performed (Create, Update, Delete, etc.)
    /// </summary>
    public string Action { get; set; } = string.Empty;

    /// <summary>
    /// The user who performed the action
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// When the action was performed
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Description of changes made (JSON format for complex changes)
    /// </summary>
    public string? Changes { get; set; }

    /// <summary>
    /// IP address of the user who performed the action
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// User agent of the client used to perform the action
    /// </summary>
    public string? UserAgent { get; set; }
}
