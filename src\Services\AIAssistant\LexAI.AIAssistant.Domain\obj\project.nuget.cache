{"version": 2, "dgSpecHash": "q9FzAOkuAxY=", "success": true, "projectFilePath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation\\11.9.2\\fluentvalidation.11.9.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr\\12.4.1\\mediatr.12.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr.contracts\\2.0.1\\mediatr.contracts.2.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.0\\microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.0\\microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\9.0.0\\system.text.json.9.0.0.nupkg.sha512"], "logs": []}