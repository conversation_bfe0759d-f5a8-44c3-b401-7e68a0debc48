#!/bin/bash
set -e

# Function to create database if it doesn't exist
create_database() {
    local database=$1
    echo "Creating database: $database"
    psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
        SELECT 'CREATE DATABASE $database'
        WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$database')\gexec
EOSQL
}

# Create databases for each microservice
create_database "identity_db"
create_database "legal_research_db"
create_database "client_management_db"
create_database "document_analysis_db"
create_database "document_generator_db"
create_database "ai_assistant_db"
create_database "notification_db"
create_database "file_storage_db"

echo "All databases created successfully!"

# Create extensions for each database
for db in identity_db legal_research_db client_management_db document_analysis_db document_generator_db ai_assistant_db notification_db file_storage_db; do
    echo "Creating extensions for database: $db"
    psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$db" <<-EOSQL
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        CREATE EXTENSION IF NOT EXISTS "pgcrypto";
        CREATE EXTENSION IF NOT EXISTS "pg_trgm";
        CREATE EXTENSION IF NOT EXISTS "unaccent";
EOSQL
done

echo "All extensions created successfully!"
