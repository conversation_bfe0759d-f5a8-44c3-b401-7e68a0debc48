using FluentAssertions;
using LexAI.LegalResearch.Application.DTOs;
using LexAI.LegalResearch.Domain.ValueObjects;
using LexAI.LegalResearch.IntegrationTests.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace LexAI.LegalResearch.IntegrationTests.Controllers;

/// <summary>
/// Integration tests for SearchController
/// </summary>
public class SearchControllerTests : IClassFixture<TestWebApplicationFactory>
{
    private readonly TestWebApplicationFactory _factory;
    private readonly HttpClient _client;
    private readonly JsonSerializerOptions _jsonOptions;

    public SearchControllerTests(TestWebApplicationFactory factory)
    {
        _factory = factory;
        _client = _factory.CreateAuthenticatedClient();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    [Fact]
    public async Task Search_WithValidRequest_ShouldReturnSearchResults()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        await _factory.SeedDatabaseAsync();

        var searchRequest = new SearchRequestDto
        {
            Query = "contrat de travail CDI",
            Method = SearchMethod.Semantic,
            DomainFilter = LegalDomain.Labor,
            Limit = 10,
            MinRelevanceScore = 0.7,
            IncludeHighlights = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/search/search", searchRequest, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var content = await response.Content.ReadAsStringAsync();
        var searchResponse = JsonSerializer.Deserialize<SearchResponseDto>(content, _jsonOptions);

        searchResponse.Should().NotBeNull();
        searchResponse!.Query.Should().Be(searchRequest.Query);
        searchResponse.Method.Should().Be(SearchMethod.Semantic);
        searchResponse.ExecutionTimeMs.Should().BeGreaterThan(0);
        searchResponse.QueryId.Should().NotBeEmpty();
    }

    [Fact]
    public async Task Search_WithEmptyQuery_ShouldReturnBadRequest()
    {
        // Arrange
        var searchRequest = new SearchRequestDto
        {
            Query = "",
            Method = SearchMethod.Semantic
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/search/search", searchRequest, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Search query cannot be empty");
    }

    [Fact]
    public async Task Search_WithoutAuthentication_ShouldReturnUnauthorized()
    {
        // Arrange
        var unauthenticatedClient = _factory.CreateClient();
        var searchRequest = new SearchRequestDto
        {
            Query = "test query",
            Method = SearchMethod.Semantic
        };

        // Act
        var response = await unauthenticatedClient.PostAsJsonAsync("/api/search/search", searchRequest, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task HybridSearch_WithValidRequest_ShouldReturnHybridResults()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        await _factory.SeedDatabaseAsync();

        var searchRequest = new SearchRequestDto
        {
            Query = "licenciement pour faute grave",
            DomainFilter = LegalDomain.Labor,
            TypeFilter = DocumentType.Law,
            Limit = 15,
            IncludeHighlights = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/search/hybrid-search", searchRequest, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var content = await response.Content.ReadAsStringAsync();
        var searchResponse = JsonSerializer.Deserialize<SearchResponseDto>(content, _jsonOptions);

        searchResponse.Should().NotBeNull();
        searchResponse!.Query.Should().Be(searchRequest.Query);
        searchResponse.Method.Should().Be(SearchMethod.Hybrid);
        searchResponse.ExecutionTimeMs.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task FindSimilarDocuments_WithValidDocumentId_ShouldReturnSimilarDocuments()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        await _factory.SeedDatabaseAsync();

        var documentId = Guid.NewGuid(); // In real test, this would be a seeded document ID
        var limit = 5;

        // Act
        var response = await _client.GetAsync($"/api/search/similar/{documentId}?limit={limit}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var content = await response.Content.ReadAsStringAsync();
        var similarDocuments = JsonSerializer.Deserialize<List<SearchResultDto>>(content, _jsonOptions);

        similarDocuments.Should().NotBeNull();
        similarDocuments!.Should().HaveCountLessOrEqualTo(limit);
    }

    [Fact]
    public async Task FindSimilarDocuments_WithInvalidLimit_ShouldReturnBadRequest()
    {
        // Arrange
        var documentId = Guid.NewGuid();
        var invalidLimit = 0;

        // Act
        var response = await _client.GetAsync($"/api/search/similar/{documentId}?limit={invalidLimit}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Limit must be between 1 and 50");
    }

    [Fact]
    public async Task GetSearchSuggestions_WithValidQuery_ShouldReturnSuggestions()
    {
        // Arrange
        var partialQuery = "contrat";
        var limit = 10;

        // Act
        var response = await _client.GetAsync($"/api/search/suggestions?q={partialQuery}&limit={limit}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var content = await response.Content.ReadAsStringAsync();
        var suggestions = JsonSerializer.Deserialize<List<string>>(content, _jsonOptions);

        suggestions.Should().NotBeNull();
        suggestions!.Should().HaveCountLessOrEqualTo(limit);
    }

    [Theory]
    [InlineData("a")] // Too short
    [InlineData("")] // Empty
    public async Task GetSearchSuggestions_WithInvalidQuery_ShouldReturnBadRequest(string invalidQuery)
    {
        // Act
        var response = await _client.GetAsync($"/api/search/suggestions?q={invalidQuery}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Query must be at least 2 characters long");
    }

    [Fact]
    public async Task AnalyzeQuery_WithValidQuery_ShouldReturnAnalysis()
    {
        // Arrange
        var query = "Quelles sont les conditions de licenciement pour faute grave?";

        // Act
        var response = await _client.PostAsJsonAsync("/api/search/analyze", query, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var content = await response.Content.ReadAsStringAsync();
        var analysis = JsonSerializer.Deserialize<QueryAnalysisDto>(content, _jsonOptions);

        analysis.Should().NotBeNull();
        analysis!.OriginalQuery.Should().Be(query);
        analysis.ProcessedQuery.Should().NotBeEmpty();
        analysis.Intent.Should().NotBe(QueryIntent.Unknown);
    }

    [Fact]
    public async Task AnalyzeQuery_WithEmptyQuery_ShouldReturnBadRequest()
    {
        // Arrange
        var emptyQuery = "";

        // Act
        var response = await _client.PostAsJsonAsync("/api/search/analyze", emptyQuery, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Query cannot be empty");
    }

    [Fact]
    public async Task ProvideFeedback_WithValidFeedback_ShouldReturnSuccess()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        await _factory.SeedDatabaseAsync();

        // First perform a search to get a query ID
        var searchRequest = new SearchRequestDto
        {
            Query = "test query for feedback",
            Method = SearchMethod.Semantic
        };

        var searchResponse = await _client.PostAsJsonAsync("/api/search/search", searchRequest, _jsonOptions);
        searchResponse.EnsureSuccessStatusCode();

        var searchContent = await searchResponse.Content.ReadAsStringAsync();
        var searchResult = JsonSerializer.Deserialize<SearchResponseDto>(searchContent, _jsonOptions);
        var queryId = searchResult!.QueryId;

        var feedback = new UserFeedbackDto
        {
            OverallRating = 4,
            RelevanceRating = 4,
            CompletenessRating = 3,
            UsefulnessRating = 4,
            Comments = "Good results, very helpful"
        };

        // Act
        var response = await _client.PostAsJsonAsync($"/api/search/feedback/{queryId}", feedback, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Feedback recorded successfully");
    }

    [Fact]
    public async Task ProvideFeedback_WithInvalidQueryId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidQueryId = Guid.NewGuid();
        var feedback = new UserFeedbackDto
        {
            OverallRating = 4,
            RelevanceRating = 4,
            CompletenessRating = 3,
            UsefulnessRating = 4
        };

        // Act
        var response = await _client.PostAsJsonAsync($"/api/search/feedback/{invalidQueryId}", feedback, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetSearchAnalytics_ShouldReturnAnalytics()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        await _factory.SeedDatabaseAsync();

        // Act
        var response = await _client.GetAsync("/api/search/analytics");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var content = await response.Content.ReadAsStringAsync();
        var analytics = JsonSerializer.Deserialize<SearchAnalyticsDto>(content, _jsonOptions);

        analytics.Should().NotBeNull();
        analytics!.TotalSearches.Should().BeGreaterOrEqualTo(0);
        analytics.AverageExecutionTime.Should().BeGreaterOrEqualTo(0);
    }

    [Fact]
    public async Task Search_WithRateLimiting_ShouldEventuallyReturnTooManyRequests()
    {
        // Arrange
        var searchRequest = new SearchRequestDto
        {
            Query = "rate limit test",
            Method = SearchMethod.Semantic
        };

        var tasks = new List<Task<HttpResponseMessage>>();

        // Act - Send many requests quickly to trigger rate limiting
        for (int i = 0; i < 150; i++) // Exceed the rate limit of 100/minute
        {
            tasks.Add(_client.PostAsJsonAsync("/api/search/search", searchRequest, _jsonOptions));
        }

        var responses = await Task.WhenAll(tasks);

        // Assert - At least some requests should be rate limited
        var rateLimitedResponses = responses.Count(r => r.StatusCode == HttpStatusCode.TooManyRequests);
        rateLimitedResponses.Should().BeGreaterThan(0);

        // Clean up
        foreach (var response in responses)
        {
            response.Dispose();
        }
    }

    [Fact]
    public async Task Search_WithComplexFilters_ShouldApplyFiltersCorrectly()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        await _factory.SeedDatabaseAsync();

        var searchRequest = new SearchRequestDto
        {
            Query = "contrat de travail",
            Method = SearchMethod.Hybrid,
            DomainFilter = LegalDomain.Labor,
            TypeFilter = DocumentType.Law,
            LanguageFilter = "fr",
            DateFilter = new DateRangeDto
            {
                StartDate = new DateTime(2020, 1, 1),
                EndDate = new DateTime(2024, 12, 31)
            },
            Limit = 20,
            MinRelevanceScore = 0.8,
            IncludeHighlights = true,
            SortOrder = SearchSortOrder.Relevance
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/search/search", searchRequest, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var content = await response.Content.ReadAsStringAsync();
        var searchResponse = JsonSerializer.Deserialize<SearchResponseDto>(content, _jsonOptions);

        searchResponse.Should().NotBeNull();
        searchResponse!.Query.Should().Be(searchRequest.Query);
        searchResponse.Results.Should().HaveCountLessOrEqualTo(searchRequest.Limit);

        // Verify that results match the filters (when actual data is present)
        foreach (var result in searchResponse.Results)
        {
            if (searchRequest.DomainFilter.HasValue)
            {
                result.LegalDomain.Should().Be(searchRequest.DomainFilter.Value);
            }
            if (searchRequest.TypeFilter.HasValue)
            {
                result.DocumentType.Should().Be(searchRequest.TypeFilter.Value);
            }
            result.RelevanceScore.Should().BeGreaterOrEqualTo(searchRequest.MinRelevanceScore);
        }
    }
}
