// MongoDB initialization script for LexAI
print('Starting MongoDB initialization for LexAI...');

// Switch to the lexai_documents database
db = db.getSiblingDB('lexai_documents');

// Create collections for different document types
print('Creating collections...');

// Legal documents collection for RAG
db.createCollection('legal_documents', {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["title", "content", "document_type", "created_at"],
            properties: {
                title: {
                    bsonType: "string",
                    description: "Document title is required"
                },
                content: {
                    bsonType: "string",
                    description: "Document content is required"
                },
                document_type: {
                    bsonType: "string",
                    enum: ["law", "jurisprudence", "doctrine", "regulation"],
                    description: "Document type must be one of the allowed values"
                },
                source: {
                    bsonType: "string",
                    description: "Source of the document"
                },
                jurisdiction: {
                    bsonType: "string",
                    description: "Legal jurisdiction"
                },
                practice_area: {
                    bsonType: "string",
                    description: "Practice area"
                },
                embeddings: {
                    bsonType: "array",
                    description: "Vector embeddings for similarity search"
                },
                metadata: {
                    bsonType: "object",
                    description: "Additional metadata"
                },
                created_at: {
                    bsonType: "date",
                    description: "Creation timestamp is required"
                },
                updated_at: {
                    bsonType: "date",
                    description: "Last update timestamp"
                }
            }
        }
    }
});

// Chat conversations collection
db.createCollection('chat_conversations', {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["user_id", "created_at"],
            properties: {
                user_id: {
                    bsonType: "string",
                    description: "User ID is required"
                },
                title: {
                    bsonType: "string",
                    description: "Conversation title"
                },
                messages: {
                    bsonType: "array",
                    items: {
                        bsonType: "object",
                        required: ["role", "content", "timestamp"],
                        properties: {
                            role: {
                                bsonType: "string",
                                enum: ["user", "assistant", "system"],
                                description: "Message role"
                            },
                            content: {
                                bsonType: "string",
                                description: "Message content"
                            },
                            timestamp: {
                                bsonType: "date",
                                description: "Message timestamp"
                            },
                            metadata: {
                                bsonType: "object",
                                description: "Additional message metadata"
                            }
                        }
                    }
                },
                created_at: {
                    bsonType: "date",
                    description: "Creation timestamp is required"
                },
                updated_at: {
                    bsonType: "date",
                    description: "Last update timestamp"
                }
            }
        }
    }
});

// Document analysis results collection
db.createCollection('document_analysis', {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["document_id", "user_id", "analysis_type", "created_at"],
            properties: {
                document_id: {
                    bsonType: "string",
                    description: "Document ID is required"
                },
                user_id: {
                    bsonType: "string",
                    description: "User ID is required"
                },
                analysis_type: {
                    bsonType: "string",
                    enum: ["contract", "legal_document", "clause_extraction", "risk_assessment"],
                    description: "Analysis type"
                },
                results: {
                    bsonType: "object",
                    description: "Analysis results"
                },
                confidence_score: {
                    bsonType: "double",
                    minimum: 0,
                    maximum: 1,
                    description: "Confidence score between 0 and 1"
                },
                processing_time_ms: {
                    bsonType: "int",
                    description: "Processing time in milliseconds"
                },
                created_at: {
                    bsonType: "date",
                    description: "Creation timestamp is required"
                }
            }
        }
    }
});

// Generated documents collection
db.createCollection('generated_documents', {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["user_id", "template_type", "created_at"],
            properties: {
                user_id: {
                    bsonType: "string",
                    description: "User ID is required"
                },
                template_type: {
                    bsonType: "string",
                    enum: ["contract", "letter", "notice", "statutes", "brief", "pleading", "opinion"],
                    description: "Template type"
                },
                title: {
                    bsonType: "string",
                    description: "Document title"
                },
                content: {
                    bsonType: "string",
                    description: "Generated document content"
                },
                input_data: {
                    bsonType: "object",
                    description: "Input data used for generation"
                },
                file_path: {
                    bsonType: "string",
                    description: "Path to generated file"
                },
                file_format: {
                    bsonType: "string",
                    enum: ["pdf", "docx", "html"],
                    description: "Generated file format"
                },
                created_at: {
                    bsonType: "date",
                    description: "Creation timestamp is required"
                }
            }
        }
    }
});

// Search queries collection for analytics
db.createCollection('search_queries', {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["user_id", "query", "created_at"],
            properties: {
                user_id: {
                    bsonType: "string",
                    description: "User ID is required"
                },
                query: {
                    bsonType: "string",
                    description: "Search query is required"
                },
                query_type: {
                    bsonType: "string",
                    enum: ["legal_research", "document_search", "case_law", "regulation"],
                    description: "Type of search query"
                },
                results_count: {
                    bsonType: "int",
                    description: "Number of results returned"
                },
                response_time_ms: {
                    bsonType: "int",
                    description: "Response time in milliseconds"
                },
                created_at: {
                    bsonType: "date",
                    description: "Creation timestamp is required"
                }
            }
        }
    }
});

print('Creating indexes...');

// Create indexes for better performance
db.legal_documents.createIndex({ "document_type": 1, "jurisdiction": 1 });
db.legal_documents.createIndex({ "practice_area": 1 });
db.legal_documents.createIndex({ "created_at": -1 });
db.legal_documents.createIndex({ "title": "text", "content": "text" });

db.chat_conversations.createIndex({ "user_id": 1, "created_at": -1 });
db.chat_conversations.createIndex({ "created_at": -1 });

db.document_analysis.createIndex({ "document_id": 1 });
db.document_analysis.createIndex({ "user_id": 1, "created_at": -1 });
db.document_analysis.createIndex({ "analysis_type": 1 });

db.generated_documents.createIndex({ "user_id": 1, "created_at": -1 });
db.generated_documents.createIndex({ "template_type": 1 });

db.search_queries.createIndex({ "user_id": 1, "created_at": -1 });
db.search_queries.createIndex({ "query_type": 1, "created_at": -1 });

print('Creating sample data...');

// Insert sample legal documents
db.legal_documents.insertMany([
    {
        title: "Code Civil - Article 1134",
        content: "Les conventions légalement formées tiennent lieu de loi à ceux qui les ont faites.",
        document_type: "law",
        source: "Code Civil",
        jurisdiction: "France",
        practice_area: "Contract",
        metadata: {
            article_number: "1134",
            book: "III",
            title: "Des contrats ou des obligations conventionnelles en général"
        },
        created_at: new Date(),
        updated_at: new Date()
    },
    {
        title: "Code du Travail - Article L1221-1",
        content: "Le contrat de travail est soumis aux règles du droit commun. Il peut être établi selon les formes que les parties contractantes décident d'adopter.",
        document_type: "law",
        source: "Code du Travail",
        jurisdiction: "France",
        practice_area: "Employment",
        metadata: {
            article_number: "L1221-1",
            part: "Première partie législative"
        },
        created_at: new Date(),
        updated_at: new Date()
    }
]);

print('MongoDB initialization completed successfully!');

// Create user for application access
db.createUser({
    user: "lexai_app",
    pwd: "lexai_app_password_2024!",
    roles: [
        {
            role: "readWrite",
            db: "lexai_documents"
        }
    ]
});

print('Application user created successfully!');
