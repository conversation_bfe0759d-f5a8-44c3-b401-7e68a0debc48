2025-05-28 00:07:36.113 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-28 00:07:36.149 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-28 00:07:36.156 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-05-28 00:07:36.596 +04:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-05-28 00:07:36.611 +04:00 [INF] LexAI Identity Service started successfully
2025-05-28 00:07:36.648 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-28 00:07:36.963 +04:00 [INF] Now listening on: https://localhost:59998
2025-05-28 00:07:36.965 +04:00 [INF] Now listening on: http://localhost:59999
2025-05-28 00:07:37.007 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-28 00:07:37.010 +04:00 [INF] Hosting environment: Development
2025-05-28 00:07:37.012 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-05-28 00:07:38.522 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-05-28 00:07:38.728 +04:00 [INF] Request GET / started with correlation ID 51aa25cc-5ed8-4653-9634-180e8b499320
2025-05-28 00:07:59.236 +04:00 [ERR] An unhandled exception has occurred while executing the request.
System.ArgumentException: IDX10703: Cannot create a 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey', key length is zero.
   at Microsoft.IdentityModel.Tokens.SymmetricSecurityKey..ctor(Byte[] key)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__2(JwtBearerOptions options) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Program.cs:line 111
   at Microsoft.Extensions.Options.OptionsFactory`1.Create(String name)
   at System.Lazy`1.ViaFactory(LazyThreadSafetyMode mode)
   at System.Lazy`1.ExecutionAndPublication(LazyHelper executionAndPublication, Boolean useDefaultConstructor)
   at System.Lazy`1.CreateValue()
   at Microsoft.Extensions.Options.OptionsCache`1.GetOrAdd[TArg](String name, Func`3 createOptions, TArg factoryArgument)
   at Microsoft.Extensions.Options.OptionsMonitor`1.Get(String name)
   at Microsoft.AspNetCore.Authentication.AuthenticationHandler`1.InitializeAsync(AuthenticationScheme scheme, HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationHandlerProvider.GetHandlerAsync(HttpContext context, String authenticationScheme)
   at Microsoft.AspNetCore.Authentication.AuthenticationService.AuthenticateAsync(HttpContext context, String scheme)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_9>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Program.cs:line 230
--- End of stack trace from previous location ---
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__8>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Program.cs:line 215
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-05-28 00:07:59.343 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 500 null text/html; charset=utf-8 20830.6436ms
2025-05-28 00:07:59.370 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-05-28 00:07:59.370 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-05-28 00:07:59.393 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 22.9258ms
2025-05-28 00:07:59.430 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 60.8247ms
2025-05-28 00:07:59.487 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/favicon.ico - null null
2025-05-28 00:07:59.495 +04:00 [INF] Request GET /favicon.ico started with correlation ID c049430c-f29c-4894-a05c-02db3bbade40
2025-05-28 00:08:00.597 +04:00 [ERR] An unhandled exception has occurred while executing the request.
System.ArgumentException: IDX10703: Cannot create a 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey', key length is zero.
   at Microsoft.IdentityModel.Tokens.SymmetricSecurityKey..ctor(Byte[] key)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__2(JwtBearerOptions options) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Program.cs:line 111
   at Microsoft.Extensions.Options.OptionsFactory`1.Create(String name)
   at System.Lazy`1.ViaFactory(LazyThreadSafetyMode mode)
--- End of stack trace from previous location ---
   at System.Lazy`1.CreateValue()
   at Microsoft.Extensions.Options.OptionsCache`1.GetOrAdd[TArg](String name, Func`3 createOptions, TArg factoryArgument)
   at Microsoft.Extensions.Options.OptionsMonitor`1.Get(String name)
   at Microsoft.AspNetCore.Authentication.AuthenticationHandler`1.InitializeAsync(AuthenticationScheme scheme, HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationHandlerProvider.GetHandlerAsync(HttpContext context, String authenticationScheme)
   at Microsoft.AspNetCore.Authentication.AuthenticationService.AuthenticateAsync(HttpContext context, String scheme)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_9>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Program.cs:line 230
--- End of stack trace from previous location ---
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__8>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Program.cs:line 215
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-05-28 00:08:00.612 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/favicon.ico - 500 null text/plain; charset=utf-8 1125.3663ms
2025-05-28 00:10:18.815 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-28 00:10:18.844 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-28 00:10:18.849 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-05-28 00:10:19.215 +04:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-05-28 00:10:19.226 +04:00 [INF] LexAI Identity Service started successfully
2025-05-28 00:10:19.259 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-28 00:10:19.507 +04:00 [INF] Now listening on: https://localhost:59998
2025-05-28 00:10:19.509 +04:00 [INF] Now listening on: http://localhost:59999
2025-05-28 00:10:19.615 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-28 00:10:19.618 +04:00 [INF] Hosting environment: Development
2025-05-28 00:10:19.620 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-05-28 00:10:20.238 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-05-28 00:10:20.386 +04:00 [INF] Request GET / started with correlation ID cd0ccb4b-7555-4c3b-9c38-451332b4dc7c
2025-05-28 00:15:31.360 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-28 00:15:31.387 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-28 00:15:31.393 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-05-28 00:15:31.742 +04:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-05-28 00:15:31.756 +04:00 [INF] LexAI Identity Service started successfully
2025-05-28 00:15:31.786 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-28 00:15:31.955 +04:00 [INF] Now listening on: https://localhost:59998
2025-05-28 00:15:31.956 +04:00 [INF] Now listening on: http://localhost:59999
2025-05-28 00:15:32.155 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-28 00:15:32.159 +04:00 [INF] Hosting environment: Development
2025-05-28 00:15:32.161 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-05-28 00:15:32.746 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-05-28 00:15:32.880 +04:00 [INF] Request GET / started with correlation ID c833806b-b4e8-456e-8f5f-50c557280734
2025-05-28 00:15:32.973 +04:00 [INF] Request GET / completed in 85ms with status 404 (Correlation ID: c833806b-b4e8-456e-8f5f-50c557280734)
2025-05-28 00:15:32.985 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 256.0224ms
2025-05-28 00:15:33.000 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-05-28 00:17:12.258 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger - null null
2025-05-28 00:17:12.288 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger - 301 0 null 68.6765ms
2025-05-28 00:17:12.300 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.html - null null
2025-05-28 00:17:12.357 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.html - 200 null text/html;charset=utf-8 57.5102ms
2025-05-28 00:17:12.387 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/swagger-ui.css - null null
2025-05-28 00:17:12.387 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.css - null null
2025-05-28 00:17:12.435 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/swagger-ui-standalone-preset.js - null null
2025-05-28 00:17:12.387 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/swagger-ui-bundle.js - null null
2025-05-28 00:17:12.582 +04:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-05-28 00:17:12.436 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.js - null null
2025-05-28 00:17:12.607 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.css - 200 202 text/css 219.5791ms
2025-05-28 00:17:12.511 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-05-28 00:17:12.512 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-05-28 00:17:12.654 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.js - 200 null application/javascript;charset=utf-8 217.9868ms
2025-05-28 00:17:12.702 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 254.7568ms
2025-05-28 00:17:12.666 +04:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-05-28 00:17:12.666 +04:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-05-28 00:17:12.730 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/swagger-ui-standalone-preset.js - 200 230007 text/javascript 341.452ms
2025-05-28 00:17:12.731 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/swagger-ui.css - 200 152035 text/css 343.8025ms
2025-05-28 00:17:12.740 +04:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-05-28 00:17:12.748 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/swagger-ui-bundle.js - 200 1426001 text/javascript 360.947ms
2025-05-28 00:17:12.750 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 238.4966ms
2025-05-28 00:17:12.875 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - null null
2025-05-28 00:17:12.876 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/favicon-32x32.png - null null
2025-05-28 00:17:12.888 +04:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-05-28 00:17:12.892 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/favicon-32x32.png - 200 628 image/png 15.8582ms
2025-05-28 00:17:12.910 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 35.1832ms
2025-05-28 00:18:51.279 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/login - application/json 77
2025-05-28 00:18:51.291 +04:00 [INF] Request POST /api/Auth/login started with correlation ID e686940f-459e-43c8-983b-24887b8934d5
2025-05-28 00:18:51.296 +04:00 [INF] CORS policy execution failed.
2025-05-28 00:18:51.298 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-28 00:18:51.304 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-28 00:18:51.329 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-28 00:18:51.374 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-05-28 00:18:51.383 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-05-28 00:18:51.792 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-28 00:18:51.869 +04:00 [INF] Executed DbCommand (24ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-28 00:18:51.878 +04:00 [WRN] Login attempt with non-<NAME_EMAIL>
2025-05-28 00:18:52.164 +04:00 [WRN] Login failed <NAME_EMAIL>
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid email or password
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 83
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 62
2025-05-28 00:18:52.195 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-28 00:18:52.208 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 871.8801ms
2025-05-28 00:18:52.211 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-28 00:18:52.213 +04:00 [INF] Request POST /api/Auth/login completed in 918ms with status 401 (Correlation ID: e686940f-459e-43c8-983b-24887b8934d5)
2025-05-28 00:18:52.219 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/login - 401 null application/json; charset=utf-8 998.1566ms
2025-05-28 00:19:30.894 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/login - application/json 77
2025-05-28 00:19:30.943 +04:00 [INF] Request POST /api/Auth/login started with correlation ID 6d47c3a9-4359-4e68-a240-b50c0638eaf1
2025-05-28 00:19:30.947 +04:00 [INF] CORS policy execution failed.
2025-05-28 00:19:30.949 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-28 00:19:30.955 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-28 00:19:30.959 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-28 00:19:30.963 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-05-28 00:19:30.969 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-05-28 00:19:31.018 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-28 00:19:31.021 +04:00 [WRN] Login attempt with non-<NAME_EMAIL>
2025-05-28 00:19:31.190 +04:00 [WRN] Login failed <NAME_EMAIL>
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid email or password
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 83
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 62
2025-05-28 00:19:31.197 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-28 00:19:31.201 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 239.3479ms
2025-05-28 00:19:31.204 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-28 00:19:31.206 +04:00 [INF] Request POST /api/Auth/login completed in 259ms with status 401 (Correlation ID: 6d47c3a9-4359-4e68-a240-b50c0638eaf1)
2025-05-28 00:19:31.209 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/login - 401 null application/json; charset=utf-8 317.3488ms
