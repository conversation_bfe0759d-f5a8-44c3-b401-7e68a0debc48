using LexAI.Identity.Application.Interfaces;
using LexAI.Identity.Domain.Entities;
using LexAI.Identity.Infrastructure.Data;
using LexAI.Shared.Domain.Common;
using LexAI.Shared.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace LexAI.Identity.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for User entity
/// </summary>
public class UserRepository : IUserRepository
{
    private readonly IdentityDbContext _context;
    private readonly ILogger<UserRepository> _logger;

    /// <summary>
    /// Initializes a new instance of the UserRepository
    /// </summary>
    /// <param name="context">Database context</param>
    /// <param name="logger">Logger</param>
    public UserRepository(IdentityDbContext context, ILogger<UserRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Gets a user by their unique identifier
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User entity or null if not found</returns>
    public async Task<User?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting user by ID: {UserId}", id);

        return await _context.Users
            .Include(u => u.RefreshTokens)
            .Include(u => u.Permissions)
            .FirstOrDefaultAsync(u => u.Id == id, cancellationToken);
    }

    /// <summary>
    /// Gets a user by their email address
    /// </summary>
    /// <param name="email">Email address</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User entity or null if not found</returns>
    public async Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting user by email: {Email}", email);

        return await _context.Users
            .Include(u => u.RefreshTokens)
            .Include(u => u.Permissions)
            .FirstOrDefaultAsync(u => u.Email.Value == email.ToLowerInvariant(), cancellationToken);
    }

    /// <summary>
    /// Gets all users with pagination
    /// </summary>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Number of items per page</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of users</returns>
    public async Task<PagedResult<User>> GetAllAsync(int pageNumber = 1, int pageSize = 10, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting all users - Page: {PageNumber}, Size: {PageSize}", pageNumber, pageSize);

        var query = _context.Users.AsQueryable();

        var totalCount = await query.CountAsync(cancellationToken);
        var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

        var users = await query
            .OrderBy(u => u.LastName)
            .ThenBy(u => u.FirstName)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<User>
       (
            users,
            totalCount,
            pageNumber,
            pageSize,
            totalPages,
            pageNumber < totalPages,
            pageNumber > 1
        );
    }

    /// <summary>
    /// Searches users by criteria
    /// </summary>
    /// <param name="searchTerm">Search term for name or email</param>
    /// <param name="role">Optional role filter</param>
    /// <param name="isActive">Optional active status filter</param>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Number of items per page</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated search results</returns>
    public async Task<PagedResult<User>> SearchAsync(
        string? searchTerm = null,
        UserRole? role = null,
        bool? isActive = null,
        int pageNumber = 1,
        int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Searching users - Term: {SearchTerm}, Role: {Role}, Active: {IsActive}, Page: {PageNumber}, Size: {PageSize}",
            searchTerm, role, isActive, pageNumber, pageSize);

        var query = _context.Users.AsQueryable();

        // Apply search term filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var lowerSearchTerm = searchTerm.ToLowerInvariant();
            query = query.Where(u =>
                u.FirstName.ToLower().Contains(lowerSearchTerm) ||
                u.LastName.ToLower().Contains(lowerSearchTerm) ||
                u.Email.Value.Contains(lowerSearchTerm));
        }

        // Apply role filter
        if (role.HasValue)
        {
            query = query.Where(u => u.Role == role.Value);
        }

        // Apply active status filter
        if (isActive.HasValue)
        {
            query = query.Where(u => u.IsActive == isActive.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);
        var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

        var users = await query
            .OrderBy(u => u.LastName)
            .ThenBy(u => u.FirstName)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<User>
        (
            users,
            totalCount,
            pageNumber,
            pageSize,
            totalPages,
            pageNumber < totalPages,
            pageNumber > 1
           );
    }

    /// <summary>
    /// Adds a new user to the repository
    /// </summary>
    /// <param name="user">User entity to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Added user entity</returns>
    public async Task<User> AddAsync(User user, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Adding new user: {Email}", user.Email);

        _context.Users.Add(user);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("User added successfully: {UserId}", user.Id);
        return user;
    }

    /// <summary>
    /// Updates an existing user
    /// </summary>
    /// <param name="user">User entity to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated user entity</returns>
    public async Task<User> UpdateAsync(User user, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Updating user: {UserId}", user.Id);

        _context.Users.Update(user);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("User updated successfully: {UserId}", user.Id);
        return user;
    }

    /// <summary>
    /// Soft deletes a user
    /// </summary>
    /// <param name="id">User ID to delete</param>
    /// <param name="deletedBy">ID of the user performing the deletion</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if deletion was successful</returns>
    public async Task<bool> DeleteAsync(Guid id, string deletedBy, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Soft deleting user: {UserId}", id);

        var user = await _context.Users.FindAsync(new object[] { id }, cancellationToken);
        if (user == null)
        {
            _logger.LogWarning("User not found for deletion: {UserId}", id);
            return false;
        }

        user.IsDeleted = true;
        user.DeletedAt = DateTime.UtcNow;
        user.DeletedBy = deletedBy;

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("User soft deleted successfully: {UserId}", id);
        return true;
    }

    /// <summary>
    /// Checks if a user exists with the given email
    /// </summary>
    /// <param name="email">Email address to check</param>
    /// <param name="excludeUserId">Optional user ID to exclude from the check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if user exists</returns>
    public async Task<bool> ExistsAsync(string email, Guid? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Checking if user exists with email: {Email}", email);

        var query = _context.Users.Where(u => u.Email.Value == email.ToLowerInvariant());

        if (excludeUserId.HasValue)
        {
            query = query.Where(u => u.Id != excludeUserId.Value);
        }

        return await query.AnyAsync(cancellationToken);
    }

    /// <summary>
    /// Gets users by role
    /// </summary>
    /// <param name="role">User role</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of users with the specified role</returns>
    public async Task<IEnumerable<User>> GetByRoleAsync(UserRole role, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting users by role: {Role}", role);

        return await _context.Users
            .Where(u => u.Role == role && u.IsActive)
            .OrderBy(u => u.LastName)
            .ThenBy(u => u.FirstName)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Gets locked users
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of locked users</returns>
    public async Task<IEnumerable<User>> GetLockedUsersAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting locked users");

        return await _context.Users
            .Where(u => u.IsLocked)
            .OrderBy(u => u.LockedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Gets users with unverified emails
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of users with unverified emails</returns>
    public async Task<IEnumerable<User>> GetUnverifiedUsersAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting users with unverified emails");

        return await _context.Users
            .Where(u => !u.IsEmailVerified && u.IsActive)
            .OrderBy(u => u.CreatedAt)
            .ToListAsync(cancellationToken);
    }
}
