2025-05-30 02:18:14.262 +04:00 [FTL] LexAI Legal Research Service failed to start
System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: MediatR.IRequestHandler`2[LexAI.LegalResearch.Application.Commands.PerformSearchCommand,LexAI.LegalResearch.Application.DTOs.SearchResponseDto] Lifetime: Transient ImplementationType: LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler': Unable to resolve service for type 'LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService' while attempting to activate 'LexAI.LegalResearch.Infrastructure.Services.LegalSearchService'.) (Error while validating the service descriptor 'ServiceType: MediatR.IRequestHandler`2[LexAI.LegalResearch.Application.Commands.IndexDocumentCommand,System.Boolean] Lifetime: Transient ImplementationType: LexAI.LegalResearch.Application.Commands.IndexDocumentCommandHandler': Unable to resolve service for type 'LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository' while attempting to activate 'LexAI.LegalResearch.Application.Commands.IndexDocumentCommandHandler'.) (Error while validating the service descriptor 'ServiceType: MediatR.IRequestHandler`2[LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommand,System.Boolean] Lifetime: Transient ImplementationType: LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommandHandler': Unable to resolve service for type 'LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository' while attempting to activate 'LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommandHandler'.) (Error while validating the service descriptor 'ServiceType: LexAI.LegalResearch.Application.Interfaces.ILegalSearchService Lifetime: Scoped ImplementationType: LexAI.LegalResearch.Infrastructure.Services.LegalSearchService': Unable to resolve service for type 'LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService' while attempting to activate 'LexAI.LegalResearch.Infrastructure.Services.LegalSearchService'.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: MediatR.IRequestHandler`2[LexAI.LegalResearch.Application.Commands.PerformSearchCommand,LexAI.LegalResearch.Application.DTOs.SearchResponseDto] Lifetime: Transient ImplementationType: LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler': Unable to resolve service for type 'LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService' while attempting to activate 'LexAI.LegalResearch.Infrastructure.Services.LegalSearchService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService' while attempting to activate 'LexAI.LegalResearch.Infrastructure.Services.LegalSearchService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API\Program.cs:line 193
 ---> (Inner Exception #1) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: MediatR.IRequestHandler`2[LexAI.LegalResearch.Application.Commands.IndexDocumentCommand,System.Boolean] Lifetime: Transient ImplementationType: LexAI.LegalResearch.Application.Commands.IndexDocumentCommandHandler': Unable to resolve service for type 'LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository' while attempting to activate 'LexAI.LegalResearch.Application.Commands.IndexDocumentCommandHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository' while attempting to activate 'LexAI.LegalResearch.Application.Commands.IndexDocumentCommandHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #2) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: MediatR.IRequestHandler`2[LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommand,System.Boolean] Lifetime: Transient ImplementationType: LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommandHandler': Unable to resolve service for type 'LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository' while attempting to activate 'LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommandHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository' while attempting to activate 'LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommandHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #3) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: LexAI.LegalResearch.Application.Interfaces.ILegalSearchService Lifetime: Scoped ImplementationType: LexAI.LegalResearch.Infrastructure.Services.LegalSearchService': Unable to resolve service for type 'LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService' while attempting to activate 'LexAI.LegalResearch.Infrastructure.Services.LegalSearchService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService' while attempting to activate 'LexAI.LegalResearch.Infrastructure.Services.LegalSearchService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

