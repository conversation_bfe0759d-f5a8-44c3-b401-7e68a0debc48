using LexAI.Shared.Domain.Common;

namespace LexAI.LegalResearch.Domain.ValueObjects;

/// <summary>
/// Represents the source of a legal document
/// </summary>
public class DocumentSource : ValueObject
{
    /// <summary>
    /// Source name (e.g., "<PERSON><PERSON>gi<PERSON><PERSON>", "EUR-Lex", "Cour de Cassation")
    /// </summary>
    public string Name { get; private set; }

    /// <summary>
    /// Source URL or identifier
    /// </summary>
    public string Url { get; private set; }

    /// <summary>
    /// Source type
    /// </summary>
    public SourceType Type { get; private set; }

    /// <summary>
    /// Source authority level
    /// </summary>
    public AuthorityLevel Authority { get; private set; }

    /// <summary>
    /// Source jurisdiction
    /// </summary>
    public string Jurisdiction { get; private set; }

    /// <summary>
    /// Source reliability score (0-1)
    /// </summary>
    public double ReliabilityScore { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private DocumentSource() 
    {
        Name = string.Empty;
        Url = string.Empty;
        Jurisdiction = string.Empty;
    }

    /// <summary>
    /// Creates a new document source
    /// </summary>
    /// <param name="name">Source name</param>
    /// <param name="url">Source URL</param>
    /// <param name="type">Source type</param>
    /// <param name="authority">Authority level</param>
    /// <param name="jurisdiction">Jurisdiction</param>
    /// <param name="reliabilityScore">Reliability score</param>
    /// <returns>New document source</returns>
    public static DocumentSource Create(
        string name,
        string url,
        SourceType type,
        AuthorityLevel authority,
        string jurisdiction,
        double reliabilityScore = 1.0)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Source name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(url))
            throw new ArgumentException("Source URL cannot be empty", nameof(url));

        if (string.IsNullOrWhiteSpace(jurisdiction))
            throw new ArgumentException("Jurisdiction cannot be empty", nameof(jurisdiction));

        if (reliabilityScore < 0.0 || reliabilityScore > 1.0)
            throw new ArgumentException("Reliability score must be between 0 and 1", nameof(reliabilityScore));

        return new DocumentSource
        {
            Name = name.Trim(),
            Url = url.Trim(),
            Type = type,
            Authority = authority,
            Jurisdiction = jurisdiction.Trim(),
            ReliabilityScore = reliabilityScore
        };
    }

    /// <summary>
    /// Creates a Légifrance source
    /// </summary>
    /// <param name="url">Document URL</param>
    /// <returns>Légifrance document source</returns>
    public static DocumentSource CreateLegifrance(string url)
    {
        return Create(
            "Légifrance",
            url,
            SourceType.Official,
            AuthorityLevel.National,
            "France",
            1.0);
    }

    /// <summary>
    /// Creates an EUR-Lex source
    /// </summary>
    /// <param name="url">Document URL</param>
    /// <returns>EUR-Lex document source</returns>
    public static DocumentSource CreateEurLex(string url)
    {
        return Create(
            "EUR-Lex",
            url,
            SourceType.Official,
            AuthorityLevel.European,
            "European Union",
            1.0);
    }

    /// <summary>
    /// Creates a court decision source
    /// </summary>
    /// <param name="courtName">Court name</param>
    /// <param name="url">Decision URL</param>
    /// <param name="jurisdiction">Jurisdiction</param>
    /// <returns>Court decision source</returns>
    public static DocumentSource CreateCourtDecision(string courtName, string url, string jurisdiction)
    {
        var authority = courtName.ToLowerInvariant() switch
        {
            var name when name.Contains("cassation") => AuthorityLevel.Supreme,
            var name when name.Contains("conseil d'état") => AuthorityLevel.Supreme,
            var name when name.Contains("cour d'appel") => AuthorityLevel.Appellate,
            var name when name.Contains("tribunal") => AuthorityLevel.FirstInstance,
            _ => AuthorityLevel.FirstInstance
        };

        return Create(
            courtName,
            url,
            SourceType.Jurisprudence,
            authority,
            jurisdiction,
            GetAuthorityReliabilityScore(authority));
    }

    /// <summary>
    /// Creates an academic source
    /// </summary>
    /// <param name="institutionName">Institution name</param>
    /// <param name="url">Document URL</param>
    /// <param name="jurisdiction">Jurisdiction</param>
    /// <returns>Academic source</returns>
    public static DocumentSource CreateAcademic(string institutionName, string url, string jurisdiction)
    {
        return Create(
            institutionName,
            url,
            SourceType.Academic,
            AuthorityLevel.Academic,
            jurisdiction,
            0.8);
    }

    /// <summary>
    /// Gets the display name for the source
    /// </summary>
    /// <returns>Display name</returns>
    public string GetDisplayName()
    {
        return $"{Name} ({Jurisdiction})";
    }

    /// <summary>
    /// Checks if the source is official
    /// </summary>
    /// <returns>True if source is official</returns>
    public bool IsOfficial()
    {
        return Type == SourceType.Official;
    }

    /// <summary>
    /// Checks if the source is highly reliable
    /// </summary>
    /// <returns>True if reliability score >= 0.8</returns>
    public bool IsHighlyReliable()
    {
        return ReliabilityScore >= 0.8;
    }

    /// <summary>
    /// Gets the authority level description
    /// </summary>
    /// <returns>Authority level description</returns>
    public string GetAuthorityDescription()
    {
        return Authority switch
        {
            AuthorityLevel.European => "Autorité européenne",
            AuthorityLevel.National => "Autorité nationale",
            AuthorityLevel.Supreme => "Juridiction suprême",
            AuthorityLevel.Appellate => "Juridiction d'appel",
            AuthorityLevel.FirstInstance => "Juridiction de première instance",
            AuthorityLevel.Administrative => "Autorité administrative",
            AuthorityLevel.Academic => "Source académique",
            AuthorityLevel.Professional => "Source professionnelle",
            _ => "Autorité inconnue"
        };
    }

    private static double GetAuthorityReliabilityScore(AuthorityLevel authority)
    {
        return authority switch
        {
            AuthorityLevel.European => 1.0,
            AuthorityLevel.National => 1.0,
            AuthorityLevel.Supreme => 0.95,
            AuthorityLevel.Appellate => 0.9,
            AuthorityLevel.FirstInstance => 0.85,
            AuthorityLevel.Administrative => 0.8,
            AuthorityLevel.Academic => 0.75,
            AuthorityLevel.Professional => 0.7,
            _ => 0.5
        };
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Name;
        yield return Url;
        yield return Type;
        yield return Authority;
        yield return Jurisdiction;
    }
}

/// <summary>
/// Source type enumeration
/// </summary>
public enum SourceType
{
    /// <summary>
    /// Official government source
    /// </summary>
    Official,

    /// <summary>
    /// Court jurisprudence
    /// </summary>
    Jurisprudence,

    /// <summary>
    /// Academic publication
    /// </summary>
    Academic,

    /// <summary>
    /// Professional publication
    /// </summary>
    Professional,

    /// <summary>
    /// News or media source
    /// </summary>
    Media,

    /// <summary>
    /// Internal document
    /// </summary>
    Internal,

    /// <summary>
    /// Other source type
    /// </summary>
    Other
}

/// <summary>
/// Authority level enumeration
/// </summary>
public enum AuthorityLevel
{
    /// <summary>
    /// European authority
    /// </summary>
    European,

    /// <summary>
    /// National authority
    /// </summary>
    National,

    /// <summary>
    /// Supreme court
    /// </summary>
    Supreme,

    /// <summary>
    /// Appellate court
    /// </summary>
    Appellate,

    /// <summary>
    /// First instance court
    /// </summary>
    FirstInstance,

    /// <summary>
    /// Administrative authority
    /// </summary>
    Administrative,

    /// <summary>
    /// Academic institution
    /// </summary>
    Academic,

    /// <summary>
    /// Professional organization
    /// </summary>
    Professional
}
