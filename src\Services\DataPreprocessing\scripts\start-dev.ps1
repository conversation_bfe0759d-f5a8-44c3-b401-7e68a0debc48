# Script de démarrage pour le développement local
param(
    #[switch]$WithAdmin = $false # Desactiver suite a l'erreur du au parametre profile passe : unknown flag: --profile (Activer les interfaces d'administration (pgAdmin, Mongo Express))
    [switch]$Build = $false,
    [switch]$Clean = $false
)

Write-Host "🚀 Démarrage du service Data Preprocessing en mode développement" -ForegroundColor Green

# Vérifier que Docker est en cours d'exécution
try {
    docker version | Out-Null
} catch {
    Write-Error "Docker n'est pas en cours d'exécution. Veuillez démarrer Docker Desktop."
    exit 1
}

# Naviguer vers le répertoire du service
$serviceDir = "src/Services/DataPreprocessing"
if (!(Test-Path $serviceDir)) {
    Write-Error "Répertoire du service non trouvé : $serviceDir"
    exit 1
}

Set-Location $serviceDir

try {
    # Nettoyer les conteneurs existants si demandé
    if ($Clean) {
        Write-Host "🧹 Nettoyage des conteneurs existants..." -ForegroundColor Yellow
        docker compose down -v --remove-orphans
        docker system prune -f
    }

    # Construire les images si demandé
    if ($Build) {
        Write-Host "🔨 Construction des images Docker..." -ForegroundColor Yellow
        docker compose build --no-cache
    }

    # Définir les profils à utiliser
    # $profiles = @()
    # if ($WithAdmin) {
    #     $profiles += "admin"
    #     Write-Host "📊 Interfaces d'administration activées" -ForegroundColor Cyan
    # }

    # Construire la commande docker compose
    $command = "docker compose up -d"
    # if ($profiles.Count -gt 0) {
    #     $profilesStr = $profiles -join ","
    #     $command += " --profile $profilesStr"
    # }

    # Démarrer les services
    Write-Host "🐳 Démarrage des conteneurs Docker..." -ForegroundColor Yellow
    Invoke-Expression $command

    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ Services démarrés avec succès !" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 Services disponibles :" -ForegroundColor Cyan
        Write-Host "  • API Data Preprocessing : http://localhost:5001" -ForegroundColor White
        Write-Host "  • Swagger UI            : http://localhost:5001/swagger" -ForegroundColor White
        Write-Host "  • Health Checks         : http://localhost:5001/health" -ForegroundColor White
        Write-Host "  • Hangfire Dashboard    : http://localhost:5001/hangfire" -ForegroundColor White
        Write-Host ""
        Write-Host "🗄️ Bases de données :" -ForegroundColor Cyan
        Write-Host "  • PostgreSQL            : localhost:5434" -ForegroundColor White
        Write-Host "  • MongoDB               : localhost:27017" -ForegroundColor White
        
        if ($WithAdmin) {
            Write-Host ""
            Write-Host "🔧 Interfaces d'administration :" -ForegroundColor Cyan
            Write-Host "  • pgAdmin               : http://localhost:5050" -ForegroundColor White
            Write-Host "    - Email: <EMAIL>" -ForegroundColor Gray
            Write-Host "    - Password: pgadmin_2024!" -ForegroundColor Gray
            Write-Host "  • Mongo Express         : http://localhost:8081" -ForegroundColor White
            Write-Host "    - Username: admin" -ForegroundColor Gray
            Write-Host "    - Password: mongoexpress_2024!" -ForegroundColor Gray
        }

        Write-Host ""
        Write-Host "📝 Commandes utiles :" -ForegroundColor Cyan
        Write-Host "  • Voir les logs         : docker compose logs -f" -ForegroundColor White
        Write-Host "  • Arrêter les services  : docker compose down" -ForegroundColor White
        Write-Host "  • Redémarrer un service : docker compose restart <service>" -ForegroundColor White
        Write-Host "  • Voir le statut        : docker compose ps" -ForegroundColor White

        # Attendre que les services soient prêts
        Write-Host ""
        Write-Host "⏳ Vérification de l'état des services..." -ForegroundColor Yellow
        
        $maxAttempts = 1
        $attempt = 0
        $allHealthy = $false

        while ($attempt -lt $maxAttempts -and -not $allHealthy) {
            $attempt++
            Start-Sleep -Seconds 2
            
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:5001/health" -UseBasicParsing -TimeoutSec 5
                if ($response.StatusCode -eq 200) {
                    $allHealthy = $true
                    Write-Host "✅ Service Data Preprocessing prêt !" -ForegroundColor Green
                }
            } catch {
                Write-Host "⏳ Tentative $attempt/$maxAttempts - En attente..." -ForegroundColor Yellow
            }
        }

        if (-not $allHealthy) {
            Write-Warning "⚠️ Le service met plus de temps que prévu à démarrer. Vérifiez les logs avec 'docker compose logs -f'"
        }

        Write-Host ""
        Write-Host "🎉 Environnement de développement prêt !" -ForegroundColor Green
        Write-Host "💡 N'oubliez pas de configurer votre clé API OpenAI dans les variables d'environnement" -ForegroundColor Yellow
    } else {
        Write-Error "❌ Erreur lors du démarrage des services"
        exit 1
    }
}
catch {
    Write-Error "❌ Erreur lors du démarrage : $_"
    exit 1
}
finally {
    # Retourner au répertoire racine
    Set-Location ../../..
}
