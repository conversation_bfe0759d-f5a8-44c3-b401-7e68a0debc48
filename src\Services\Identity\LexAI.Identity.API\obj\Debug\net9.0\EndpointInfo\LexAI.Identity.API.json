{"openapi": "3.0.1", "info": {"title": "LexAI Identity Service API", "description": "Service d'authentification et de gestion des utilisateurs pour LexAI", "contact": {"name": "LexAI Support", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Authenticates a user and returns access and refresh tokens", "requestBody": {"description": "Login credentials", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationResponseDto"}}}}, "400": {"description": "Invalid credentials or validation errors", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Authentication failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "423": {"description": "Account is locked", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Auth/refresh": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Refreshes an access token using a refresh token", "requestBody": {"description": "Refresh token request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"200": {"description": "Token refresh successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationResponseDto"}}}}, "400": {"description": "Invalid refresh token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Refresh token expired or invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Logs out a user by revoking their refresh token(s)", "parameters": [{"name": "revokeAllTokens", "in": "query", "description": "Whether to revoke all user tokens or just the current one", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Gets the current authenticated user's information", "responses": {"200": {"description": "User information retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Auth/change-password": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Changes the current user's password", "requestBody": {"description": "Password change request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "Password changed successfully", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Invalid current password or validation errors", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Auth/forgot-password": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Initiates a password reset process", "requestBody": {"description": "Forgot password request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}}}, "responses": {"200": {"description": "Password reset email sent (if email exists)", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Invalid email format", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Auth/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Resets a user's password using a reset token", "requestBody": {"description": "Password reset request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}}}, "responses": {"200": {"description": "Password reset successfully", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Invalid token or validation errors", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}}, "components": {"schemas": {"AuthenticationResponseDto": {"type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "tokenType": {"type": "string", "nullable": true}, "expiresIn": {"type": "integer", "format": "int32"}, "user": {"$ref": "#/components/schemas/UserDto"}}, "additionalProperties": false}, "ChangePasswordDto": {"type": "object", "properties": {"currentPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ForgotPasswordDto": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginDto": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "rememberMe": {"type": "boolean"}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "RefreshTokenDto": {"type": "object", "properties": {"refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResetPasswordDto": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "role": {"$ref": "#/components/schemas/UserRole"}, "isEmailVerified": {"type": "boolean"}, "isActive": {"type": "boolean"}, "isLocked": {"type": "boolean"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "preferredLanguage": {"type": "string", "nullable": true}, "timeZone": {"type": "string", "nullable": true}, "profilePictureUrl": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UserRole": {"enum": [1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}