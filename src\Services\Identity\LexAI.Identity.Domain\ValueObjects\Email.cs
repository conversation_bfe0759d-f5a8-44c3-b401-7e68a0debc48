using LexAI.Shared.Domain.Common;
using System.Text.RegularExpressions;

namespace LexAI.Identity.Domain.ValueObjects;

/// <summary>
/// Value object representing an email address
/// </summary>
public class Email : ValueObject
{
    /// <summary>
    /// The email address value
    /// </summary>
    public string Value { get; private set; }

    /// <summary>
    /// Regular expression for email validation
    /// </summary>
    private static readonly Regex EmailRegex = new(
        @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        RegexOptions.Compiled | RegexOptions.IgnoreCase);

    /// <summary>
    /// Private constructor for EF Core
    /// </summary>
    private Email(string value)
    {
        Value = value;
    }

    /// <summary>
    /// Creates a new email value object
    /// </summary>
    /// <param name="email">Email address string</param>
    /// <returns>Email value object</returns>
    /// <exception cref="ArgumentException">Thrown when email is invalid</exception>
    public static Email Create(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be empty", nameof(email));

        email = email.Trim().ToLowerInvariant();

        if (!EmailRegex.IsMatch(email))
            throw new ArgumentException("Invalid email format", nameof(email));

        if (email.Length > 254) // RFC 5321 limit
            throw new ArgumentException("Email address is too long", nameof(email));

        return new Email(email);
    }

    /// <summary>
    /// Gets the domain part of the email address
    /// </summary>
    /// <returns>Domain part of the email</returns>
    public string GetDomain()
    {
        var atIndex = Value.LastIndexOf('@');
        return atIndex >= 0 ? Value.Substring(atIndex + 1) : string.Empty;
    }

    /// <summary>
    /// Gets the local part of the email address (before @)
    /// </summary>
    /// <returns>Local part of the email</returns>
    public string GetLocalPart()
    {
        var atIndex = Value.LastIndexOf('@');
        return atIndex >= 0 ? Value.Substring(0, atIndex) : Value;
    }

    /// <summary>
    /// Checks if the email belongs to a specific domain
    /// </summary>
    /// <param name="domain">Domain to check</param>
    /// <returns>True if email belongs to the domain</returns>
    public bool BelongsToDomain(string domain)
    {
        if (string.IsNullOrWhiteSpace(domain))
            return false;

        return GetDomain().Equals(domain.Trim().ToLowerInvariant(), StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Gets the atomic values for equality comparison
    /// </summary>
    /// <returns>Collection of atomic values</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Value;
    }

    /// <summary>
    /// Implicit conversion from Email to string
    /// </summary>
    /// <param name="email">Email value object</param>
    public static implicit operator string(Email email)
    {
        return email?.Value ?? string.Empty;
    }

    /// <summary>
    /// String representation of the email
    /// </summary>
    /// <returns>Email address string</returns>
    public override string ToString()
    {
        return Value;
    }
}

/// <summary>
/// Value object representing a phone number
/// </summary>
public class PhoneNumber : ValueObject
{
    /// <summary>
    /// The phone number value
    /// </summary>
    public string Value { get; private set; }

    /// <summary>
    /// The country code
    /// </summary>
    public string CountryCode { get; private set; }

    /// <summary>
    /// Regular expression for phone number validation (international format)
    /// </summary>
    private static readonly Regex PhoneRegex = new(
        @"^\+?[1-9]\d{1,14}$",
        RegexOptions.Compiled);

    /// <summary>
    /// Private constructor for EF Core
    /// </summary>
    private PhoneNumber(string value, string countryCode)
    {
        Value = value;
        CountryCode = countryCode;
    }

    /// <summary>
    /// Creates a new phone number value object
    /// </summary>
    /// <param name="phoneNumber">Phone number string</param>
    /// <param name="countryCode">Country code (optional, defaults to +33 for France)</param>
    /// <returns>PhoneNumber value object</returns>
    /// <exception cref="ArgumentException">Thrown when phone number is invalid</exception>
    public static PhoneNumber Create(string phoneNumber, string countryCode = "+33")
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            throw new ArgumentException("Phone number cannot be empty", nameof(phoneNumber));

        // Clean the phone number (remove spaces, dashes, parentheses)
        var cleanedNumber = phoneNumber.Trim()
            .Replace(" ", "")
            .Replace("-", "")
            .Replace("(", "")
            .Replace(")", "")
            .Replace(".", "");

        // Add country code if not present
        if (!cleanedNumber.StartsWith("+"))
        {
            if (cleanedNumber.StartsWith("0"))
            {
                // Remove leading 0 for French numbers
                cleanedNumber = cleanedNumber.Substring(1);
            }
            cleanedNumber = countryCode + cleanedNumber;
        }

        if (!PhoneRegex.IsMatch(cleanedNumber))
            throw new ArgumentException("Invalid phone number format", nameof(phoneNumber));

        // Extract country code
        var extractedCountryCode = "+1"; // Default
        if (cleanedNumber.StartsWith("+"))
        {
            var match = Regex.Match(cleanedNumber, @"^\+(\d{1,3})");
            if (match.Success)
            {
                extractedCountryCode = "+" + match.Groups[1].Value;
            }
        }

        return new PhoneNumber(cleanedNumber, extractedCountryCode);
    }

    /// <summary>
    /// Gets the national format of the phone number (without country code)
    /// </summary>
    /// <returns>National format phone number</returns>
    public string GetNationalFormat()
    {
        if (Value.StartsWith(CountryCode))
        {
            var national = Value.Substring(CountryCode.Length);
            // Add leading 0 for French numbers
            if (CountryCode == "+33" && !national.StartsWith("0"))
            {
                national = "0" + national;
            }
            return national;
        }
        return Value;
    }

    /// <summary>
    /// Gets the international format of the phone number
    /// </summary>
    /// <returns>International format phone number</returns>
    public string GetInternationalFormat()
    {
        return Value;
    }

    /// <summary>
    /// Gets the atomic values for equality comparison
    /// </summary>
    /// <returns>Collection of atomic values</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Value;
        yield return CountryCode;
    }

    /// <summary>
    /// Implicit conversion from PhoneNumber to string
    /// </summary>
    /// <param name="phoneNumber">PhoneNumber value object</param>
    public static implicit operator string(PhoneNumber phoneNumber)
    {
        return phoneNumber?.Value ?? string.Empty;
    }

    /// <summary>
    /// String representation of the phone number
    /// </summary>
    /// <returns>Phone number string</returns>
    public override string ToString()
    {
        return Value;
    }
}
