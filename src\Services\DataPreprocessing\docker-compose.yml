version: '3.8'

services:
  # Service Data Preprocessing
  # datapreprocessing-api:
  #   build:
  #     context: ../../..
  #     dockerfile: src/Services/DataPreprocessing/Dockerfile
  #   container_name: lexai-datapreprocessing-api
  #   ports:
  #     - "5001:8080"
  #   environment:
  #     - ASPNETCORE_ENVIRONMENT=Development
  #     - ConnectionStrings__PostgreSql=Host=postgres;Database=data_preprocessing_db;Username=lexai_user;Password=lexai_password_2024!
  #     - ConnectionStrings__MongoDB=mongodb://lexai_admin:lexai_mongo_password_2024!@mongodb:27017/lexai_preprocessing?authSource=admin
  #     - ConnectionStrings__Hangfire=Host=postgres;Database=data_preprocessing_hangfire;Username=lexai_user;Password=lexai_password_2024!
  #     - OpenAI__ApiKey=${OPENAI_API_KEY:-your-openai-api-key-here}
  #     - Jwt__SecretKey=your-super-secret-key-that-is-at-least-32-characters-long-for-development
  #     - Storage__DocumentPath=/app/storage/documents
  #   volumes:
  #     - datapreprocessing-storage:/app/storage
  #   depends_on:
  #     - postgres
  #     - mongodb
  #   networks:
  #     - lexai-network
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 60s

  # Base de données PostgreSQL
  postgres:
    image: postgres:16-alpine
    container_name: lexai-data-preprocessing-postgres
    ports:
      - "5434:5432"
    environment:
      - POSTGRES_DB=data_preprocessing_db
      - POSTGRES_USER=lexai_user
      - POSTGRES_PASSWORD=lexai_password_2024!
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-postgres.sql:/docker-entrypoint-initdb.d/init-postgres.sql
    networks:
      - lexai-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U lexai_user -d data_preprocessing_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Base de données MongoDB
  mongodb:
    image: mongo:7
    container_name: lexai-data-preprocessing-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=lexai_admin
      - MONGO_INITDB_ROOT_PASSWORD=lexai_mongo_password_2024!
    volumes:
      - mongodb-data:/data/db
      - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js
    networks:
      - lexai-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Interface d'administration PostgreSQL (optionnel) #Note au moment de la creation du serveur il faut saisir `postgres` comme nom de serveur dans pgAdmin et `5432` comme port
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: lexai-pgadmin
    ports:
      - "5050:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=pgadmin_2024!
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - lexai-network
    restart: unless-stopped
    # profiles:
    #   - admin

  # Interface d'administration MongoDB (optionnel)
  mongo-express:
    image: mongo-express:latest
    container_name: lexai-mongo-express
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=lexai_admin
      - ME_CONFIG_MONGODB_ADMINPASSWORD=lexai_mongo_password_2024!
      - ME_CONFIG_MONGODB_URL=mongodb://lexai_admin:lexai_mongo_password_2024!@mongodb:27017/
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=mongoexpress_2024!
    depends_on:
      - mongodb
    networks:
      - lexai-network
    restart: unless-stopped
    # profiles:
    #   - admin

volumes:
  postgres-data:
    driver: local
  mongodb-data:
    driver: local
  pgadmin-data:
    driver: local
  datapreprocessing-storage:
    driver: local

networks:
  lexai-network:
    driver: bridge
