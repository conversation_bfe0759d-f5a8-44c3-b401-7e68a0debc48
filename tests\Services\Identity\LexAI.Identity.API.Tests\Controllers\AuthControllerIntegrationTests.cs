using LexAI.Identity.Application.DTOs;
using LexAI.Shared.Domain.Enums;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace LexAI.Identity.API.Tests.Controllers;

public class AuthControllerIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly JsonSerializerOptions _jsonOptions;

    public AuthControllerIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    [Fact]
    public async Task Register_WithValidData_ShouldReturnCreated()
    {
        // Arrange
        var registerDto = new RegisterUserDto
        {
            Email = $"test{Guid.NewGuid()}@example.com",
            FirstName = "John",
            LastName = "Doe",
            Password = "Password123!",
            ConfirmPassword = "Password123!",
            Role = UserRole.Client,
            PreferredLanguage = "fr-FR",
            TimeZone = "Europe/Paris",
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/register", registerDto, _jsonOptions);

        // Assert
        Assert.Equal(HttpStatusCode.Created, response.StatusCode);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        var userDto = JsonSerializer.Deserialize<UserDto>(responseContent, _jsonOptions);
        
        Assert.NotNull(userDto);
        Assert.Equal(registerDto.Email, userDto.Email);
        Assert.Equal(registerDto.FirstName, userDto.FirstName);
        Assert.Equal(registerDto.LastName, userDto.LastName);
        Assert.Equal(registerDto.Role, userDto.Role);
    }

    [Fact]
    public async Task Register_WithAdministratorRole_ShouldReturnBadRequest()
    {
        // Arrange
        var registerDto = new RegisterUserDto
        {
            Email = $"admin{Guid.NewGuid()}@example.com",
            FirstName = "Admin",
            LastName = "User",
            Password = "Password123!",
            ConfirmPassword = "Password123!",
            Role = UserRole.Administrator,
            PreferredLanguage = "fr-FR",
            TimeZone = "Europe/Paris",
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/register", registerDto, _jsonOptions);

        // Assert
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    public async Task Register_WithMismatchedPasswords_ShouldReturnBadRequest()
    {
        // Arrange
        var registerDto = new RegisterUserDto
        {
            Email = $"test{Guid.NewGuid()}@example.com",
            FirstName = "John",
            LastName = "Doe",
            Password = "Password123!",
            ConfirmPassword = "DifferentPassword123!",
            Role = UserRole.Client,
            PreferredLanguage = "fr-FR",
            TimeZone = "Europe/Paris",
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/register", registerDto, _jsonOptions);

        // Assert
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        Assert.Contains("Password and confirmation password do not match", responseContent);
    }

    [Fact]
    public async Task Register_WithInvalidEmail_ShouldReturnBadRequest()
    {
        // Arrange
        var registerDto = new RegisterUserDto
        {
            Email = "invalid-email",
            FirstName = "John",
            LastName = "Doe",
            Password = "Password123!",
            ConfirmPassword = "Password123!",
            Role = UserRole.Client,
            PreferredLanguage = "fr-FR",
            TimeZone = "Europe/Paris",
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/register", registerDto, _jsonOptions);

        // Assert
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    public async Task Register_WithWeakPassword_ShouldReturnBadRequest()
    {
        // Arrange
        var registerDto = new RegisterUserDto
        {
            Email = $"test{Guid.NewGuid()}@example.com",
            FirstName = "John",
            LastName = "Doe",
            Password = "weak",
            ConfirmPassword = "weak",
            Role = UserRole.Client,
            PreferredLanguage = "fr-FR",
            TimeZone = "Europe/Paris",
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/register", registerDto, _jsonOptions);

        // Assert
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    public async Task Register_WithoutAcceptingTerms_ShouldReturnBadRequest()
    {
        // Arrange
        var registerDto = new RegisterUserDto
        {
            Email = $"test{Guid.NewGuid()}@example.com",
            FirstName = "John",
            LastName = "Doe",
            Password = "Password123!",
            ConfirmPassword = "Password123!",
            Role = UserRole.Client,
            PreferredLanguage = "fr-FR",
            TimeZone = "Europe/Paris",
            AcceptTerms = false,
            AcceptPrivacyPolicy = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/register", registerDto, _jsonOptions);

        // Assert
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    public async Task Register_WithDuplicateEmail_ShouldReturnConflict()
    {
        // Arrange
        var email = $"duplicate{Guid.NewGuid()}@example.com";
        var registerDto = new RegisterUserDto
        {
            Email = email,
            FirstName = "John",
            LastName = "Doe",
            Password = "Password123!",
            ConfirmPassword = "Password123!",
            Role = UserRole.Client,
            PreferredLanguage = "fr-FR",
            TimeZone = "Europe/Paris",
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        // Act - Premier enregistrement
        var firstResponse = await _client.PostAsJsonAsync("/api/auth/register", registerDto, _jsonOptions);
        Assert.Equal(HttpStatusCode.Created, firstResponse.StatusCode);

        // Act - Deuxième enregistrement avec le même email
        var secondResponse = await _client.PostAsJsonAsync("/api/auth/register", registerDto, _jsonOptions);

        // Assert
        Assert.Equal(HttpStatusCode.Conflict, secondResponse.StatusCode);
    }

    [Theory]
    [InlineData(UserRole.SeniorLawyer)]
    [InlineData(UserRole.Lawyer)]
    [InlineData(UserRole.LegalAssistant)]
    [InlineData(UserRole.Client)]
    [InlineData(UserRole.Guest)]
    public async Task Register_WithValidNonAdminRoles_ShouldSucceed(UserRole role)
    {
        // Arrange
        var registerDto = new RegisterUserDto
        {
            Email = $"test{role}{Guid.NewGuid()}@example.com",
            FirstName = "John",
            LastName = "Doe",
            Password = "Password123!",
            ConfirmPassword = "Password123!",
            Role = role,
            PreferredLanguage = "fr-FR",
            TimeZone = "Europe/Paris",
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/register", registerDto, _jsonOptions);

        // Assert
        Assert.Equal(HttpStatusCode.Created, response.StatusCode);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        var userDto = JsonSerializer.Deserialize<UserDto>(responseContent, _jsonOptions);
        
        Assert.NotNull(userDto);
        Assert.Equal(role, userDto.Role);
    }

    [Fact]
    public async Task Register_WithPhoneNumber_ShouldIncludePhoneInResponse()
    {
        // Arrange
        var registerDto = new RegisterUserDto
        {
            Email = $"test{Guid.NewGuid()}@example.com",
            FirstName = "John",
            LastName = "Doe",
            PhoneNumber = "+33123456789",
            Password = "Password123!",
            ConfirmPassword = "Password123!",
            Role = UserRole.Client,
            PreferredLanguage = "fr-FR",
            TimeZone = "Europe/Paris",
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/register", registerDto, _jsonOptions);

        // Assert
        Assert.Equal(HttpStatusCode.Created, response.StatusCode);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        var userDto = JsonSerializer.Deserialize<UserDto>(responseContent, _jsonOptions);
        
        Assert.NotNull(userDto);
        Assert.Equal(registerDto.PhoneNumber, userDto.PhoneNumber);
    }

    [Fact]
    public async Task Register_WithCustomLanguageAndTimezone_ShouldSetPreferences()
    {
        // Arrange
        var registerDto = new RegisterUserDto
        {
            Email = $"test{Guid.NewGuid()}@example.com",
            FirstName = "John",
            LastName = "Doe",
            Password = "Password123!",
            ConfirmPassword = "Password123!",
            Role = UserRole.Client,
            PreferredLanguage = "en-US",
            TimeZone = "America/New_York",
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/register", registerDto, _jsonOptions);

        // Assert
        Assert.Equal(HttpStatusCode.Created, response.StatusCode);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        var userDto = JsonSerializer.Deserialize<UserDto>(responseContent, _jsonOptions);
        
        Assert.NotNull(userDto);
        Assert.Equal(registerDto.PreferredLanguage, userDto.PreferredLanguage);
        Assert.Equal(registerDto.TimeZone, userDto.TimeZone);
    }
}
