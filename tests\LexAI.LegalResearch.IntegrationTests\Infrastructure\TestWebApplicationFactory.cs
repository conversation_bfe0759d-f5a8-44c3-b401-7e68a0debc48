using LexAI.LegalResearch.Application.Interfaces;
using LexAI.LegalResearch.Infrastructure.Services;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text.Json;
using Testcontainers.MongoDb;
using Testcontainers.PostgreSql;

namespace LexAI.LegalResearch.IntegrationTests.Infrastructure;

/// <summary>
/// Test web application factory for integration tests
/// </summary>
public class TestWebApplicationFactory : WebApplicationFactory<Program>, IAsyncLifetime
{
    private readonly PostgreSqlContainer _postgresContainer;
    private readonly MongoDbContainer _mongoContainer;
    private readonly Mock<IEmbeddingService> _mockEmbeddingService;

    public TestWebApplicationFactory()
    {
        _postgresContainer = new PostgreSqlBuilder()
            .WithImage("pgvector/pgvector:pg16")
            .WithDatabase("legal_research_test_db")
            .WithUsername("test_user")
            .WithPassword("test_password")
            .WithPortBinding(5433, 5432)
            .Build();

        _mongoContainer = new MongoDbBuilder()
            .WithImage("mongo:7")
            .WithUsername("test_admin")
            .WithPassword("test_password")
            .WithPortBinding(27017, 27017)
            .Build();

        _mockEmbeddingService = new Mock<IEmbeddingService>();
        SetupMockEmbeddingService();
    }

    /// <summary>
    /// Gets the PostgreSQL connection string
    /// </summary>
    public string PostgreSqlConnectionString => _postgresContainer.GetConnectionString();

    /// <summary>
    /// Gets the MongoDB connection string
    /// </summary>
    public string MongoDbConnectionString => _mongoContainer.GetConnectionString();

    /// <summary>
    /// Gets the mock embedding service
    /// </summary>
    public Mock<IEmbeddingService> MockEmbeddingService => _mockEmbeddingService;

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureAppConfiguration((context, config) =>
        {
            config.AddJsonFile("appsettings.Test.json", optional: false, reloadOnChange: true);
            config.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["ConnectionStrings:PostgreSql"] = PostgreSqlConnectionString,
                ["ConnectionStrings:MongoDB"] = MongoDbConnectionString,
                ["OpenAI:ApiKey"] = "test-api-key",
                ["OpenAI:EmbeddingModel"] = "text-embedding-3-small",
                ["OpenAI:EmbeddingDimension"] = "1536"
            });
        });

        builder.ConfigureServices(services =>
        {
            // Remove the real embedding service and replace with mock
            var embeddingServiceDescriptor = services.SingleOrDefault(
                d => d.ServiceType == typeof(IEmbeddingService));
            if (embeddingServiceDescriptor != null)
            {
                services.Remove(embeddingServiceDescriptor);
            }

            services.AddSingleton(_mockEmbeddingService.Object);

            // Configure logging for tests
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Warning);
            });
        });

        builder.UseEnvironment("Test");
    }

    public async Task InitializeAsync()
    {
        await _postgresContainer.StartAsync();
        await _mongoContainer.StartAsync();

        // Wait a bit for containers to be fully ready
        await Task.Delay(2000);
    }

    public new async Task DisposeAsync()
    {
        await _postgresContainer.DisposeAsync();
        await _mongoContainer.DisposeAsync();
        await base.DisposeAsync();
    }

    /// <summary>
    /// Resets the test database to a clean state
    /// </summary>
    public async Task ResetDatabaseAsync()
    {
        using var scope = Services.CreateScope();
        // Add database reset logic here when repositories are implemented
        await Task.CompletedTask;
    }

    /// <summary>
    /// Seeds the test database with sample data
    /// </summary>
    public async Task SeedDatabaseAsync()
    {
        using var scope = Services.CreateScope();
        // Add database seeding logic here when repositories are implemented
        await Task.CompletedTask;
    }

    /// <summary>
    /// Creates an authenticated HTTP client with JWT token
    /// </summary>
    /// <param name="userId">User ID for the token</param>
    /// <param name="role">User role</param>
    /// <returns>Authenticated HTTP client</returns>
    public HttpClient CreateAuthenticatedClient(Guid? userId = null, string role = "Lawyer")
    {
        var client = CreateClient();
        var token = GenerateJwtToken(userId ?? Guid.NewGuid(), role);
        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        return client;
    }

    private void SetupMockEmbeddingService()
    {
        // Setup mock to return deterministic embeddings for testing
        _mockEmbeddingService
            .Setup(x => x.GenerateEmbeddingAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((string text, CancellationToken _) => GenerateMockEmbedding(text));

        _mockEmbeddingService
            .Setup(x => x.GenerateEmbeddingsAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((IEnumerable<string> texts, CancellationToken _) => 
                texts.Select(GenerateMockEmbedding).ToArray());

        _mockEmbeddingService
            .Setup(x => x.CalculateSimilarity(It.IsAny<float[]>(), It.IsAny<float[]>()))
            .Returns((float[] v1, float[] v2) => CalculateCosineSimilarity(v1, v2));

        _mockEmbeddingService
            .Setup(x => x.GetEmbeddingDimension())
            .Returns(1536);
    }

    private static float[] GenerateMockEmbedding(string text)
    {
        // Generate a deterministic embedding based on text hash
        var hash = text.GetHashCode();
        var random = new Random(hash);
        var embedding = new float[1536];
        
        for (int i = 0; i < embedding.Length; i++)
        {
            embedding[i] = (float)(random.NextDouble() * 2.0 - 1.0); // Range [-1, 1]
        }

        // Normalize the vector
        var magnitude = Math.Sqrt(embedding.Sum(x => x * x));
        for (int i = 0; i < embedding.Length; i++)
        {
            embedding[i] = (float)(embedding[i] / magnitude);
        }

        return embedding;
    }

    private static double CalculateCosineSimilarity(float[] vector1, float[] vector2)
    {
        if (vector1.Length != vector2.Length)
            return 0.0;

        double dotProduct = 0.0;
        double magnitude1 = 0.0;
        double magnitude2 = 0.0;

        for (int i = 0; i < vector1.Length; i++)
        {
            dotProduct += vector1[i] * vector2[i];
            magnitude1 += vector1[i] * vector1[i];
            magnitude2 += vector2[i] * vector2[i];
        }

        magnitude1 = Math.Sqrt(magnitude1);
        magnitude2 = Math.Sqrt(magnitude2);

        if (magnitude1 == 0.0 || magnitude2 == 0.0)
            return 0.0;

        return dotProduct / (magnitude1 * magnitude2);
    }

    private string GenerateJwtToken(Guid userId, string role)
    {
        // For testing purposes, create a simple JWT token
        // In a real implementation, you would use the same JWT configuration as the main application
        var header = Convert.ToBase64String(JsonSerializer.SerializeToUtf8Bytes(new { alg = "HS256", typ = "JWT" }));
        var payload = Convert.ToBase64String(JsonSerializer.SerializeToUtf8Bytes(new 
        { 
            sub = userId.ToString(),
            role = role,
            exp = DateTimeOffset.UtcNow.AddHours(1).ToUnixTimeSeconds()
        }));
        var signature = "test-signature";

        return $"{header}.{payload}.{signature}";
    }
}
