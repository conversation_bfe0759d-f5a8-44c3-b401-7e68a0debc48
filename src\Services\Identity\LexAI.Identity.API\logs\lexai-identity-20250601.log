2025-06-01 19:10:25.954 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 19:10:25.994 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 19:10:26.005 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 19:10:27.228 +04:00 [INF] Executed DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 19:10:27.269 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 19:10:27.329 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 19:10:27.901 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-01 19:10:27.922 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-01 19:10:28.029 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 19:10:28.031 +04:00 [INF] Hosting environment: Development
2025-06-01 19:10:28.033 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 19:10:28.847 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-01 19:10:29.024 +04:00 [INF] Request GET / started with correlation ID ac7d1b9a-96cc-4f94-89fa-7d503afba094
2025-06-01 19:10:29.150 +04:00 [INF] Request GET / completed in 118ms with status 404 (Correlation ID: ac7d1b9a-96cc-4f94-89fa-7d503afba094)
2025-06-01 19:10:29.157 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 317.5476ms
2025-06-01 19:10:29.169 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-01 19:11:39.922 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-01 19:11:39.976 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID df579d2e-2596-453e-b483-b77fd6db35a8
2025-06-01 19:11:39.986 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:11:39.995 +04:00 [INF] Request OPTIONS /api/auth/login completed in 11ms with status 204 (Correlation ID: df579d2e-2596-453e-b483-b77fd6db35a8)
2025-06-01 19:11:39.999 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 78.4253ms
2025-06-01 19:11:40.002 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-01 19:11:40.017 +04:00 [INF] Request POST /api/auth/login started with correlation ID 0d532da5-b7cd-4ffb-8e87-daafe90607c8
2025-06-01 19:11:40.026 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:11:40.032 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 19:11:40.065 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 19:11:40.105 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-01 19:11:40.202 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-01 19:11:40.546 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 19:11:40.629 +04:00 [INF] Executed DbCommand (26ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 19:11:41.104 +04:00 [INF] Executed DbCommand (16ms) [Parameters=[@p0='807e5878-3a7e-4b5d-98b9-45dd59a159db', @p1='Login' (Nullable = false), @p2='"Successful login from ::1"' (DbType = Object), @p3='2025-06-01T15:11:40.9311940Z' (DbType = DateTime), @p4=NULL, @p5=NULL (DbType = DateTime), @p6=NULL, @p7='fc4ebb70-007f-43d4-873b-c865709b364d', @p8='User' (Nullable = false), @p9=NULL, @p10='False', @p11='2025-06-01T15:11:40.9313379Z' (DbType = DateTime), @p12='2025-06-01T15:11:40.9922452Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14=NULL, @p15='fc4ebb70-007f-43d4-873b-c865709b364d' (Nullable = false), @p37='fc4ebb70-007f-43d4-873b-c865709b364d', @p16='2025-06-01T14:14:05.6522930Z' (DbType = DateTime), @p17='system', @p18=NULL (DbType = DateTime), @p19=NULL, @p20='0', @p21='Senior' (Nullable = false), @p22='True', @p23='False', @p24='False', @p25='False', @p26='2025-06-01T15:11:40.9307603Z' (Nullable = true) (DbType = DateTime), @p27='::1', @p28='Lawyer' (Nullable = false), @p29=NULL (DbType = DateTime), @p30='$2a$11$KGxzovu/BHhgqWFM3ugS0uPlQYchgI.ruAZvKRG9KKK9/ZxG2DMXy' (Nullable = false), @p31='fr-FR' (Nullable = false), @p32=NULL, @p33='Administrator' (Nullable = false), @p34='Europe/Paris' (Nullable = false), @p35='2025-06-01T15:11:40.9921726Z' (Nullable = true) (DbType = DateTime), @p36='system', @p38='798' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15)
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p16, "CreatedBy" = @p17, "DeletedAt" = @p18, "DeletedBy" = @p19, "FailedLoginAttempts" = @p20, "FirstName" = @p21, "IsActive" = @p22, "IsDeleted" = @p23, "IsEmailVerified" = @p24, "IsLocked" = @p25, "LastLoginAt" = @p26, "LastLoginIpAddress" = @p27, "LastName" = @p28, "LockedAt" = @p29, "PasswordHash" = @p30, "PreferredLanguage" = @p31, "ProfilePictureUrl" = @p32, "Role" = @p33, "TimeZone" = @p34, "UpdatedAt" = @p35, "UpdatedBy" = @p36
WHERE "Id" = @p37 AND xmin = @p38
RETURNING xmin;
2025-06-01 19:11:41.144 +04:00 [INF] User updated successfully: "fc4ebb70-007f-43d4-873b-c865709b364d"
2025-06-01 19:11:41.189 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='d25e3abb-f1df-4bd8-9142-13e9b5b9cbe3', @p1='2025-06-01T15:11:41.1834051Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-08T15:11:41.1796250Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='fBX7pxlI32iNwAJAVVNO4QnFFsMTJJLn3jZ8+Hf6HizKN9aY+P6ByjlTeCN+BmDyXJIsA9WRgirM2QX/SFWQOg==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='fc4ebb70-007f-43d4-873b-c865709b364d'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-01 19:11:41.195 +04:00 [INF] Refresh token added successfully: "d25e3abb-f1df-4bd8-9142-13e9b5b9cbe3"
2025-06-01 19:11:41.196 +04:00 [INF] User "fc4ebb70-007f-43d4-873b-c865709b364d" logged in successfully
2025-06-01 19:11:41.199 +04:00 [INF] Login successful <NAME_EMAIL>
2025-06-01 19:11:41.204 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-01 19:11:41.222 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 1150.9577ms
2025-06-01 19:11:41.226 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 19:11:41.228 +04:00 [INF] Request POST /api/auth/login completed in 1203ms with status 200 (Correlation ID: 0d532da5-b7cd-4ffb-8e87-daafe90607c8)
2025-06-01 19:11:41.240 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 200 null application/json; charset=utf-8 1238.4051ms
2025-06-01 19:43:41.478 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/users? - null null
2025-06-01 19:43:41.478 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/users? - null null
2025-06-01 19:43:41.489 +04:00 [INF] Request OPTIONS /api/users started with correlation ID 5ad0523b-170b-4125-ad71-e66f2ed06f69
2025-06-01 19:43:41.490 +04:00 [INF] Request OPTIONS /api/users started with correlation ID 9ffa6150-e567-4901-b038-0655a02a5d82
2025-06-01 19:43:41.492 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:43:41.494 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:43:41.495 +04:00 [INF] Request OPTIONS /api/users completed in 3ms with status 204 (Correlation ID: 5ad0523b-170b-4125-ad71-e66f2ed06f69)
2025-06-01 19:43:41.496 +04:00 [INF] Request OPTIONS /api/users completed in 2ms with status 204 (Correlation ID: 9ffa6150-e567-4901-b038-0655a02a5d82)
2025-06-01 19:43:41.500 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/users? - 204 null null 23.1633ms
2025-06-01 19:43:41.538 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/users? - 204 null null 90.7838ms
2025-06-01 19:43:41.538 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/api/users? - application/json null
2025-06-01 19:43:41.577 +04:00 [INF] Request GET /api/users started with correlation ID ebeb9f3c-4e06-4d1f-a970-98d777c166ba
2025-06-01 19:43:41.581 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:43:41.780 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:43:41.786 +04:00 [INF] Request GET /api/users completed in 205ms with status 404 (Correlation ID: ebeb9f3c-4e06-4d1f-a970-98d777c166ba)
2025-06-01 19:43:41.791 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/api/users? - 404 0 null 253.2824ms
2025-06-01 19:43:41.796 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/api/users? - application/json null
2025-06-01 19:43:41.801 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/api/users, Response status code: 404
2025-06-01 19:43:41.810 +04:00 [INF] Request GET /api/users started with correlation ID 7b232fe1-ba2f-454d-96a2-477770cbe727
2025-06-01 19:43:41.828 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:43:41.837 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:43:41.848 +04:00 [INF] Request GET /api/users completed in 20ms with status 404 (Correlation ID: 7b232fe1-ba2f-454d-96a2-477770cbe727)
2025-06-01 19:43:41.855 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/api/users? - 404 0 null 59.1073ms
2025-06-01 19:43:41.872 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/api/users, Response status code: 404
2025-06-01 19:44:20.801 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/users? - null null
2025-06-01 19:44:20.844 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/users? - null null
2025-06-01 19:44:20.877 +04:00 [INF] Request OPTIONS /api/users started with correlation ID 93877d3e-f74d-4101-917b-43dea729b0a6
2025-06-01 19:44:20.889 +04:00 [INF] Request OPTIONS /api/users started with correlation ID 884e6053-436b-406a-a96a-80a4ee573c18
2025-06-01 19:44:20.896 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:44:20.903 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:44:20.908 +04:00 [INF] Request OPTIONS /api/users completed in 12ms with status 204 (Correlation ID: 93877d3e-f74d-4101-917b-43dea729b0a6)
2025-06-01 19:44:20.913 +04:00 [INF] Request OPTIONS /api/users completed in 10ms with status 204 (Correlation ID: 884e6053-436b-406a-a96a-80a4ee573c18)
2025-06-01 19:44:20.926 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/users? - 204 null null 125.5206ms
2025-06-01 19:44:20.939 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/users? - 204 null null 95.3545ms
2025-06-01 19:44:20.934 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/api/users? - application/json null
2025-06-01 19:44:21.029 +04:00 [INF] Request GET /api/users started with correlation ID 0b292461-dd4b-4ea2-af71-c682555dc98d
2025-06-01 19:44:21.037 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:44:21.042 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:44:21.048 +04:00 [INF] Request GET /api/users completed in 12ms with status 404 (Correlation ID: 0b292461-dd4b-4ea2-af71-c682555dc98d)
2025-06-01 19:44:21.057 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/api/users? - 404 0 null 123.1698ms
2025-06-01 19:44:21.071 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/api/users? - application/json null
2025-06-01 19:44:21.089 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/api/users, Response status code: 404
2025-06-01 19:44:21.102 +04:00 [INF] Request GET /api/users started with correlation ID f9423120-e0da-43ad-8650-3d3c52b31d0e
2025-06-01 19:44:21.112 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:44:21.114 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:44:21.116 +04:00 [INF] Request GET /api/users completed in 4ms with status 404 (Correlation ID: f9423120-e0da-43ad-8650-3d3c52b31d0e)
2025-06-01 19:44:21.122 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/api/users? - 404 0 null 51.7309ms
2025-06-01 19:44:21.135 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/api/users, Response status code: 404
2025-06-01 19:45:15.455 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/users? - null null
2025-06-01 19:45:15.494 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/users? - null null
2025-06-01 19:45:15.512 +04:00 [INF] Request OPTIONS /api/users started with correlation ID 7fccb0aa-6de8-4959-b89b-60694a47c7e1
2025-06-01 19:45:15.521 +04:00 [INF] Request OPTIONS /api/users started with correlation ID 30eb2e90-8749-4580-81de-d6715e8417c5
2025-06-01 19:45:15.523 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:45:15.527 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:45:15.529 +04:00 [INF] Request OPTIONS /api/users completed in 5ms with status 204 (Correlation ID: 7fccb0aa-6de8-4959-b89b-60694a47c7e1)
2025-06-01 19:45:15.530 +04:00 [INF] Request OPTIONS /api/users completed in 4ms with status 204 (Correlation ID: 30eb2e90-8749-4580-81de-d6715e8417c5)
2025-06-01 19:45:15.537 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/users? - 204 null null 80.7962ms
2025-06-01 19:45:15.540 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/api/users? - application/json null
2025-06-01 19:45:15.540 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/users? - 204 null null 45.8282ms
2025-06-01 19:45:15.563 +04:00 [INF] Request GET /api/users started with correlation ID ee3eb398-e217-4562-84cc-0272cfba1d95
2025-06-01 19:45:15.575 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:45:15.577 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:45:15.579 +04:00 [INF] Request GET /api/users completed in 4ms with status 404 (Correlation ID: ee3eb398-e217-4562-84cc-0272cfba1d95)
2025-06-01 19:45:15.583 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/api/users? - 404 0 null 43.3421ms
2025-06-01 19:45:15.586 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/api/users? - application/json null
2025-06-01 19:45:15.591 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/api/users, Response status code: 404
2025-06-01 19:45:15.594 +04:00 [INF] Request GET /api/users started with correlation ID 06baeea1-cbf0-43f8-bebd-56f3474ce42b
2025-06-01 19:45:15.601 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:45:15.604 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:45:15.606 +04:00 [INF] Request GET /api/users completed in 5ms with status 404 (Correlation ID: 06baeea1-cbf0-43f8-bebd-56f3474ce42b)
2025-06-01 19:45:15.614 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/api/users? - 404 0 null 27.9047ms
2025-06-01 19:45:15.620 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/api/users, Response status code: 404
2025-06-01 19:56:17.887 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 19:56:17.929 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 19:56:17.938 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 19:56:19.425 +04:00 [INF] Executed DbCommand (70ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 19:56:19.441 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 19:56:19.475 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 19:56:19.778 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-01 19:56:19.780 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-01 19:56:19.820 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 19:56:19.822 +04:00 [INF] Hosting environment: Development
2025-06-01 19:56:19.825 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 19:56:20.346 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-01 19:56:20.471 +04:00 [INF] Request GET / started with correlation ID ecf2b6f7-f628-47f8-9130-209e624ce9be
2025-06-01 19:56:20.604 +04:00 [INF] Request GET / completed in 126ms with status 404 (Correlation ID: ecf2b6f7-f628-47f8-9130-209e624ce9be)
2025-06-01 19:56:20.613 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 277.1162ms
2025-06-01 19:56:20.622 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-01 20:13:02.608 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 20:13:02.642 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 20:13:02.654 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 20:13:03.109 +04:00 [INF] Executed DbCommand (59ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 20:13:03.121 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 20:13:03.154 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 20:13:03.489 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-01 20:13:03.491 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-01 20:13:03.557 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 20:13:03.561 +04:00 [INF] Hosting environment: Development
2025-06-01 20:13:03.563 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 20:13:04.593 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-01 20:13:04.757 +04:00 [INF] Request GET / started with correlation ID 4f417ae1-1cfb-4d74-8b9b-75021fb41a40
2025-06-01 20:13:04.949 +04:00 [INF] Request GET / completed in 185ms with status 404 (Correlation ID: 4f417ae1-1cfb-4d74-8b9b-75021fb41a40)
2025-06-01 20:13:04.965 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 394.1914ms
2025-06-01 20:13:04.974 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-01 20:17:56.014 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.html - null null
2025-06-01 20:17:56.160 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.html - 200 null text/html;charset=utf-8 146.2872ms
2025-06-01 20:17:56.242 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.js - null null
2025-06-01 20:17:56.242 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 20:17:56.255 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.js - 200 null application/javascript;charset=utf-8 13.5467ms
2025-06-01 20:17:56.282 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 40.3835ms
2025-06-01 20:17:56.338 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-06-01 20:17:56.416 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 78.3288ms
2025-06-01 20:17:56.569 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - null null
2025-06-01 20:17:56.643 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 73.3252ms
2025-06-01 20:18:04.059 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/hangfire - null null
2025-06-01 20:18:04.078 +04:00 [INF] Request GET /hangfire started with correlation ID 13de5548-00fc-4b8e-8497-e0ec59001425
2025-06-01 20:18:04.088 +04:00 [INF] Request GET /hangfire completed in 1ms with status 404 (Correlation ID: 13de5548-00fc-4b8e-8497-e0ec59001425)
2025-06-01 20:18:04.095 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/hangfire - 404 0 null 35.7355ms
2025-06-01 20:18:04.101 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/hangfire, Response status code: 404
