<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.LegalResearch.Domain</name>
    </assembly>
    <members>
        <member name="T:LexAI.LegalResearch.Domain.Entities.LegalDocument">
            <summary>
            Represents a legal document in the system
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.Title">
            <summary>
            Document title
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.Content">
            <summary>
            Document content/text
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.Summary">
            <summary>
            Document summary
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.Type">
            <summary>
            Document type (law, regulation, jurisprudence, etc.)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.Domain">
            <summary>
            Legal domain/category
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.Source">
            <summary>
            Document source information
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.Metadata">
            <summary>
            Document metadata
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.Language">
            <summary>
            Document language (ISO 639-1 code)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.PublicationDate">
            <summary>
            Document publication date
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.EffectiveDate">
            <summary>
            Document effective date
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.ExpirationDate">
            <summary>
            Document expiration date (if applicable)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.Status">
            <summary>
            Document status
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.Priority">
            <summary>
            Document priority/importance level
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.Tags">
            <summary>
            Document tags for categorization
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.Keywords">
            <summary>
            Document keywords for search
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.References">
            <summary>
            Related document references
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.Chunks">
            <summary>
            Document chunks for vector search
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.FilePath">
            <summary>
            Document file path or URL
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.FileSize">
            <summary>
            Document file size in bytes
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.FileHash">
            <summary>
            Document file hash for integrity
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.AccessCount">
            <summary>
            Number of times this document has been accessed
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.LastAccessedAt">
            <summary>
            Last access timestamp
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.IsIndexed">
            <summary>
            Document indexing status
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.LegalDocument.IndexedAt">
            <summary>
            Document indexing timestamp
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.Create(System.String,System.String,LexAI.LegalResearch.Domain.ValueObjects.DocumentType,LexAI.LegalResearch.Domain.ValueObjects.LegalDomain,LexAI.LegalResearch.Domain.ValueObjects.DocumentSource,System.String)">
            <summary>
            Creates a new legal document
            </summary>
            <param name="title">Document title</param>
            <param name="content">Document content</param>
            <param name="type">Document type</param>
            <param name="domain">Legal domain</param>
            <param name="source">Document source</param>
            <param name="createdBy">User who created the document</param>
            <returns>New legal document instance</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.UpdateContent(System.String,System.String)">
            <summary>
            Updates document content and resets indexing status
            </summary>
            <param name="content">New content</param>
            <param name="updatedBy">User who updated the document</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.UpdateMetadata(System.String,LexAI.LegalResearch.Domain.ValueObjects.DocumentType,LexAI.LegalResearch.Domain.ValueObjects.LegalDomain,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String)">
            <summary>
            Updates document metadata
            </summary>
            <param name="title">Document title</param>
            <param name="type">Document type</param>
            <param name="domain">Legal domain</param>
            <param name="publicationDate">Publication date</param>
            <param name="effectiveDate">Effective date</param>
            <param name="updatedBy">User who updated the document</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.AddTags(System.String[])">
            <summary>
            Adds tags to the document
            </summary>
            <param name="tags">Tags to add</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.RemoveTags(System.String[])">
            <summary>
            Removes tags from the document
            </summary>
            <param name="tags">Tags to remove</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.AddKeywords(System.String[])">
            <summary>
            Adds keywords to the document
            </summary>
            <param name="keywords">Keywords to add</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.AddReference(LexAI.LegalResearch.Domain.ValueObjects.DocumentReference)">
            <summary>
            Adds a document reference
            </summary>
            <param name="reference">Document reference to add</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.AddChunks(System.Collections.Generic.IEnumerable{LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk})">
            <summary>
            Adds document chunks for vector search
            </summary>
            <param name="chunks">Document chunks to add</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.MarkAsIndexed">
            <summary>
            Marks the document as indexed
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.RecordAccess">
            <summary>
            Records document access
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.UpdateStatus(LexAI.LegalResearch.Domain.ValueObjects.DocumentStatus,System.String)">
            <summary>
            Updates document status
            </summary>
            <param name="status">New status</param>
            <param name="updatedBy">User who updated the status</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.SetFileInfo(System.String,System.Int64,System.String)">
            <summary>
            Sets document file information
            </summary>
            <param name="filePath">File path or URL</param>
            <param name="fileSize">File size in bytes</param>
            <param name="fileHash">File hash for integrity</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.IsActive">
            <summary>
            Checks if the document is active and valid
            </summary>
            <returns>True if document is active</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.LegalDocument.IsExpired">
            <summary>
            Checks if the document is expired
            </summary>
            <returns>True if document is expired</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.Entities.SearchQuery">
            <summary>
            Represents a legal research search query
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.OriginalQuery">
            <summary>
            Original query text from user
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.ProcessedQuery">
            <summary>
            Processed/normalized query text
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.Intent">
            <summary>
            Query intent classification
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.DomainFilter">
            <summary>
            Legal domain filter
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.TypeFilter">
            <summary>
            Document type filter
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.DateFilter">
            <summary>
            Date range filter
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.LanguageFilter">
            <summary>
            Language filter
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.Parameters">
            <summary>
            Search parameters
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.UserId">
            <summary>
            User who performed the search
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.SessionId">
            <summary>
            Session identifier for grouping related searches
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.ExecutionTimeMs">
            <summary>
            Search execution time in milliseconds
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.ResultCount">
            <summary>
            Number of results found
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.Results">
            <summary>
            Search results
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.EmbeddingVector">
            <summary>
            Query embedding vector for similarity search
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.Status">
            <summary>
            Search status
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.ErrorMessage">
            <summary>
            Error message if search failed
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.QualityScore">
            <summary>
            Search quality score (0-1)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.IsCached">
            <summary>
            Whether the search was cached
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.CacheKey">
            <summary>
            Cache key for the search
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.Entities.SearchQuery.Feedback">
            <summary>
            User feedback on search quality
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.Create(System.String,System.Guid,System.String)">
            <summary>
            Creates a new search query
            </summary>
            <param name="originalQuery">Original query text</param>
            <param name="userId">User performing the search</param>
            <param name="sessionId">Session identifier</param>
            <returns>New search query instance</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.SetProcessedQuery(System.String)">
            <summary>
            Sets the processed query text
            </summary>
            <param name="processedQuery">Processed query text</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.SetIntent(LexAI.LegalResearch.Domain.ValueObjects.QueryIntent)">
            <summary>
            Sets the query intent
            </summary>
            <param name="intent">Query intent</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.SetFilters(System.Nullable{LexAI.LegalResearch.Domain.ValueObjects.LegalDomain},System.Nullable{LexAI.LegalResearch.Domain.ValueObjects.DocumentType},LexAI.LegalResearch.Domain.ValueObjects.DateRange,System.String)">
            <summary>
            Sets search filters
            </summary>
            <param name="domainFilter">Legal domain filter</param>
            <param name="typeFilter">Document type filter</param>
            <param name="dateFilter">Date range filter</param>
            <param name="languageFilter">Language filter</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.SetParameters(LexAI.LegalResearch.Domain.ValueObjects.SearchParameters)">
            <summary>
            Sets search parameters
            </summary>
            <param name="parameters">Search parameters</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.SetEmbeddingVector(System.Single[])">
            <summary>
            Sets the query embedding vector
            </summary>
            <param name="embeddingVector">Embedding vector</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.MarkAsStarted">
            <summary>
            Marks the search as started
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.MarkAsCompleted(System.Collections.Generic.IEnumerable{LexAI.LegalResearch.Domain.ValueObjects.SearchResult},System.Int64,System.Double,System.Boolean,System.String)">
            <summary>
            Marks the search as completed with results
            </summary>
            <param name="results">Search results</param>
            <param name="executionTimeMs">Execution time in milliseconds</param>
            <param name="qualityScore">Search quality score</param>
            <param name="isCached">Whether results were cached</param>
            <param name="cacheKey">Cache key if applicable</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.MarkAsFailed(System.String,System.Int64)">
            <summary>
            Marks the search as failed
            </summary>
            <param name="errorMessage">Error message</param>
            <param name="executionTimeMs">Execution time in milliseconds</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.AddFeedback(LexAI.LegalResearch.Domain.ValueObjects.UserFeedback)">
            <summary>
            Adds user feedback for the search
            </summary>
            <param name="feedback">User feedback</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.IsSuccessful">
            <summary>
            Checks if the search was successful
            </summary>
            <returns>True if search completed successfully</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.GetEffectivenessScore">
            <summary>
            Gets the search effectiveness score based on results and feedback
            </summary>
            <returns>Effectiveness score (0-1)</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.Entities.SearchQuery.GetTopResults(System.Int32)">
            <summary>
            Gets the top N search results
            </summary>
            <param name="count">Number of results to return</param>
            <returns>Top search results</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.DocumentType">
            <summary>
            Document type enumeration
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentType.Law">
            <summary>
            Law or statute
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentType.Regulation">
            <summary>
            Regulation or decree
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentType.Jurisprudence">
            <summary>
            Court decision or jurisprudence
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentType.Doctrine">
            <summary>
            Legal doctrine or commentary
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentType.Contract">
            <summary>
            Contract template
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentType.Form">
            <summary>
            Legal form or template
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentType.Procedure">
            <summary>
            Legal procedure or guide
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentType.Analysis">
            <summary>
            Legal analysis or opinion
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentType.News">
            <summary>
            Legal news or update
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentType.Other">
            <summary>
            Other document type
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain">
            <summary>
            Legal domain enumeration
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Civil">
            <summary>
            Civil law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Criminal">
            <summary>
            Criminal law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Administrative">
            <summary>
            Administrative law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Constitutional">
            <summary>
            Constitutional law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Commercial">
            <summary>
            Commercial and business law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Labor">
            <summary>
            Labor and employment law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Tax">
            <summary>
            Tax law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.RealEstate">
            <summary>
            Real estate law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Family">
            <summary>
            Family law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.IntellectualProperty">
            <summary>
            Intellectual property law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Environmental">
            <summary>
            Environmental law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Health">
            <summary>
            Health law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Immigration">
            <summary>
            Immigration law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.International">
            <summary>
            International law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.European">
            <summary>
            European Union law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Banking">
            <summary>
            Banking and finance law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Insurance">
            <summary>
            Insurance law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Technology">
            <summary>
            Technology and data law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Competition">
            <summary>
            Competition law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Consumer">
            <summary>
            Consumer protection law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.LegalDomain.Other">
            <summary>
            Other legal domain
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.DocumentStatus">
            <summary>
            Document status enumeration
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentStatus.Draft">
            <summary>
            Document is in draft state
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentStatus.Review">
            <summary>
            Document is under review
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentStatus.Published">
            <summary>
            Document is published and active
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentStatus.Archived">
            <summary>
            Document is archived
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentStatus.Deprecated">
            <summary>
            Document is deprecated
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentStatus.Deleted">
            <summary>
            Document is deleted
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.DocumentPriority">
            <summary>
            Document priority enumeration
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentPriority.Low">
            <summary>
            Low priority
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentPriority.Normal">
            <summary>
            Normal priority
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentPriority.High">
            <summary>
            High priority
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.DocumentPriority.Critical">
            <summary>
            Critical priority
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.QueryIntent">
            <summary>
            Query intent enumeration
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.QueryIntent.Unknown">
            <summary>
            Unknown intent
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.QueryIntent.Information">
            <summary>
            Search for specific information
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.QueryIntent.Procedure">
            <summary>
            Search for legal procedures
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.QueryIntent.Definition">
            <summary>
            Search for legal definitions
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.QueryIntent.CaseLaw">
            <summary>
            Search for case law
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.QueryIntent.Forms">
            <summary>
            Search for legal forms
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.QueryIntent.Analysis">
            <summary>
            Search for legal analysis
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.QueryIntent.Precedent">
            <summary>
            Search for legal precedents
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.QueryIntent.Updates">
            <summary>
            Search for legal updates
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.QueryIntent.Comparative">
            <summary>
            Comparative legal research
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.SearchStatus">
            <summary>
            Search status enumeration
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SearchStatus.Pending">
            <summary>
            Search is pending
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SearchStatus.InProgress">
            <summary>
            Search is in progress
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SearchStatus.Completed">
            <summary>
            Search completed successfully
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SearchStatus.Failed">
            <summary>
            Search failed
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SearchStatus.Cancelled">
            <summary>
            Search was cancelled
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SearchStatus.Timeout">
            <summary>
            Search timed out
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.SearchMethod">
            <summary>
            Search method enumeration
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SearchMethod.Keyword">
            <summary>
            Keyword-based search
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SearchMethod.Semantic">
            <summary>
            Semantic vector search
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SearchMethod.Hybrid">
            <summary>
            Hybrid search (keyword + semantic)
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SearchMethod.FullText">
            <summary>
            Full-text search
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SearchMethod.Fuzzy">
            <summary>
            Fuzzy search
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SearchMethod.Boolean">
            <summary>
            Boolean search
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.ChunkType">
            <summary>
            Chunk type enumeration
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.ChunkType.Title">
            <summary>
            Document title
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.ChunkType.Summary">
            <summary>
            Document summary
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.ChunkType.Paragraph">
            <summary>
            Document paragraph
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.ChunkType.Section">
            <summary>
            Document section
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.ChunkType.Article">
            <summary>
            Document article
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.ChunkType.Clause">
            <summary>
            Document clause
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.ChunkType.Footnote">
            <summary>
            Document footnote
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.ChunkType.Table">
            <summary>
            Document table
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.ChunkType.List">
            <summary>
            Document list
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.ChunkType.Other">
            <summary>
            Other chunk type
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.DateRange">
            <summary>
            Represents a date range for filtering
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DateRange.StartDate">
            <summary>
            Start date
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DateRange.EndDate">
            <summary>
            End date
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DateRange.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DateRange.Create(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Creates a new date range
            </summary>
            <param name="startDate">Start date</param>
            <param name="endDate">End date</param>
            <returns>New date range</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DateRange.Contains(System.DateTime)">
            <summary>
            Checks if a date falls within this range
            </summary>
            <param name="date">Date to check</param>
            <returns>True if date is within range</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DateRange.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.SearchParameters">
            <summary>
            Represents search parameters
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchParameters.Method">
            <summary>
            Search method
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchParameters.Limit">
            <summary>
            Maximum number of results
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchParameters.MinRelevanceScore">
            <summary>
            Minimum relevance score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchParameters.IncludeHighlights">
            <summary>
            Include highlights
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchParameters.IncludeSimilar">
            <summary>
            Include similar documents
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchParameters.TimeoutSeconds">
            <summary>
            Search timeout in seconds
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchParameters.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchParameters.Create(LexAI.LegalResearch.Domain.ValueObjects.SearchMethod,System.Int32,System.Double,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            Creates new search parameters
            </summary>
            <param name="method">Search method</param>
            <param name="limit">Result limit</param>
            <param name="minRelevanceScore">Minimum relevance score</param>
            <param name="includeHighlights">Include highlights</param>
            <param name="includeSimilar">Include similar documents</param>
            <param name="timeoutSeconds">Timeout in seconds</param>
            <returns>New search parameters</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchParameters.CreateDefault">
            <summary>
            Creates default search parameters
            </summary>
            <returns>Default search parameters</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchParameters.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.TextHighlight">
            <summary>
            Represents text highlight information
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.TextHighlight.Text">
            <summary>
            Highlighted text
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.TextHighlight.StartPosition">
            <summary>
            Start position in document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.TextHighlight.EndPosition">
            <summary>
            End position in document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.TextHighlight.Score">
            <summary>
            Highlight score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.TextHighlight.Type">
            <summary>
            Highlight type
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.TextHighlight.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.TextHighlight.Create(System.String,System.Int32,System.Int32,System.Double,System.String)">
            <summary>
            Creates a new text highlight
            </summary>
            <param name="text">Highlighted text</param>
            <param name="startPosition">Start position</param>
            <param name="endPosition">End position</param>
            <param name="score">Highlight score</param>
            <param name="type">Highlight type</param>
            <returns>New text highlight</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.TextHighlight.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.MatchedChunk">
            <summary>
            Represents a matched chunk in search results
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.MatchedChunk.ChunkId">
            <summary>
            Chunk ID
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.MatchedChunk.Content">
            <summary>
            Chunk content
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.MatchedChunk.Type">
            <summary>
            Chunk type
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.MatchedChunk.SimilarityScore">
            <summary>
            Similarity score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.MatchedChunk.StartPosition">
            <summary>
            Start position in document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.MatchedChunk.EndPosition">
            <summary>
            End position in document
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.MatchedChunk.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.MatchedChunk.Create(System.Guid,System.String,LexAI.LegalResearch.Domain.ValueObjects.ChunkType,System.Double,System.Int32,System.Int32)">
            <summary>
            Creates a new matched chunk
            </summary>
            <param name="chunkId">Chunk ID</param>
            <param name="content">Chunk content</param>
            <param name="type">Chunk type</param>
            <param name="similarityScore">Similarity score</param>
            <param name="startPosition">Start position</param>
            <param name="endPosition">End position</param>
            <returns>New matched chunk</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.MatchedChunk.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.UserFeedback">
            <summary>
            Represents user feedback on search results
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.UserFeedback.OverallRating">
            <summary>
            Overall rating (1-5)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.UserFeedback.RelevanceRating">
            <summary>
            Relevance rating (1-5)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.UserFeedback.CompletenessRating">
            <summary>
            Completeness rating (1-5)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.UserFeedback.UsefulnessRating">
            <summary>
            Usefulness rating (1-5)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.UserFeedback.Comments">
            <summary>
            Additional comments
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.UserFeedback.CreatedAt">
            <summary>
            Feedback timestamp
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.UserFeedback.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.UserFeedback.Create(System.Int32,System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            Creates new user feedback
            </summary>
            <param name="overallRating">Overall rating</param>
            <param name="relevanceRating">Relevance rating</param>
            <param name="completenessRating">Completeness rating</param>
            <param name="usefulnessRating">Usefulness rating</param>
            <param name="comments">Comments</param>
            <returns>New user feedback</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.UserFeedback.GetOverallRating">
            <summary>
            Gets the overall rating as a normalized score (0-1)
            </summary>
            <returns>Overall rating score</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.UserFeedback.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.DocumentMetadata">
            <summary>
            Represents document metadata
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentMetadata.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentMetadata.WordCount">
            <summary>
            Word count
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentMetadata.CharacterCount">
            <summary>
            Character count
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentMetadata.ReadingTimeMinutes">
            <summary>
            Reading time in minutes
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentMetadata.ComplexityScore">
            <summary>
            Complexity score (0-1)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentMetadata.Properties">
            <summary>
            Additional metadata properties
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentMetadata.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentMetadata.Create(System.Guid)">
            <summary>
            Creates new document metadata
            </summary>
            <param name="documentId">Document ID</param>
            <returns>New document metadata</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentMetadata.WithTextStatistics(System.String)">
            <summary>
            Updates text statistics
            </summary>
            <param name="content">Document content</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentMetadata.WithComplexityScore(System.Double)">
            <summary>
            Sets complexity score
            </summary>
            <param name="score">Complexity score</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentMetadata.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.DocumentReference">
            <summary>
            Represents a document reference
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentReference.ReferencedDocumentId">
            <summary>
            Referenced document ID
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentReference.ReferenceType">
            <summary>
            Reference type
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentReference.Description">
            <summary>
            Reference description
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentReference.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentReference.Create(System.Guid,System.String,System.String)">
            <summary>
            Creates a new document reference
            </summary>
            <param name="referencedDocumentId">Referenced document ID</param>
            <param name="referenceType">Reference type</param>
            <param name="description">Reference description</param>
            <returns>New document reference</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentReference.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk">
            <summary>
            Represents a chunk of a legal document for vector search
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.Id">
            <summary>
            Chunk unique identifier
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.DocumentId">
            <summary>
            Document ID this chunk belongs to
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.Content">
            <summary>
            Chunk content/text
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.Type">
            <summary>
            Chunk type
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.SequenceNumber">
            <summary>
            Chunk sequence number within the document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.StartPosition">
            <summary>
            Start position in the original document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.EndPosition">
            <summary>
            End position in the original document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.Length">
            <summary>
            Chunk length in characters
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.EmbeddingVector">
            <summary>
            Embedding vector for semantic search
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.Keywords">
            <summary>
            Chunk keywords for search
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.Metadata">
            <summary>
            Chunk metadata
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.CreatedAt">
            <summary>
            Chunk creation timestamp
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.Create(System.Guid,System.String,LexAI.LegalResearch.Domain.ValueObjects.ChunkType,System.Int32,System.Int32,System.Int32)">
            <summary>
            Creates a new document chunk
            </summary>
            <param name="documentId">Document ID</param>
            <param name="content">Chunk content</param>
            <param name="type">Chunk type</param>
            <param name="sequenceNumber">Sequence number</param>
            <param name="startPosition">Start position</param>
            <param name="endPosition">End position</param>
            <returns>New document chunk</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.WithEmbedding(System.Single[])">
            <summary>
            Sets the embedding vector for the chunk
            </summary>
            <param name="embeddingVector">Embedding vector</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.WithKeywords(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets keywords for the chunk
            </summary>
            <param name="keywords">Keywords</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.WithMetadata(System.String,System.Object)">
            <summary>
            Adds metadata to the chunk
            </summary>
            <param name="key">Metadata key</param>
            <param name="value">Metadata value</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.WithMetadata(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Adds multiple metadata entries
            </summary>
            <param name="metadata">Metadata dictionary</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.GetPreview(System.Int32)">
            <summary>
            Gets a preview of the chunk content
            </summary>
            <param name="maxLength">Maximum preview length</param>
            <returns>Content preview</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.HasEmbedding">
            <summary>
            Checks if the chunk has an embedding vector
            </summary>
            <returns>True if chunk has embedding</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.GetEmbeddingDimension">
            <summary>
            Gets the embedding dimension
            </summary>
            <returns>Embedding dimension</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.CalculateSimilarity(LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk)">
            <summary>
            Calculates cosine similarity with another chunk
            </summary>
            <param name="other">Other chunk</param>
            <returns>Cosine similarity score</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.CalculateSimilarity(System.Single[])">
            <summary>
            Calculates cosine similarity with a query vector
            </summary>
            <param name="queryVector">Query embedding vector</param>
            <returns>Cosine similarity score</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.ContainsKeywords(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Checks if the chunk contains any of the specified keywords
            </summary>
            <param name="searchKeywords">Keywords to search for</param>
            <returns>True if chunk contains any keyword</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.GetContextualContent(System.Int32)">
            <summary>
            Gets the chunk context (surrounding text)
            </summary>
            <param name="contextLength">Context length in characters</param>
            <returns>Chunk with context</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentChunk.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource">
            <summary>
            Represents the source of a legal document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.Name">
            <summary>
            Source name (e.g., "Légifrance", "EUR-Lex", "Cour de Cassation")
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.Url">
            <summary>
            Source URL or identifier
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.Type">
            <summary>
            Source type
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.Authority">
            <summary>
            Source authority level
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.Jurisdiction">
            <summary>
            Source jurisdiction
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.ReliabilityScore">
            <summary>
            Source reliability score (0-1)
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.Create(System.String,System.String,LexAI.LegalResearch.Domain.ValueObjects.SourceType,LexAI.LegalResearch.Domain.ValueObjects.AuthorityLevel,System.String,System.Double)">
            <summary>
            Creates a new document source
            </summary>
            <param name="name">Source name</param>
            <param name="url">Source URL</param>
            <param name="type">Source type</param>
            <param name="authority">Authority level</param>
            <param name="jurisdiction">Jurisdiction</param>
            <param name="reliabilityScore">Reliability score</param>
            <returns>New document source</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.CreateLegifrance(System.String)">
            <summary>
            Creates a Légifrance source
            </summary>
            <param name="url">Document URL</param>
            <returns>Légifrance document source</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.CreateEurLex(System.String)">
            <summary>
            Creates an EUR-Lex source
            </summary>
            <param name="url">Document URL</param>
            <returns>EUR-Lex document source</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.CreateCourtDecision(System.String,System.String,System.String)">
            <summary>
            Creates a court decision source
            </summary>
            <param name="courtName">Court name</param>
            <param name="url">Decision URL</param>
            <param name="jurisdiction">Jurisdiction</param>
            <returns>Court decision source</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.CreateAcademic(System.String,System.String,System.String)">
            <summary>
            Creates an academic source
            </summary>
            <param name="institutionName">Institution name</param>
            <param name="url">Document URL</param>
            <param name="jurisdiction">Jurisdiction</param>
            <returns>Academic source</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.GetDisplayName">
            <summary>
            Gets the display name for the source
            </summary>
            <returns>Display name</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.IsOfficial">
            <summary>
            Checks if the source is official
            </summary>
            <returns>True if source is official</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.IsHighlyReliable">
            <summary>
            Checks if the source is highly reliable
            </summary>
            <returns>True if reliability score >= 0.8</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.GetAuthorityDescription">
            <summary>
            Gets the authority level description
            </summary>
            <returns>Authority level description</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.DocumentSource.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.SourceType">
            <summary>
            Source type enumeration
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SourceType.Official">
            <summary>
            Official government source
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SourceType.Jurisprudence">
            <summary>
            Court jurisprudence
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SourceType.Academic">
            <summary>
            Academic publication
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SourceType.Professional">
            <summary>
            Professional publication
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SourceType.Media">
            <summary>
            News or media source
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SourceType.Internal">
            <summary>
            Internal document
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.SourceType.Other">
            <summary>
            Other source type
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.AuthorityLevel">
            <summary>
            Authority level enumeration
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.AuthorityLevel.European">
            <summary>
            European authority
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.AuthorityLevel.National">
            <summary>
            National authority
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.AuthorityLevel.Supreme">
            <summary>
            Supreme court
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.AuthorityLevel.Appellate">
            <summary>
            Appellate court
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.AuthorityLevel.FirstInstance">
            <summary>
            First instance court
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.AuthorityLevel.Administrative">
            <summary>
            Administrative authority
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.AuthorityLevel.Academic">
            <summary>
            Academic institution
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Domain.ValueObjects.AuthorityLevel.Professional">
            <summary>
            Professional organization
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Domain.ValueObjects.SearchResult">
            <summary>
            Represents a search result for a legal document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.Title">
            <summary>
            Document title
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.Summary">
            <summary>
            Document summary or excerpt
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.RelevanceScore">
            <summary>
            Relevance score (0-1)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.SimilarityScore">
            <summary>
            Semantic similarity score (0-1)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.KeywordScore">
            <summary>
            Keyword match score (0-1)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.DocumentType">
            <summary>
            Document type
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.LegalDomain">
            <summary>
            Legal domain
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.Source">
            <summary>
            Document source
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.Highlights">
            <summary>
            Highlighted text snippets
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.MatchedChunks">
            <summary>
            Matched chunks from the document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.PublicationDate">
            <summary>
            Document publication date
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.EffectiveDate">
            <summary>
            Document effective date
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.Tags">
            <summary>
            Document tags
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.DocumentUrl">
            <summary>
            Document URL or file path
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.Rank">
            <summary>
            Search ranking position
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.MatchExplanation">
            <summary>
            Explanation of why this document was matched
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.Create(System.Guid,System.String,System.String,System.Double,LexAI.LegalResearch.Domain.ValueObjects.DocumentType,LexAI.LegalResearch.Domain.ValueObjects.LegalDomain,LexAI.LegalResearch.Domain.ValueObjects.DocumentSource)">
            <summary>
            Creates a new search result
            </summary>
            <param name="documentId">Document ID</param>
            <param name="title">Document title</param>
            <param name="summary">Document summary</param>
            <param name="relevanceScore">Relevance score</param>
            <param name="documentType">Document type</param>
            <param name="legalDomain">Legal domain</param>
            <param name="source">Document source</param>
            <returns>New search result</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.WithSimilarityScore(System.Double)">
            <summary>
            Sets the similarity score
            </summary>
            <param name="similarityScore">Similarity score</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.WithKeywordScore(System.Double)">
            <summary>
            Sets the keyword score
            </summary>
            <param name="keywordScore">Keyword score</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.WithHighlights(System.Collections.Generic.IEnumerable{LexAI.LegalResearch.Domain.ValueObjects.TextHighlight})">
            <summary>
            Adds text highlights
            </summary>
            <param name="highlights">Text highlights</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.WithMatchedChunks(System.Collections.Generic.IEnumerable{LexAI.LegalResearch.Domain.ValueObjects.MatchedChunk})">
            <summary>
            Adds matched chunks
            </summary>
            <param name="chunks">Matched chunks</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.WithDates(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Sets document dates
            </summary>
            <param name="publicationDate">Publication date</param>
            <param name="effectiveDate">Effective date</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.WithTags(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets document tags
            </summary>
            <param name="tags">Document tags</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.WithUrl(System.String)">
            <summary>
            Sets document URL
            </summary>
            <param name="url">Document URL</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.WithRank(System.Int32)">
            <summary>
            Sets search rank
            </summary>
            <param name="rank">Search rank</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.WithExplanation(System.String)">
            <summary>
            Sets match explanation
            </summary>
            <param name="explanation">Match explanation</param>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.GetOverallScore">
            <summary>
            Gets the overall match score combining all scoring factors
            </summary>
            <returns>Overall match score</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.GetBestSnippet(System.Int32)">
            <summary>
            Gets the best text snippet for display
            </summary>
            <param name="maxLength">Maximum snippet length</param>
            <returns>Best text snippet</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.IsHighlyRelevant">
            <summary>
            Checks if the result is highly relevant
            </summary>
            <returns>True if relevance score >= 0.8</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.IsAuthoritative">
            <summary>
            Checks if the result is from an authoritative source
            </summary>
            <returns>True if source is highly reliable</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Domain.ValueObjects.SearchResult.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
    </members>
</doc>
