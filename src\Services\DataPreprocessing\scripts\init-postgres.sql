-- Script d'initialisation PostgreSQL pour le service Data Preprocessing

-- <PERSON><PERSON><PERSON> l'utilisateur pour l'application
CREATE USER lexai_user WITH PASSWORD 'lexai_password_2024!';

-- C<PERSON>er la base de données principale
CREATE DATABASE data_preprocessing_db OWNER lexai_user;

-- Créer la base de données pour Hangfire
CREATE DATABASE data_preprocessing_hangfire OWNER lexai_user;

-- Accorder tous les privilèges sur les bases de données
GRANT ALL PRIVILEGES ON DATABASE data_preprocessing_db TO lexai_user;
GRANT ALL PRIVILEGES ON DATABASE data_preprocessing_hangfire TO lexai_user;

-- Se connecter à la base de données principale pour configurer les extensions
\c data_preprocessing_db;

-- Activer l'extension UUID pour générer des UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Activer l'extension pour les types JSON avancés
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Accorder les privilèges sur le schéma public
GRANT ALL ON SCHEMA public TO lexai_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO lexai_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO lexai_user;

-- Configurer les privilèges par défaut pour les futurs objets
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO lexai_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO lexai_user;

-- Se connecter à la base de données Hangfire
\c data_preprocessing_hangfire;

-- Accorder les privilèges sur le schéma public pour Hangfire
GRANT ALL ON SCHEMA public TO lexai_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO lexai_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO lexai_user;

-- Configurer les privilèges par défaut pour les futurs objets Hangfire
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO lexai_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO lexai_user;

-- Retourner à la base de données postgres pour les logs
\c postgres;

-- Afficher un message de confirmation
SELECT 'Initialisation PostgreSQL terminée pour le service Data Preprocessing' AS message;
