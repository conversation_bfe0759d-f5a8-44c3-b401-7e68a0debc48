using LexAI.DataPreprocessing.Domain.Entities;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.Text.Json;

namespace LexAI.DataPreprocessing.Infrastructure.Data;

/// <summary>
/// Database context for data preprocessing service
/// </summary>
public class DataPreprocessingDbContext : DbContext
{
    /// <summary>
    /// Documents
    /// </summary>
    public DbSet<Document> Documents { get; set; } = null!;

    /// <summary>
    /// Document chunks
    /// </summary>
    public DbSet<DocumentChunk> DocumentChunks { get; set; } = null!;

    /// <summary>
    /// Processing steps
    /// </summary>
    public DbSet<ProcessingStep> ProcessingSteps { get; set; } = null!;

    /// <summary>
    /// Processing errors
    /// </summary>
    public DbSet<ProcessingError> ProcessingErrors { get; set; } = null!;

    /// <summary>
    /// Named entities
    /// </summary>
    public DbSet<NamedEntity> NamedEntities { get; set; } = null!;

    /// <summary>
    /// Initializes a new instance of the DataPreprocessingDbContext
    /// </summary>
    /// <param name="options">Database context options</param>
    public DataPreprocessingDbContext(DbContextOptions<DataPreprocessingDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Configures the model
    /// </summary>
    /// <param name="modelBuilder">Model builder</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure Document entity
        modelBuilder.Entity<Document>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
            entity.Property(e => e.MimeType).IsRequired().HasMaxLength(100);
            entity.Property(e => e.FileHash).IsRequired().HasMaxLength(64);
            entity.Property(e => e.StoragePath).IsRequired().HasMaxLength(500);
            entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(50);
            entity.Property(e => e.UpdatedBy).HasMaxLength(50);

            entity.Property(e => e.Status)
                .HasConversion<string>()
                .HasMaxLength(50);

            entity.Property(e => e.DetectedDomain)
                .HasConversion<string>()
                .HasMaxLength(50);

            entity.Property(e => e.ExtractedText).HasColumnType("text");
            entity.Property(e => e.VectorDatabase).HasMaxLength(100);
            entity.Property(e => e.VectorCollection).HasMaxLength(100);

            // Configure metadata as JSON
            entity.Property(e => e.Metadata)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<DocumentMetadata>(v, (JsonSerializerOptions?)null) ?? DocumentMetadata.Create(Guid.Empty, "default", 0, "application/octet-stream"))
                .HasColumnType("jsonb");

            // Configure relationships
            entity.HasMany(e => e.Chunks)
                .WithOne()
                .HasForeignKey(c => c.DocumentId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.ProcessingSteps)
                .WithOne()
                .HasForeignKey(s => s.DocumentId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.Errors)
                .WithOne()
                .HasForeignKey(e => e.DocumentId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            entity.HasIndex(e => e.FileHash).IsUnique();
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.CreatedBy);
            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => e.DetectedDomain);
        });

        // Configure DocumentChunk entity
        modelBuilder.Entity<DocumentChunk>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.Content).IsRequired().HasColumnType("text");
            entity.Property(e => e.VectorId).HasMaxLength(100);
            entity.Property(e => e.EmbeddingModel).HasMaxLength(50);

            entity.Property(e => e.Type)
                .HasConversion<string>()
                .HasMaxLength(50);

            // Configure keywords as JSON array
            entity.Property(e => e.Keywords)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
                .HasColumnType("jsonb");

            // Configure domain relevance as JSON
            entity.Property(e => e.DomainRelevance)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<LegalDomain, double>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<LegalDomain, double>())
                .HasColumnType("jsonb");

            // Configure metadata as JSON
            entity.Property(e => e.Metadata)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<ChunkMetadata>(v, (JsonSerializerOptions?)null) ?? ChunkMetadata.Create(Guid.Empty, ChunkType.Paragraph, 0, 0))
                .HasColumnType("jsonb");

            // Configure embedding vector as binary data
            entity.Property(e => e.EmbeddingVector)
                .HasConversion(
                    v => v != null ? Convert.ToBase64String(System.Runtime.InteropServices.MemoryMarshal.AsBytes(v.AsSpan()).ToArray()) : null,
                    v => v != null ? System.Runtime.InteropServices.MemoryMarshal.Cast<byte, float>(Convert.FromBase64String(v)).ToArray() : null)
                .HasColumnType("text");

            // Configure relationships
            entity.HasMany(e => e.NamedEntities)
                .WithOne()
                .HasForeignKey(ne => ne.ChunkId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            entity.HasIndex(e => e.DocumentId);
            entity.HasIndex(e => e.SequenceNumber);
            entity.HasIndex(e => e.Type);
            entity.HasIndex(e => e.IsVectorized);
            entity.HasIndex(e => e.VectorId);
        });

        // Configure ProcessingStep entity
        modelBuilder.Entity<ProcessingStep>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.StepName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.AgentName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.ErrorMessage).HasMaxLength(1000);

            // Configure metadata as JSON
            entity.Property(e => e.Metadata)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
                .HasColumnType("jsonb");

            // Indexes
            entity.HasIndex(e => e.DocumentId);
            entity.HasIndex(e => e.StepName);
            entity.HasIndex(e => e.StartedAt);
        });

        // Configure ProcessingError entity
        modelBuilder.Entity<ProcessingError>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.ErrorCode).IsRequired().HasMaxLength(50);
            entity.Property(e => e.ErrorMessage).IsRequired().HasMaxLength(1000);
            entity.Property(e => e.StepName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.AgentName).IsRequired().HasMaxLength(100);

            entity.Property(e => e.Severity)
                .HasConversion<string>()
                .HasMaxLength(20);

            // Configure metadata as JSON
            entity.Property(e => e.Metadata)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
                .HasColumnType("jsonb");

            // Indexes
            entity.HasIndex(e => e.DocumentId);
            entity.HasIndex(e => e.ErrorCode);
            entity.HasIndex(e => e.Severity);
            entity.HasIndex(e => e.OccurredAt);
        });

        // Configure NamedEntity entity
        modelBuilder.Entity<NamedEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.Text).IsRequired().HasMaxLength(500);

            entity.Property(e => e.Type)
                .HasConversion<string>()
                .HasMaxLength(50);

            // Configure metadata as JSON
            entity.Property(e => e.Metadata)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
                .HasColumnType("jsonb");

            // Indexes
            entity.HasIndex(e => e.ChunkId);
            entity.HasIndex(e => e.Type);
            entity.HasIndex(e => e.Text);
        });
    }
}
