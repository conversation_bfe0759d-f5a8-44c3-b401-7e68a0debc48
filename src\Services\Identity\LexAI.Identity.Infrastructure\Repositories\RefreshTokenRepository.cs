using LexAI.Identity.Application.Interfaces;
using LexAI.Identity.Domain.Entities;
using LexAI.Identity.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace LexAI.Identity.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for RefreshToken entity
/// </summary>
public class RefreshTokenRepository : IRefreshTokenRepository
{
    private readonly IdentityDbContext _context;
    private readonly ILogger<RefreshTokenRepository> _logger;

    /// <summary>
    /// Initializes a new instance of the RefreshTokenRepository
    /// </summary>
    /// <param name="context">Database context</param>
    /// <param name="logger">Logger</param>
    public RefreshTokenRepository(IdentityDbContext context, ILogger<RefreshTokenRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Gets a refresh token by its value
    /// </summary>
    /// <param name="token">Token value</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>RefreshToken entity or null if not found</returns>
    public async Task<RefreshToken?> GetByTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting refresh token by value");

        return await _context.RefreshTokens
            .Include(rt => rt.User)
            .FirstOrDefaultAsync(rt => rt.Token == token, cancellationToken);
    }

    /// <summary>
    /// Gets all active refresh tokens for a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of active refresh tokens</returns>
    public async Task<IEnumerable<RefreshToken>> GetActiveTokensByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting active refresh tokens for user: {UserId}", userId);

        return await _context.RefreshTokens
            .Where(rt => rt.UserId == userId && !rt.IsRevoked && rt.ExpiresAt > DateTime.UtcNow)
            .OrderByDescending(rt => rt.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Adds a new refresh token
    /// </summary>
    /// <param name="refreshToken">RefreshToken entity to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Added refresh token</returns>
    public async Task<RefreshToken> AddAsync(RefreshToken refreshToken, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Adding new refresh token for user: {UserId}", refreshToken.UserId);

        _context.RefreshTokens.Add(refreshToken);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Refresh token added successfully: {TokenId}", refreshToken.Id);
        return refreshToken;
    }

    /// <summary>
    /// Updates an existing refresh token
    /// </summary>
    /// <param name="refreshToken">RefreshToken entity to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated refresh token</returns>
    public async Task<RefreshToken> UpdateAsync(RefreshToken refreshToken, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Updating refresh token: {TokenId}", refreshToken.Id);

        _context.RefreshTokens.Update(refreshToken);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Refresh token updated successfully: {TokenId}", refreshToken.Id);
        return refreshToken;
    }

    /// <summary>
    /// Revokes all refresh tokens for a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="revokedBy">ID of the user revoking the tokens</param>
    /// <param name="reason">Reason for revocation</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of tokens revoked</returns>
    public async Task<int> RevokeAllUserTokensAsync(Guid userId, string revokedBy, string? reason = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Revoking all refresh tokens for user: {UserId}", userId);

        var activeTokens = await _context.RefreshTokens
            .Where(rt => rt.UserId == userId && !rt.IsRevoked)
            .ToListAsync(cancellationToken);

        var revokedCount = 0;
        foreach (var token in activeTokens)
        {
            token.Revoke(revokedBy, reason);
            revokedCount++;
        }

        if (revokedCount > 0)
        {
            await _context.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Revoked {Count} refresh tokens for user: {UserId}", revokedCount, userId);
        }

        return revokedCount;
    }

    /// <summary>
    /// Removes expired refresh tokens
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of tokens removed</returns>
    public async Task<int> RemoveExpiredTokensAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Removing expired refresh tokens");

        var expiredTokens = await _context.RefreshTokens
            .Where(rt => rt.ExpiresAt <= DateTime.UtcNow)
            .ToListAsync(cancellationToken);

        if (expiredTokens.Any())
        {
            _context.RefreshTokens.RemoveRange(expiredTokens);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Removed {Count} expired refresh tokens", expiredTokens.Count);
            return expiredTokens.Count;
        }

        return 0;
    }
}
