{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Ocelot": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"Redis": "localhost:6379", "PostgreSql": "Host=localhost;Database=lexai_db;Username=lexai_user;Password=lexai_password_2024!", "MongoDB": "mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_documents?authSource=admin", "RabbitMQ": "amqp://lexai_user:lexai_rabbitmq_password_2024!@localhost:5672/lexai_vhost"}, "Jwt": {"SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "LexAI", "Audience": "LexAI-Users", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "RequireHttpsMetadata": false}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "https://lexai.com", "https://app.lexai.com"]}, "RateLimiting": {"EnableGlobalRateLimit": true, "RequestsPerMinute": 100, "BurstSize": 20}, "HealthChecks": {"UI": {"EvaluationTimeInSeconds": 10, "MinimumSecondsBetweenFailureNotifications": 60}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.AspNetCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/lexai-gateway-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] [{CorrelationId}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Security": {"EnableSecurityHeaders": true, "EnableHttpsRedirection": false, "RequireHttps": false}, "Monitoring": {"EnableMetrics": true, "EnableTracing": true, "ApplicationInsights": {"InstrumentationKey": "", "EnableAdaptiveSampling": true}}}