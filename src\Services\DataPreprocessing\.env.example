# Configuration d'exemple pour le service Data Preprocessing
# Copiez ce fichier vers .env et modifiez les valeurs selon vos besoins

# =============================================================================
# CONFIGURATION GÉNÉRALE
# =============================================================================

# Environnement (Development, Staging, Production)
ASPNETCORE_ENVIRONMENT=Development

# URLs d'écoute
ASPNETCORE_URLS=http://+:8080

# =============================================================================
# BASES DE DONNÉES
# =============================================================================

# PostgreSQL - Base de données principale
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=data_preprocessing_db
POSTGRES_USER=lexai_user
POSTGRES_PASSWORD=lexai_password_2024!

# MongoDB - Stockage des vecteurs
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DATABASE=lexai_preprocessing
MONGODB_USERNAME=lexai_admin
MONGODB_PASSWORD=lexai_mongo_password_2024!

# Hangfire - Base de données pour les tâches en arrière-plan
HANGFIRE_DB=data_preprocessing_hangfire

# =============================================================================
# SÉCURITÉ ET AUTHENTIFICATION
# =============================================================================

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-key-that-is-at-least-32-characters-long-for-production
JWT_ISSUER=LexAI
JWT_AUDIENCE=LexAI-Users
JWT_EXPIRATION_MINUTES=60

# =============================================================================
# SERVICES EXTERNES
# =============================================================================

# OpenAI API
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1/
OPENAI_MAX_RETRIES=3
OPENAI_TIMEOUT_SECONDS=30

# Qdrant (optionnel)
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=

# Weaviate (optionnel)
WEAVIATE_URL=http://localhost:8080
WEAVIATE_API_KEY=

# Pinecone (optionnel)
PINECONE_API_KEY=
PINECONE_ENVIRONMENT=us-west1-gcp

# =============================================================================
# STOCKAGE
# =============================================================================

# Stockage des fichiers
STORAGE_DOCUMENT_PATH=./storage/documents
STORAGE_MAX_FILE_SIZE=104857600
STORAGE_ALLOWED_EXTENSIONS=.pdf,.docx,.doc,.txt,.html,.rtf

# =============================================================================
# TRAITEMENT
# =============================================================================

# Configuration du traitement
PROCESSING_DEFAULT_CHUNK_SIZE=1000
PROCESSING_DEFAULT_OVERLAP_SIZE=100
PROCESSING_DEFAULT_EMBEDDING_MODEL=OpenAISmall
PROCESSING_MAX_CONCURRENT=5
PROCESSING_TIMEOUT_MINUTES=30
PROCESSING_RETRY_ATTEMPTS=3

# =============================================================================
# RATE LIMITING
# =============================================================================

# Limitation du taux de requêtes
RATE_LIMITING_PERMIT_LIMIT=100
RATE_LIMITING_WINDOW_MINUTES=1
RATE_LIMITING_QUEUE_LIMIT=50

# =============================================================================
# CORS
# =============================================================================

# Origines autorisées (séparées par des virgules)
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,https://localhost:7000

# =============================================================================
# MONITORING ET LOGS
# =============================================================================

# Health Checks
HEALTH_CHECKS_ENABLED=true
HEALTH_CHECKS_DETAILED_ERRORS=true

# Hangfire Dashboard
HANGFIRE_DASHBOARD_ENABLED=true
HANGFIRE_DASHBOARD_PATH=/hangfire
HANGFIRE_WORKER_COUNT=2

# Logging
SERILOG_MINIMUM_LEVEL=Information
SERILOG_WRITE_TO_CONSOLE=true
SERILOG_WRITE_TO_FILE=true
SERILOG_FILE_PATH=logs/lexai-datapreprocessing-.log

# =============================================================================
# DÉVELOPPEMENT UNIQUEMENT
# =============================================================================

# Interfaces d'administration (développement seulement)
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=pgadmin_2024!

MONGO_EXPRESS_USERNAME=admin
MONGO_EXPRESS_PASSWORD=mongoexpress_2024!

# =============================================================================
# NOTES
# =============================================================================

# 1. Ne jamais committer le fichier .env avec de vraies valeurs de production
# 2. Utilisez des secrets managers en production (Azure Key Vault, AWS Secrets Manager, etc.)
# 3. Changez tous les mots de passe par défaut en production
# 4. Activez HTTPS en production
# 5. Configurez des certificats SSL valides
# 6. Utilisez des bases de données managées en production
