# Script pour réinitialiser complètement l'environnement de développement
param(
    [switch]$Force = $false
)

Write-Host "🔄 Réinitialisation complète de l'environnement de développement" -ForegroundColor Yellow

if (-not $Force) {
    Write-Host ""
    Write-Warning "⚠️ ATTENTION : Cette opération va supprimer TOUTES les données des bases de données !"
    Write-Host "Cela inclut :" -ForegroundColor Red
    Write-Host "  • Toutes les données PostgreSQL" -ForegroundColor Red
    Write-Host "  • Toutes les données MongoDB" -ForegroundColor Red
    Write-Host "  • Tous les volumes Docker" -ForegroundColor Red
    Write-Host "  • Tous les conteneurs du projet" -ForegroundColor Red
    Write-Host ""
    $confirmation = Read-Host "Êtes-vous sûr de vouloir continuer ? Tapez 'RESET' pour confirmer"
    
    if ($confirmation -ne "RESET") {
        Write-Host "❌ Opération annulée" -ForegroundColor Yellow
        exit 0
    }
}

# Naviguer vers le répertoire du service
$serviceDir = "src/Services/DataPreprocessing"
if (!(Test-Path $serviceDir)) {
    Write-Error "Répertoire du service non trouvé : $serviceDir"
    exit 1
}

Set-Location $serviceDir

try {
    Write-Host ""
    Write-Host "🛑 Arrêt de tous les conteneurs..." -ForegroundColor Yellow
    docker-compose down --remove-orphans

    Write-Host "🗑️ Suppression des volumes..." -ForegroundColor Yellow
    docker-compose down -v

    Write-Host "🧹 Nettoyage des images du projet..." -ForegroundColor Yellow
    $images = docker images --filter "reference=*lexai*" --filter "reference=*datapreprocessing*" -q
    if ($images) {
        docker rmi $images -f
    }

    Write-Host "🧹 Nettoyage général Docker..." -ForegroundColor Yellow
    docker system prune -f

    Write-Host "🧹 Suppression des volumes orphelins..." -ForegroundColor Yellow
    docker volume prune -f

    Write-Host ""
    Write-Host "✅ Environnement nettoyé avec succès !" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 Redémarrage avec un environnement propre..." -ForegroundColor Green
    
    # Redémarrer avec un build complet
    docker-compose up -d --build

    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ Environnement redémarré avec succès !" -ForegroundColor Green
        Write-Host ""
        Write-Host "⏳ Attente de l'initialisation des bases de données..." -ForegroundColor Yellow
        
        # Attendre que les bases de données soient prêtes
        $maxWait = 60
        $waited = 0
        
        while ($waited -lt $maxWait) {
            Start-Sleep -Seconds 5
            $waited += 5
            
            # Vérifier PostgreSQL
            $pgStatus = docker-compose exec -T postgres pg_isready -U postgres 2>$null
            $mongoStatus = docker-compose exec -T mongodb mongosh --eval "db.adminCommand('ping')" 2>$null
            
            if ($pgStatus -and $mongoStatus) {
                Write-Host "✅ Bases de données prêtes !" -ForegroundColor Green
                break
            }
            
            Write-Host "⏳ Attente... ($waited/$maxWait secondes)" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "🔍 Vérification des bases de données créées..." -ForegroundColor Cyan
        
        # Vérifier PostgreSQL
        Write-Host "📊 PostgreSQL :" -ForegroundColor Cyan
        docker-compose exec -T postgres psql -U postgres -c "\l" | Select-String "data_preprocessing"
        
        # Vérifier MongoDB
        Write-Host "📊 MongoDB :" -ForegroundColor Cyan
        docker-compose exec -T mongodb mongosh --eval "show dbs" | Select-String "lexai"
        
        Write-Host ""
        Write-Host "🎉 Environnement réinitialisé et prêt !" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 Services disponibles :" -ForegroundColor Cyan
        Write-Host "  • API Data Preprocessing : http://localhost:5001" -ForegroundColor White
        Write-Host "  • pgAdmin                : http://localhost:5050" -ForegroundColor White
        Write-Host "  • Mongo Express          : http://localhost:8081" -ForegroundColor White
        
    } else {
        Write-Error "❌ Erreur lors du redémarrage"
        exit 1
    }
}
catch {
    Write-Error "❌ Erreur lors de la réinitialisation : $_"
    exit 1
}
finally {
    # Retourner au répertoire racine
    Set-Location ../../..
}
