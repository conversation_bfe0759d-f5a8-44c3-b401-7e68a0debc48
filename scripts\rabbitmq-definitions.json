{"rabbit_version": "3.12.0", "rabbitmq_version": "3.12.0", "product_name": "RabbitMQ", "product_version": "3.12.0", "users": [{"name": "lexai_user", "password_hash": "lexai_rabbitmq_password_2024!", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": "administrator"}], "vhosts": [{"name": "lexai_vhost"}], "permissions": [{"user": "lexai_user", "vhost": "lexai_vhost", "configure": ".*", "write": ".*", "read": ".*"}], "topic_permissions": [], "parameters": [], "global_parameters": [{"name": "cluster_name", "value": "lexai-cluster"}], "policies": [], "queues": [{"name": "legal.research.queries", "vhost": "lexai_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-max-length": 10000}}, {"name": "document.analysis.requests", "vhost": "lexai_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-max-length": 5000}}, {"name": "document.generation.requests", "vhost": "lexai_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-max-length": 5000}}, {"name": "notifications.email", "vhost": "lexai_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 1800000, "x-max-length": 20000}}, {"name": "notifications.sms", "vhost": "lexai_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 1800000, "x-max-length": 10000}}, {"name": "audit.events", "vhost": "lexai_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 86400000, "x-max-length": 50000}}, {"name": "ai.assistant.conversations", "vhost": "lexai_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-max-length": 15000}}], "exchanges": [{"name": "lexai.legal.research", "vhost": "lexai_vhost", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "lexai.documents", "vhost": "lexai_vhost", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "lexai.notifications", "vhost": "lexai_vhost", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "lexai.audit", "vhost": "lexai_vhost", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "lexai.ai.assistant", "vhost": "lexai_vhost", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "bindings": [{"source": "lexai.legal.research", "vhost": "lexai_vhost", "destination": "legal.research.queries", "destination_type": "queue", "routing_key": "query.submitted", "arguments": {}}, {"source": "lexai.documents", "vhost": "lexai_vhost", "destination": "document.analysis.requests", "destination_type": "queue", "routing_key": "analysis.requested", "arguments": {}}, {"source": "lexai.documents", "vhost": "lexai_vhost", "destination": "document.generation.requests", "destination_type": "queue", "routing_key": "generation.requested", "arguments": {}}, {"source": "lexai.notifications", "vhost": "lexai_vhost", "destination": "notifications.email", "destination_type": "queue", "routing_key": "email.*", "arguments": {}}, {"source": "lexai.notifications", "vhost": "lexai_vhost", "destination": "notifications.sms", "destination_type": "queue", "routing_key": "sms.*", "arguments": {}}, {"source": "lexai.audit", "vhost": "lexai_vhost", "destination": "audit.events", "destination_type": "queue", "routing_key": "audit.*", "arguments": {}}, {"source": "lexai.ai.assistant", "vhost": "lexai_vhost", "destination": "ai.assistant.conversations", "destination_type": "queue", "routing_key": "conversation.*", "arguments": {}}]}