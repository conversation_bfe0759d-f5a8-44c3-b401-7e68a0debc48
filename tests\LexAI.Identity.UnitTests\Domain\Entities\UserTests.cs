using FluentAssertions;
using LexAI.Identity.Domain.Entities;
using LexAI.Shared.Domain.Enums;
using Xunit;

namespace LexAI.Identity.UnitTests.Domain.Entities;

/// <summary>
/// Unit tests for User entity
/// </summary>
public class UserTests
{
    [Fact]
    public void Create_WithValidData_ShouldCreateUser()
    {
        // Arrange
        var email = "<EMAIL>";
        var firstName = "John";
        var lastName = "Doe";
        var role = UserRole.Lawyer;
        var createdBy = "admin";

        // Act
        var user = User.Create(email, firstName, lastName, role, createdBy);

        // Assert
        user.Should().NotBeNull();
        user.Email.Value.Should().Be(email.ToLowerInvariant());
        user.FirstName.Should().Be(firstName);
        user.LastName.Should().Be(lastName);
        user.FullName.Should().Be($"{firstName} {lastName}");
        user.Role.Should().Be(role);
        user.CreatedBy.Should().Be(createdBy);
        user.IsActive.Should().BeTrue();
        user.IsLocked.Should().BeFalse();
        user.IsEmailVerified.Should().BeFalse();
        user.FailedLoginAttempts.Should().Be(0);
        user.Id.Should().NotBeEmpty();
        user.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Create_WithInvalidEmail_ShouldThrowException(string invalidEmail)
    {
        // Arrange
        var firstName = "John";
        var lastName = "Doe";
        var role = UserRole.Lawyer;
        var createdBy = "admin";

        // Act & Assert
        var action = () => User.Create(invalidEmail, firstName, lastName, role, createdBy);
        action.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Create_WithInvalidFirstName_ShouldThrowException(string invalidFirstName)
    {
        // Arrange
        var email = "<EMAIL>";
        var lastName = "Doe";
        var role = UserRole.Lawyer;
        var createdBy = "admin";

        // Act & Assert
        var action = () => User.Create(email, invalidFirstName, lastName, role, createdBy);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void SetPassword_WithValidPassword_ShouldSetPasswordHash()
    {
        // Arrange
        var user = CreateTestUser();
        var password = "SecurePassword123!";
        var updatedBy = "admin";

        // Act
        user.SetPassword(password, updatedBy);

        // Assert
        user.PasswordHash.Should().NotBeNullOrEmpty();
        user.PasswordHash.Should().NotBe(password); // Should be hashed
        user.UpdatedBy.Should().Be(updatedBy);
        user.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void SetPassword_WithInvalidPassword_ShouldThrowException(string invalidPassword)
    {
        // Arrange
        var user = CreateTestUser();
        var updatedBy = "admin";

        // Act & Assert
        var action = () => user.SetPassword(invalidPassword, updatedBy);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void SetPassword_WithShortPassword_ShouldThrowException()
    {
        // Arrange
        var user = CreateTestUser();
        var shortPassword = "123"; // Less than 8 characters
        var updatedBy = "admin";

        // Act & Assert
        var action = () => user.SetPassword(shortPassword, updatedBy);
        action.Should().Throw<ArgumentException>()
            .WithMessage("*at least 8 characters*");
    }

    [Fact]
    public void VerifyPassword_WithCorrectPassword_ShouldReturnTrue()
    {
        // Arrange
        var user = CreateTestUser();
        var password = "SecurePassword123!";
        user.SetPassword(password, "admin");

        // Act
        var result = user.VerifyPassword(password);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void VerifyPassword_WithIncorrectPassword_ShouldReturnFalse()
    {
        // Arrange
        var user = CreateTestUser();
        var correctPassword = "SecurePassword123!";
        var incorrectPassword = "WrongPassword123!";
        user.SetPassword(correctPassword, "admin");

        // Act
        var result = user.VerifyPassword(incorrectPassword);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void RecordSuccessfulLogin_ShouldUpdateLoginInfo()
    {
        // Arrange
        var user = CreateTestUser();
        var ipAddress = "***********";

        // Act
        user.RecordSuccessfulLogin(ipAddress);

        // Assert
        user.LastLoginAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        user.LastLoginIpAddress.Should().Be(ipAddress);
        user.FailedLoginAttempts.Should().Be(0);
        user.IsLocked.Should().BeFalse();
        user.LockedAt.Should().BeNull();
    }

    [Fact]
    public void RecordFailedLogin_ShouldIncrementFailedAttempts()
    {
        // Arrange
        var user = CreateTestUser();
        var ipAddress = "***********";

        // Act
        user.RecordFailedLogin(ipAddress);

        // Assert
        user.FailedLoginAttempts.Should().Be(1);
        user.IsLocked.Should().BeFalse(); // Not locked after 1 attempt
    }

    [Fact]
    public void RecordFailedLogin_After5Attempts_ShouldLockAccount()
    {
        // Arrange
        var user = CreateTestUser();
        var ipAddress = "***********";

        // Act - Record 5 failed attempts
        for (int i = 0; i < 5; i++)
        {
            user.RecordFailedLogin(ipAddress);
        }

        // Assert
        user.FailedLoginAttempts.Should().Be(5);
        user.IsLocked.Should().BeTrue();
        user.LockedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void VerifyEmail_ShouldSetEmailAsVerified()
    {
        // Arrange
        var user = CreateTestUser();
        var verifiedBy = "admin";

        // Act
        user.VerifyEmail(verifiedBy);

        // Assert
        user.IsEmailVerified.Should().BeTrue();
        user.UpdatedBy.Should().Be(verifiedBy);
        user.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Activate_ShouldActivateUserAndResetLock()
    {
        // Arrange
        var user = CreateTestUser();
        user.RecordFailedLogin("***********"); // Lock the account
        user.RecordFailedLogin("***********");
        user.RecordFailedLogin("***********");
        user.RecordFailedLogin("***********");
        user.RecordFailedLogin("***********"); // This should lock it
        var activatedBy = "admin";

        // Act
        user.Activate(activatedBy);

        // Assert
        user.IsActive.Should().BeTrue();
        user.IsLocked.Should().BeFalse();
        user.LockedAt.Should().BeNull();
        user.FailedLoginAttempts.Should().Be(0);
        user.UpdatedBy.Should().Be(activatedBy);
        user.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Deactivate_ShouldDeactivateUser()
    {
        // Arrange
        var user = CreateTestUser();
        var deactivatedBy = "admin";
        var reason = "Policy violation";

        // Act
        user.Deactivate(deactivatedBy, reason);

        // Assert
        user.IsActive.Should().BeFalse();
        user.UpdatedBy.Should().Be(deactivatedBy);
        user.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void UpdateProfile_WithValidData_ShouldUpdateProfile()
    {
        // Arrange
        var user = CreateTestUser();
        var newFirstName = "Jane";
        var newLastName = "Smith";
        var newPhoneNumber = "+33123456789";
        var updatedBy = "admin";

        // Act
        user.UpdateProfile(newFirstName, newLastName, newPhoneNumber, updatedBy);

        // Assert
        user.FirstName.Should().Be(newFirstName);
        user.LastName.Should().Be(newLastName);
        user.FullName.Should().Be($"{newFirstName} {newLastName}");
        user.PhoneNumber.Should().NotBeNull();
        user.PhoneNumber!.Value.Should().Be(newPhoneNumber);
        user.UpdatedBy.Should().Be(updatedBy);
        user.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void ChangeRole_ShouldUpdateRole()
    {
        // Arrange
        var user = CreateTestUser();
        var newRole = UserRole.SeniorLawyer;
        var changedBy = "admin";

        // Act
        user.ChangeRole(newRole, changedBy);

        // Assert
        user.Role.Should().Be(newRole);
        user.UpdatedBy.Should().Be(changedBy);
        user.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    private static User CreateTestUser()
    {
        return User.Create(
            "<EMAIL>",
            "John",
            "Doe",
            UserRole.Lawyer,
            "admin");
    }
}
