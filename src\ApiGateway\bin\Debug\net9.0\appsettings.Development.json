{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Ocelot": "Debug"}}, "ConnectionStrings": {"Redis": "localhost:6379", "PostgreSql": "Host=localhost;Port=5433;Database=lexai_db;Username=lexai_user;Password=lexai_password_2024!", "MongoDB": "mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_documents?authSource=admin", "RabbitMQ": "amqp://lexai_user:lexai_rabbitmq_password_2024!@localhost:5672/lexai_vhost"}, "Jwt": {"RequireHttpsMetadata": false, "ValidateIssuer": false, "ValidateAudience": false}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://localhost:4200", "http://localhost:5173"]}, "RateLimiting": {"EnableGlobalRateLimit": false, "RequestsPerMinute": 1000}, "Security": {"EnableSecurityHeaders": false, "EnableHttpsRedirection": false, "RequireHttps": false}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information", "Microsoft.AspNetCore": "Information"}}}, "Monitoring": {"EnableMetrics": true, "EnableTracing": false}}