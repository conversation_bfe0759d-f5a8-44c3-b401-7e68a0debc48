using LexAI.Identity.Application.Interfaces;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace LexAI.Identity.Infrastructure.Services;

/// <summary>
/// Service implementation for password operations
/// </summary>
public class PasswordService : IPasswordService
{
    private static readonly Regex UppercaseRegex = new(@"[A-Z]", RegexOptions.Compiled);
    private static readonly Regex LowercaseRegex = new(@"[a-z]", RegexOptions.Compiled);
    private static readonly Regex DigitRegex = new(@"\d", RegexOptions.Compiled);
    private static readonly Regex SpecialCharRegex = new(@"[@$!%*?&]", RegexOptions.Compiled);
    private static readonly Regex CommonPatternsRegex = new(@"(123|abc|qwe|password|admin)", RegexOptions.Compiled | RegexOptions.IgnoreCase);

    /// <summary>
    /// Hashes a password using BCrypt
    /// </summary>
    /// <param name="password">Plain text password</param>
    /// <returns>Hashed password</returns>
    public string HashPassword(string password)
    {
        if (string.IsNullOrWhiteSpace(password))
            throw new ArgumentException("Password cannot be null or empty", nameof(password));

        return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
    }

    /// <summary>
    /// Verifies a password against its hash
    /// </summary>
    /// <param name="password">Plain text password</param>
    /// <param name="hash">Hashed password</param>
    /// <returns>True if password matches hash</returns>
    public bool VerifyPassword(string password, string hash)
    {
        if (string.IsNullOrWhiteSpace(password) || string.IsNullOrWhiteSpace(hash))
            return false;

        try
        {
            return BCrypt.Net.BCrypt.Verify(password, hash);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Generates a secure random password
    /// </summary>
    /// <param name="length">Password length (minimum 8)</param>
    /// <param name="includeSpecialChars">Include special characters</param>
    /// <returns>Generated password</returns>
    public string GeneratePassword(int length = 12, bool includeSpecialChars = true)
    {
        if (length < 8)
            throw new ArgumentException("Password length must be at least 8 characters", nameof(length));

        const string lowercase = "abcdefghijklmnopqrstuvwxyz";
        const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const string digits = "0123456789";
        const string specialChars = "@$!%*?&";

        var allChars = lowercase + uppercase + digits;
        if (includeSpecialChars)
            allChars += specialChars;

        var password = new StringBuilder();
        using var rng = RandomNumberGenerator.Create();

        // Ensure at least one character from each required category
        password.Append(GetRandomChar(lowercase, rng));
        password.Append(GetRandomChar(uppercase, rng));
        password.Append(GetRandomChar(digits, rng));
        
        if (includeSpecialChars)
            password.Append(GetRandomChar(specialChars, rng));

        // Fill the rest with random characters
        var remainingLength = length - password.Length;
        for (int i = 0; i < remainingLength; i++)
        {
            password.Append(GetRandomChar(allChars, rng));
        }

        // Shuffle the password to avoid predictable patterns
        return ShuffleString(password.ToString(), rng);
    }

    /// <summary>
    /// Validates password strength
    /// </summary>
    /// <param name="password">Password to validate</param>
    /// <returns>Password validation result</returns>
    public PasswordValidationResult ValidatePasswordStrength(string password)
    {
        var result = new PasswordValidationResult();

        if (string.IsNullOrWhiteSpace(password))
        {
            result.IsValid = false;
            result.Score = 0;
            result.Strength = PasswordStrength.VeryWeak;
            result.Errors.Add("Password is required");
            return result;
        }

        var score = 0;
        var errors = new List<string>();
        var suggestions = new List<string>();

        // Length check
        if (password.Length < 8)
        {
            errors.Add("Password must be at least 8 characters long");
        }
        else if (password.Length >= 8)
        {
            score += 10;
            if (password.Length >= 12) score += 10;
            if (password.Length >= 16) score += 10;
        }

        // Character variety checks
        if (!LowercaseRegex.IsMatch(password))
        {
            errors.Add("Password must contain at least one lowercase letter");
            suggestions.Add("Add lowercase letters (a-z)");
        }
        else
        {
            score += 15;
        }

        if (!UppercaseRegex.IsMatch(password))
        {
            errors.Add("Password must contain at least one uppercase letter");
            suggestions.Add("Add uppercase letters (A-Z)");
        }
        else
        {
            score += 15;
        }

        if (!DigitRegex.IsMatch(password))
        {
            errors.Add("Password must contain at least one digit");
            suggestions.Add("Add numbers (0-9)");
        }
        else
        {
            score += 15;
        }

        if (!SpecialCharRegex.IsMatch(password))
        {
            errors.Add("Password must contain at least one special character (@$!%*?&)");
            suggestions.Add("Add special characters (@$!%*?&)");
        }
        else
        {
            score += 15;
        }

        // Common patterns check
        if (CommonPatternsRegex.IsMatch(password))
        {
            score -= 20;
            suggestions.Add("Avoid common patterns like '123', 'abc', 'password'");
        }

        // Repetitive characters check
        if (HasRepetitiveCharacters(password))
        {
            score -= 10;
            suggestions.Add("Avoid repetitive characters");
        }

        // Sequential characters check
        if (HasSequentialCharacters(password))
        {
            score -= 10;
            suggestions.Add("Avoid sequential characters like 'abcd' or '1234'");
        }

        // Character variety bonus
        var uniqueChars = password.Distinct().Count();
        if (uniqueChars >= password.Length * 0.7)
        {
            score += 10;
        }

        // Ensure score is within bounds
        score = Math.Max(0, Math.Min(100, score));

        result.Score = score;
        result.IsValid = errors.Count == 0 && score >= 60;
        result.Errors = errors;
        result.Suggestions = suggestions;
        result.Strength = GetPasswordStrength(score);

        return result;
    }

    /// <summary>
    /// Generates a password reset token
    /// </summary>
    /// <returns>Password reset token</returns>
    public string GeneratePasswordResetToken()
    {
        return GenerateSecureToken(32);
    }

    /// <summary>
    /// Generates an email verification token
    /// </summary>
    /// <returns>Email verification token</returns>
    public string GenerateEmailVerificationToken()
    {
        return GenerateSecureToken(32);
    }

    private static char GetRandomChar(string chars, RandomNumberGenerator rng)
    {
        var randomBytes = new byte[4];
        rng.GetBytes(randomBytes);
        var randomIndex = Math.Abs(BitConverter.ToInt32(randomBytes, 0)) % chars.Length;
        return chars[randomIndex];
    }

    private static string ShuffleString(string input, RandomNumberGenerator rng)
    {
        var array = input.ToCharArray();
        for (int i = array.Length - 1; i > 0; i--)
        {
            var randomBytes = new byte[4];
            rng.GetBytes(randomBytes);
            var j = Math.Abs(BitConverter.ToInt32(randomBytes, 0)) % (i + 1);
            (array[i], array[j]) = (array[j], array[i]);
        }
        return new string(array);
    }

    private static bool HasRepetitiveCharacters(string password)
    {
        for (int i = 0; i < password.Length - 2; i++)
        {
            if (password[i] == password[i + 1] && password[i + 1] == password[i + 2])
            {
                return true;
            }
        }
        return false;
    }

    private static bool HasSequentialCharacters(string password)
    {
        for (int i = 0; i < password.Length - 2; i++)
        {
            var char1 = password[i];
            var char2 = password[i + 1];
            var char3 = password[i + 2];

            if (char2 == char1 + 1 && char3 == char2 + 1)
            {
                return true;
            }
        }
        return false;
    }

    private static PasswordStrength GetPasswordStrength(int score)
    {
        return score switch
        {
            >= 90 => PasswordStrength.VeryStrong,
            >= 75 => PasswordStrength.Strong,
            >= 60 => PasswordStrength.Good,
            >= 40 => PasswordStrength.Fair,
            >= 20 => PasswordStrength.Weak,
            _ => PasswordStrength.VeryWeak
        };
    }

    private static string GenerateSecureToken(int length)
    {
        using var rng = RandomNumberGenerator.Create();
        var tokenBytes = new byte[length];
        rng.GetBytes(tokenBytes);
        return Convert.ToBase64String(tokenBytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }
}
