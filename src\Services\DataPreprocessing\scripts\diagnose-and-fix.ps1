# Script de diagnostic et correction pour le service Data Preprocessing
Write-Host "🔍 Diagnostic et correction du service Data Preprocessing" -ForegroundColor Green

# Naviguer vers le répertoire du service
$serviceDir = "src/Services/DataPreprocessing"
if (!(Test-Path $serviceDir)) {
    Write-Error "Répertoire du service non trouvé : $serviceDir"
    exit 1
}

Set-Location $serviceDir

try {
    Write-Host ""
    Write-Host "📋 Étape 1: Vérification de l'état des conteneurs" -ForegroundColor Cyan
    docker-compose ps

    Write-Host ""
    Write-Host "📋 Étape 2: Arrêt et nettoyage complet" -ForegroundColor Cyan
    docker-compose down -v --remove-orphans
    docker system prune -f

    Write-Host ""
    Write-Host "📋 Étape 3: Redémarrage avec configuration corrigée" -ForegroundColor Cyan
    docker-compose up -d --build

    Write-Host ""
    Write-Host "📋 Étape 4: Attente de l'initialisation des services" -ForegroundColor Cyan
    Start-Sleep -Seconds 15

    Write-Host ""
    Write-Host "📋 Étape 5: Vérification PostgreSQL" -ForegroundColor Cyan
    
    # Attendre que PostgreSQL soit prêt
    $maxWait = 60
    $waited = 0
    
    Write-Host "⏳ Attente de PostgreSQL..." -ForegroundColor Yellow
    while ($waited -lt $maxWait) {
        $pgReady = docker-compose exec -T postgres pg_isready -U postgres 2>$null
        if ($pgReady -match "accepting connections") {
            Write-Host "✅ PostgreSQL prêt" -ForegroundColor Green
            break
        }
        Start-Sleep -Seconds 3
        $waited += 3
        Write-Host "⏳ Attente... ($waited/$maxWait secondes)" -ForegroundColor Yellow
    }
    
    if ($waited -ge $maxWait) {
        Write-Error "❌ PostgreSQL n'est pas prêt après $maxWait secondes"
        Write-Host "📋 Logs PostgreSQL :" -ForegroundColor Red
        docker-compose logs postgres
        exit 1
    }

    Write-Host ""
    Write-Host "📋 Étape 6: Vérification des bases de données" -ForegroundColor Cyan
    
    # Lister les bases de données
    Write-Host "🔍 Bases de données existantes :" -ForegroundColor Yellow
    docker-compose exec -T postgres psql -U postgres -c "\l"
    
    Write-Host ""
    Write-Host "🔍 Utilisateurs existants :" -ForegroundColor Yellow
    docker-compose exec -T postgres psql -U postgres -c "\du"

    Write-Host ""
    Write-Host "📋 Étape 7: Test de connexion avec lexai_user" -ForegroundColor Cyan
    
    # Tester la connexion avec lexai_user
    $testConnection = docker-compose exec -T postgres psql -U lexai_user -d data_preprocessing_db -c "SELECT current_user, current_database();" 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Connexion lexai_user réussie" -ForegroundColor Green
        Write-Host $testConnection -ForegroundColor Gray
    } else {
        Write-Host "❌ Connexion lexai_user échouée" -ForegroundColor Red
        Write-Host $testConnection -ForegroundColor Red
        
        Write-Host ""
        Write-Host "🔧 Correction manuelle de l'utilisateur..." -ForegroundColor Yellow
        
        # Créer/corriger l'utilisateur manuellement
        $fixUserSQL = @"
-- Créer ou modifier l'utilisateur
DO `$`$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'lexai_user') THEN
        CREATE USER lexai_user WITH PASSWORD 'lexai_password_2024!';
        RAISE NOTICE 'Utilisateur lexai_user créé';
    ELSE
        ALTER USER lexai_user WITH PASSWORD 'lexai_password_2024!';
        RAISE NOTICE 'Mot de passe lexai_user mis à jour';
    END IF;
END
`$`$;

-- Créer les bases de données si elles n'existent pas
SELECT 'CREATE DATABASE data_preprocessing_db OWNER lexai_user'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'data_preprocessing_db')\gexec

SELECT 'CREATE DATABASE data_preprocessing_hangfire OWNER lexai_user'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'data_preprocessing_hangfire')\gexec

-- Accorder les privilèges
GRANT ALL PRIVILEGES ON DATABASE data_preprocessing_db TO lexai_user;
GRANT ALL PRIVILEGES ON DATABASE data_preprocessing_hangfire TO lexai_user;
"@
        
        $result = echo $fixUserSQL | docker-compose exec -T postgres psql -U postgres -d postgres 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Utilisateur corrigé avec succès" -ForegroundColor Green
            
            # Re-tester la connexion
            $testConnection2 = docker-compose exec -T postgres psql -U lexai_user -d data_preprocessing_db -c "SELECT current_user, current_database();" 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Connexion lexai_user maintenant réussie !" -ForegroundColor Green
            } else {
                Write-Host "❌ Connexion lexai_user toujours en échec" -ForegroundColor Red
                Write-Host $testConnection2 -ForegroundColor Red
            }
        } else {
            Write-Host "❌ Erreur lors de la correction de l'utilisateur" -ForegroundColor Red
            Write-Host $result -ForegroundColor Red
        }
    }

    Write-Host ""
    Write-Host "📋 Étape 8: Vérification MongoDB" -ForegroundColor Cyan
    
    # Attendre que MongoDB soit prêt
    $maxWait = 30
    $waited = 0
    
    Write-Host "⏳ Attente de MongoDB..." -ForegroundColor Yellow
    while ($waited -lt $maxWait) {
        $mongoReady = docker-compose exec -T mongodb mongosh --eval "db.adminCommand('ping')" 2>$null
        if ($mongoReady -match "ok.*1") {
            Write-Host "✅ MongoDB prêt" -ForegroundColor Green
            break
        }
        Start-Sleep -Seconds 2
        $waited += 2
    }
    
    # Vérifier les bases MongoDB
    Write-Host "🔍 Bases MongoDB :" -ForegroundColor Yellow
    docker-compose exec -T mongodb mongosh --eval "show dbs"

    Write-Host ""
    Write-Host "📋 Étape 9: Test final du service .NET" -ForegroundColor Cyan
    
    Write-Host "🚀 Tentative de démarrage du service .NET..." -ForegroundColor Yellow
    Write-Host "Si le service démarre sans erreur, le problème est résolu !" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Commandes pour tester :" -ForegroundColor Cyan
    Write-Host "  • Démarrer le service : dotnet run --project LexAI.DataPreprocessing.API" -ForegroundColor White
    Write-Host "  • Ou via Docker : docker-compose up datapreprocessing-api" -ForegroundColor White
    Write-Host "  • Vérifier la santé : curl http://localhost:5001/health" -ForegroundColor White
    Write-Host ""
    Write-Host "📋 Interfaces disponibles :" -ForegroundColor Cyan
    Write-Host "  • pgAdmin : http://localhost:5050" -ForegroundColor White
    Write-Host "    - Email: <EMAIL>" -ForegroundColor Gray
    Write-Host "    - Password: pgadmin_2024!" -ForegroundColor Gray
    Write-Host "    - Serveur PostgreSQL: postgres (port 5432 dans le conteneur)" -ForegroundColor Gray
    Write-Host "  • Mongo Express : http://localhost:8081" -ForegroundColor White
    Write-Host "    - Username: admin" -ForegroundColor Gray
    Write-Host "    - Password: mongoexpress_2024!" -ForegroundColor Gray

    Write-Host ""
    Write-Host "🎉 Diagnostic et correction terminés !" -ForegroundColor Green

}
catch {
    Write-Error "❌ Erreur lors du diagnostic : $_"
    exit 1
}
finally {
    # Retourner au répertoire racine
    Set-Location ../../..
}
