using LexAI.Shared.Domain.Common;

namespace LexAI.DataPreprocessing.Domain.ValueObjects;

/// <summary>
/// Represents a step in the document processing pipeline
/// </summary>
public class ProcessingStep : ValueObject
{
    /// <summary>
    /// Step name
    /// </summary>
    public string StepName { get; private set; }

    /// <summary>
    /// Step description
    /// </summary>
    public string Description { get; private set; }

    /// <summary>
    /// When the step started
    /// </summary>
    public DateTime StartedAt { get; private set; }

    /// <summary>
    /// When the step completed (null if still running)
    /// </summary>
    public DateTime? CompletedAt { get; private set; }

    /// <summary>
    /// Step duration
    /// </summary>
    public TimeSpan? Duration { get; private set; }

    /// <summary>
    /// Whether the step was successful (null if still running)
    /// </summary>
    public bool? IsSuccessful { get; private set; }

    /// <summary>
    /// Error message if step failed
    /// </summary>
    public string? ErrorMessage { get; private set; }

    /// <summary>
    /// Additional metadata for the step
    /// </summary>
    public Dictionary<string, object> Metadata { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private ProcessingStep()
    {
        StepName = string.Empty;
        Description = string.Empty;
        Metadata = new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates a new processing step
    /// </summary>
    /// <param name="stepName">Step name</param>
    /// <param name="description">Step description</param>
    /// <param name="isSuccessful">Whether the step was successful (null if still running)</param>
    /// <returns>New processing step</returns>
    public static ProcessingStep Create(string stepName, string description, bool? isSuccessful = null)
    {
        if (string.IsNullOrWhiteSpace(stepName))
            throw new ArgumentException("Step name cannot be empty", nameof(stepName));

        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty", nameof(description));

        var step = new ProcessingStep
        {
            StepName = stepName.Trim(),
            Description = description.Trim(),
            StartedAt = DateTime.UtcNow,
            IsSuccessful = isSuccessful,
            Metadata = new Dictionary<string, object>()
        };

        if (isSuccessful.HasValue)
        {
            step.CompletedAt = DateTime.UtcNow;
            step.Duration = TimeSpan.Zero;
        }

        return step;
    }

    /// <summary>
    /// Marks the step as completed successfully
    /// </summary>
    /// <param name="metadata">Optional metadata</param>
    /// <returns>Updated processing step</returns>
    public ProcessingStep MarkAsCompleted(Dictionary<string, object>? metadata = null)
    {
        if (IsSuccessful.HasValue)
            throw new InvalidOperationException("Step is already completed");

        var completedAt = DateTime.UtcNow;
        var duration = completedAt - StartedAt;

        return new ProcessingStep
        {
            StepName = StepName,
            Description = Description,
            StartedAt = StartedAt,
            CompletedAt = completedAt,
            Duration = duration,
            IsSuccessful = true,
            ErrorMessage = ErrorMessage,
            Metadata = metadata != null ? new Dictionary<string, object>(metadata) : Metadata
        };
    }

    /// <summary>
    /// Marks the step as failed
    /// </summary>
    /// <param name="errorMessage">Error message</param>
    /// <param name="metadata">Optional metadata</param>
    /// <returns>Updated processing step</returns>
    public ProcessingStep MarkAsFailed(string errorMessage, Dictionary<string, object>? metadata = null)
    {
        if (IsSuccessful.HasValue)
            throw new InvalidOperationException("Step is already completed");

        if (string.IsNullOrWhiteSpace(errorMessage))
            throw new ArgumentException("Error message cannot be empty", nameof(errorMessage));

        var completedAt = DateTime.UtcNow;
        var duration = completedAt - StartedAt;

        return new ProcessingStep
        {
            StepName = StepName,
            Description = Description,
            StartedAt = StartedAt,
            CompletedAt = completedAt,
            Duration = duration,
            IsSuccessful = false,
            ErrorMessage = errorMessage.Trim(),
            Metadata = metadata != null ? new Dictionary<string, object>(metadata) : Metadata
        };
    }

    /// <summary>
    /// Adds metadata to the step
    /// </summary>
    /// <param name="key">Metadata key</param>
    /// <param name="value">Metadata value</param>
    /// <returns>Updated processing step</returns>
    public ProcessingStep WithMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Metadata key cannot be empty", nameof(key));

        var newMetadata = new Dictionary<string, object>(Metadata)
        {
            [key] = value
        };

        return new ProcessingStep
        {
            StepName = StepName,
            Description = Description,
            StartedAt = StartedAt,
            CompletedAt = CompletedAt,
            Duration = Duration,
            IsSuccessful = IsSuccessful,
            ErrorMessage = ErrorMessage,
            Metadata = newMetadata
        };
    }

    /// <summary>
    /// Checks if the step is currently running
    /// </summary>
    /// <returns>True if step is running</returns>
    public bool IsRunning()
    {
        return !IsSuccessful.HasValue;
    }

    /// <summary>
    /// Checks if the step completed successfully
    /// </summary>
    /// <returns>True if step completed successfully</returns>
    public bool IsCompletedSuccessfully()
    {
        return IsSuccessful == true;
    }

    /// <summary>
    /// Checks if the step failed
    /// </summary>
    /// <returns>True if step failed</returns>
    public bool IsFailed()
    {
        return IsSuccessful == false;
    }

    /// <summary>
    /// Gets the step status as a string
    /// </summary>
    /// <returns>Step status</returns>
    public string GetStatus()
    {
        return IsSuccessful switch
        {
            null => "Running",
            true => "Completed",
            false => "Failed"
        };
    }

    /// <summary>
    /// Gets a metadata value
    /// </summary>
    /// <typeparam name="T">Metadata type</typeparam>
    /// <param name="key">Metadata key</param>
    /// <param name="defaultValue">Default value if not found</param>
    /// <returns>Metadata value</returns>
    public T GetMetadata<T>(string key, T defaultValue = default!)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return StepName;
        yield return Description;
        yield return StartedAt;
        yield return CompletedAt ?? DateTime.MinValue;
        yield return IsSuccessful ?? false;
        yield return ErrorMessage ?? string.Empty;
    }
}

/// <summary>
/// Represents a processing error
/// </summary>
public class ProcessingError : ValueObject
{
    /// <summary>
    /// Error code
    /// </summary>
    public string ErrorCode { get; private set; }

    /// <summary>
    /// Error message
    /// </summary>
    public string ErrorMessage { get; private set; }

    /// <summary>
    /// Error severity
    /// </summary>
    public ErrorSeverity Severity { get; private set; }

    /// <summary>
    /// Step where error occurred
    /// </summary>
    public string StepName { get; private set; }

    /// <summary>
    /// Agent that encountered the error
    /// </summary>
    public string AgentName { get; private set; }

    /// <summary>
    /// When the error occurred
    /// </summary>
    public DateTime OccurredAt { get; private set; }

    /// <summary>
    /// Exception details if available
    /// </summary>
    public string? ExceptionDetails { get; private set; }

    /// <summary>
    /// Additional error context
    /// </summary>
    public Dictionary<string, object> Context { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private ProcessingError()
    {
        ErrorCode = string.Empty;
        ErrorMessage = string.Empty;
        StepName = string.Empty;
        AgentName = string.Empty;
        Context = new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates a new processing error
    /// </summary>
    /// <param name="errorCode">Error code</param>
    /// <param name="errorMessage">Error message</param>
    /// <param name="severity">Error severity</param>
    /// <param name="stepName">Step where error occurred</param>
    /// <param name="agentName">Agent that encountered the error</param>
    /// <param name="exceptionDetails">Exception details</param>
    /// <returns>New processing error</returns>
    public static ProcessingError Create(
        string errorCode,
        string errorMessage,
        ErrorSeverity severity,
        string stepName,
        string agentName,
        string? exceptionDetails = null)
    {
        if (string.IsNullOrWhiteSpace(errorCode))
            throw new ArgumentException("Error code cannot be empty", nameof(errorCode));

        if (string.IsNullOrWhiteSpace(errorMessage))
            throw new ArgumentException("Error message cannot be empty", nameof(errorMessage));

        if (string.IsNullOrWhiteSpace(stepName))
            throw new ArgumentException("Step name cannot be empty", nameof(stepName));

        if (string.IsNullOrWhiteSpace(agentName))
            throw new ArgumentException("Agent name cannot be empty", nameof(agentName));

        return new ProcessingError
        {
            ErrorCode = errorCode.Trim(),
            ErrorMessage = errorMessage.Trim(),
            Severity = severity,
            StepName = stepName.Trim(),
            AgentName = agentName.Trim(),
            OccurredAt = DateTime.UtcNow,
            ExceptionDetails = exceptionDetails?.Trim(),
            Context = new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// Adds context to the error
    /// </summary>
    /// <param name="key">Context key</param>
    /// <param name="value">Context value</param>
    /// <returns>Updated processing error</returns>
    public ProcessingError WithContext(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Context key cannot be empty", nameof(key));

        var newContext = new Dictionary<string, object>(Context)
        {
            [key] = value
        };

        return new ProcessingError
        {
            ErrorCode = ErrorCode,
            ErrorMessage = ErrorMessage,
            Severity = Severity,
            StepName = StepName,
            AgentName = AgentName,
            OccurredAt = OccurredAt,
            ExceptionDetails = ExceptionDetails,
            Context = newContext
        };
    }

    /// <summary>
    /// Checks if the error is critical
    /// </summary>
    /// <returns>True if error is critical</returns>
    public bool IsCritical()
    {
        return Severity == ErrorSeverity.Critical || Severity == ErrorSeverity.High;
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return ErrorCode;
        yield return ErrorMessage;
        yield return Severity;
        yield return StepName;
        yield return AgentName;
        yield return OccurredAt;
        yield return ExceptionDetails ?? string.Empty;
    }
}
