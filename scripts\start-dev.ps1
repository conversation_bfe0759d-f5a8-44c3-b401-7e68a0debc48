# LexAI Development Startup Script
# This script helps developers start the LexAI microservices architecture

param(
    [switch]$Infrastructure,
    [switch]$Services,
    [switch]$All,
    [switch]$Stop,
    [switch]$Clean,
    [switch]$Logs,
    [string]$Service = ""
)

$ErrorActionPreference = "Stop"

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Blue = "Blue"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Show-Help {
    Write-ColorOutput "LexAI Development Startup Script" $Blue
    Write-ColorOutput "=================================" $Blue
    Write-Host ""
    Write-ColorOutput "Usage:" $Yellow
    Write-Host "  .\scripts\start-dev.ps1 [OPTIONS]"
    Write-Host ""
    Write-ColorOutput "Options:" $Yellow
    Write-Host "  -Infrastructure    Start only infrastructure services (PostgreSQL, MongoDB, Redis, RabbitMQ)"
    Write-Host "  -Services         Start only application services"
    Write-Host "  -All              Start all services (infrastructure + applications)"
    Write-Host "  -Stop             Stop all services"
    Write-Host "  -Clean            Stop all services and remove volumes (⚠️  Data loss)"
    Write-Host "  -Logs             Show logs for all services"
    Write-Host "  -Service <name>   Show logs for specific service"
    Write-Host ""
    Write-ColorOutput "Examples:" $Yellow
    Write-Host "  .\scripts\start-dev.ps1 -Infrastructure"
    Write-Host "  .\scripts\start-dev.ps1 -All"
    Write-Host "  .\scripts\start-dev.ps1 -Logs"
    Write-Host "  .\scripts\start-dev.ps1 -Service api-gateway"
    Write-Host "  .\scripts\start-dev.ps1 -Stop"
}

function Test-Prerequisites {
    Write-ColorOutput "🔍 Checking prerequisites..." $Blue
    
    # Check Docker
    try {
        $dockerVersion = docker --version
        Write-ColorOutput "✅ Docker: $dockerVersion" $Green
    }
    catch {
        Write-ColorOutput "❌ Docker is not installed or not running" $Red
        exit 1
    }
    
    # Check Docker Compose
    try {
        $composeVersion = docker-compose --version
        Write-ColorOutput "✅ Docker Compose: $composeVersion" $Green
    }
    catch {
        Write-ColorOutput "❌ Docker Compose is not installed" $Red
        exit 1
    }
    
    # Check .NET
    try {
        $dotnetVersion = dotnet --version
        Write-ColorOutput "✅ .NET SDK: $dotnetVersion" $Green
    }
    catch {
        Write-ColorOutput "❌ .NET SDK is not installed" $Red
        exit 1
    }
    
    # Check if .env file exists
    if (Test-Path ".env") {
        Write-ColorOutput "✅ .env file found" $Green
    }
    else {
        Write-ColorOutput "⚠️  .env file not found, using default values" $Yellow
        if (Test-Path ".env.example") {
            Write-ColorOutput "💡 Consider copying .env.example to .env and updating values" $Yellow
        }
    }
}

function Start-Infrastructure {
    Write-ColorOutput "🚀 Starting infrastructure services..." $Blue
    
    try {
        docker-compose up -d postgres mongodb redis rabbitmq
        
        Write-ColorOutput "⏳ Waiting for services to be ready..." $Yellow
        Start-Sleep -Seconds 10
        
        # Check service health
        $services = @("postgres", "mongodb", "redis", "rabbitmq")
        foreach ($service in $services) {
            $status = docker-compose ps $service --format "table {{.State}}"
            if ($status -like "*Up*") {
                Write-ColorOutput "✅ $service is running" $Green
            }
            else {
                Write-ColorOutput "❌ $service failed to start" $Red
            }
        }
        
        Write-ColorOutput "🎉 Infrastructure services started successfully!" $Green
        Write-ColorOutput "📊 RabbitMQ Management UI: http://localhost:15672" $Blue
        Write-ColorOutput "   Username: lexai_user" $Blue
        Write-ColorOutput "   Password: lexai_rabbitmq_password_2024!" $Blue
    }
    catch {
        Write-ColorOutput "❌ Failed to start infrastructure services: $_" $Red
        exit 1
    }
}

function Start-Services {
    Write-ColorOutput "🚀 Starting application services..." $Blue
    
    try {
        # Build and start services
        docker-compose up -d --build api-gateway
        
        Write-ColorOutput "⏳ Waiting for services to be ready..." $Yellow
        Start-Sleep -Seconds 15
        
        # Test API Gateway
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:8080/api/gateway/ping" -Method Get -TimeoutSec 10
            Write-ColorOutput "✅ API Gateway is responding: $($response.message)" $Green
        }
        catch {
            Write-ColorOutput "⚠️  API Gateway might still be starting up..." $Yellow
        }
        
        Write-ColorOutput "🎉 Application services started successfully!" $Green
        Write-ColorOutput "📖 API Documentation: http://localhost:8080/swagger" $Blue
        Write-ColorOutput "🔍 Health Checks: http://localhost:8080/health" $Blue
    }
    catch {
        Write-ColorOutput "❌ Failed to start application services: $_" $Red
        exit 1
    }
}

function Start-AllServices {
    Write-ColorOutput "🚀 Starting all services..." $Blue
    Start-Infrastructure
    Start-Sleep -Seconds 5
    Start-Services
}

function Stop-Services {
    Write-ColorOutput "🛑 Stopping all services..." $Blue
    
    try {
        docker-compose down
        Write-ColorOutput "✅ All services stopped successfully!" $Green
    }
    catch {
        Write-ColorOutput "❌ Failed to stop services: $_" $Red
        exit 1
    }
}

function Clean-Services {
    Write-ColorOutput "🧹 Stopping services and cleaning volumes..." $Yellow
    Write-ColorOutput "⚠️  This will remove all data in databases!" $Red
    
    $confirmation = Read-Host "Are you sure? Type 'yes' to continue"
    if ($confirmation -eq "yes") {
        try {
            docker-compose down -v
            docker system prune -f
            Write-ColorOutput "✅ Services stopped and volumes cleaned!" $Green
        }
        catch {
            Write-ColorOutput "❌ Failed to clean services: $_" $Red
            exit 1
        }
    }
    else {
        Write-ColorOutput "❌ Operation cancelled" $Yellow
    }
}

function Show-Logs {
    param([string]$ServiceName = "")
    
    if ($ServiceName) {
        Write-ColorOutput "📋 Showing logs for service: $ServiceName" $Blue
        docker-compose logs -f $ServiceName
    }
    else {
        Write-ColorOutput "📋 Showing logs for all services..." $Blue
        docker-compose logs -f
    }
}

function Show-Status {
    Write-ColorOutput "📊 Service Status:" $Blue
    docker-compose ps
    
    Write-Host ""
    Write-ColorOutput "🔗 Quick Links:" $Blue
    Write-Host "  API Gateway:        http://localhost:8080"
    Write-Host "  Swagger Docs:       http://localhost:8080/swagger"
    Write-Host "  Health Checks:      http://localhost:8080/health"
    Write-Host "  RabbitMQ Management: http://localhost:15672"
    
    Write-Host ""
    Write-ColorOutput "🧪 Quick Tests:" $Blue
    Write-Host "  curl http://localhost:8080/api/gateway/ping"
    Write-Host "  curl http://localhost:8080/api/gateway/info"
    Write-Host "  curl http://localhost:8080/health"
}

# Main script logic
if (-not $Infrastructure -and -not $Services -and -not $All -and -not $Stop -and -not $Clean -and -not $Logs -and -not $Service) {
    Show-Help
    exit 0
}

# Change to project root directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = Split-Path -Parent $scriptPath
Set-Location $projectRoot

Write-ColorOutput "🏗️  LexAI Development Environment" $Blue
Write-ColorOutput "Working directory: $(Get-Location)" $Blue
Write-Host ""

Test-Prerequisites

if ($Stop) {
    Stop-Services
}
elseif ($Clean) {
    Clean-Services
}
elseif ($Logs) {
    Show-Logs
}
elseif ($Service) {
    Show-Logs -ServiceName $Service
}
elseif ($Infrastructure) {
    Start-Infrastructure
    Show-Status
}
elseif ($Services) {
    Start-Services
    Show-Status
}
elseif ($All) {
    Start-AllServices
    Show-Status
}

Write-Host ""
Write-ColorOutput "✨ Done! Use 'docker-compose logs -f' to monitor services." $Green
