{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Jwt": {"SecretKey": "test-secret-key-that-is-at-least-32-characters-long-for-testing", "Issuer": "LexAI-Test", "Audience": "LexAI-Test-Users", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5, "ValidateIssuer": false, "ValidateAudience": false, "ValidateLifetime": false, "ValidateIssuerSigningKey": false, "RequireHttpsMetadata": false}, "OpenAI": {"ApiKey": "test-api-key", "EmbeddingModel": "text-embedding-3-small", "EmbeddingDimension": 1536}, "Search": {"DefaultChunkSize": 1000, "ChunkOverlap": 200, "MaxResultsPerQuery": 100, "CacheExpirationMinutes": 15, "MinSimilarityThreshold": 0.7}, "AllowedHosts": "*"}