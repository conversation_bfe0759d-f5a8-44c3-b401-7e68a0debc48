using LexAI.Identity.Application.Commands;
using LexAI.Identity.Application.DTOs;
using LexAI.Identity.Application.Interfaces;
using LexAI.Identity.Domain.Entities;
using LexAI.Shared.Domain.Enums;
using LexAI.Shared.Domain.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace LexAI.Identity.Application.Tests.Commands;

public class RegisterUserCommandTests
{
    private readonly Mock<IUserRepository> _userRepositoryMock;
    private readonly Mock<IPasswordService> _passwordServiceMock;
    private readonly Mock<ILogger<RegisterUserCommandHandler>> _loggerMock;
    private readonly RegisterUserCommandHandler _handler;

    public RegisterUserCommandTests()
    {
        _userRepositoryMock = new Mock<IUserRepository>();
        _passwordServiceMock = new Mock<IPasswordService>();
        _loggerMock = new Mock<ILogger<RegisterUserCommandHandler>>();
        _handler = new RegisterUserCommandHandler(
            _userRepositoryMock.Object,
            _passwordServiceMock.Object,
            _loggerMock.Object);
    }

    [Fact]
    public async Task Handle_WithValidData_ShouldCreateUser()
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Password = "Password123!",
            Role = UserRole.Client,
            PreferredLanguage = "fr-FR",
            TimeZone = "Europe/Paris",
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        var passwordValidation = new PasswordValidationResult { IsValid = true, Errors = new List<string>() };
        var createdUser = User.Create(command.Email, command.FirstName, command.LastName, command.Role, "system");

        _userRepositoryMock
            .Setup(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        _passwordServiceMock
            .Setup(x => x.ValidatePasswordStrength(command.Password))
            .Returns(passwordValidation);

        _userRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(createdUser);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(command.Email, result.Email);
        Assert.Equal(command.FirstName, result.FirstName);
        Assert.Equal(command.LastName, result.LastName);
        Assert.Equal(command.Role, result.Role);

        _userRepositoryMock.Verify(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()), Times.Once);
        _passwordServiceMock.Verify(x => x.ValidatePasswordStrength(command.Password), Times.Once);
        _userRepositoryMock.Verify(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithAdministratorRole_ShouldThrowInvalidDataException()
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "Admin",
            LastName = "User",
            Password = "Password123!",
            Role = UserRole.Administrator,
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidDataException>(
            () => _handler.Handle(command, CancellationToken.None));

        Assert.Contains("Administrator role cannot be selected", exception.Message);
        Assert.Equal("Role", exception.PropertyName);
    }

    [Fact]
    public async Task Handle_WithoutAcceptingTerms_ShouldThrowInvalidDataException()
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Password = "Password123!",
            Role = UserRole.Client,
            AcceptTerms = false,
            AcceptPrivacyPolicy = true
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidDataException>(
            () => _handler.Handle(command, CancellationToken.None));

        Assert.Contains("Terms and conditions must be accepted", exception.Message);
        Assert.Equal("AcceptTerms", exception.PropertyName);
    }

    [Fact]
    public async Task Handle_WithoutAcceptingPrivacyPolicy_ShouldThrowInvalidDataException()
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Password = "Password123!",
            Role = UserRole.Client,
            AcceptTerms = true,
            AcceptPrivacyPolicy = false
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidDataException>(
            () => _handler.Handle(command, CancellationToken.None));

        Assert.Contains("Privacy policy must be accepted", exception.Message);
        Assert.Equal("AcceptPrivacyPolicy", exception.PropertyName);
    }

    [Fact]
    public async Task Handle_WithExistingEmail_ShouldThrowEntityAlreadyExistsException()
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Password = "Password123!",
            Role = UserRole.Client,
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        var existingUser = User.Create(command.Email, "Jane", "Smith", UserRole.Client, "system");

        _userRepositoryMock
            .Setup(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingUser);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<EntityAlreadyExistsException>(
            () => _handler.Handle(command, CancellationToken.None));

        Assert.Equal("User", exception.EntityName);
        Assert.Equal(command.Email, exception.EntityId);
    }

    [Fact]
    public async Task Handle_WithInvalidPassword_ShouldThrowInvalidDataException()
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Password = "weak",
            Role = UserRole.Client,
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        var passwordValidation = new PasswordValidationResult 
        { 
            IsValid = false, 
            Errors = new List<string> { "Password too short", "Missing special character" }
        };

        _userRepositoryMock
            .Setup(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        _passwordServiceMock
            .Setup(x => x.ValidatePasswordStrength(command.Password))
            .Returns(passwordValidation);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidDataException>(
            () => _handler.Handle(command, CancellationToken.None));

        Assert.Contains("Password validation failed", exception.Message);
        Assert.Equal("Password", exception.PropertyName);
    }

    [Fact]
    public async Task Handle_WithPhoneNumber_ShouldUpdateUserProfile()
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            PhoneNumber = "+33123456789",
            Password = "Password123!",
            Role = UserRole.Client,
            PreferredLanguage = "fr-FR",
            TimeZone = "Europe/Paris",
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        var passwordValidation = new PasswordValidationResult { IsValid = true, Errors = new List<string>() };
        var createdUser = User.Create(command.Email, command.FirstName, command.LastName, command.Role, "system");

        _userRepositoryMock
            .Setup(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        _passwordServiceMock
            .Setup(x => x.ValidatePasswordStrength(command.Password))
            .Returns(passwordValidation);

        _userRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(createdUser);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(command.PhoneNumber, result.PhoneNumber);
    }

    [Theory]
    [InlineData(UserRole.SeniorLawyer)]
    [InlineData(UserRole.Lawyer)]
    [InlineData(UserRole.LegalAssistant)]
    [InlineData(UserRole.Client)]
    [InlineData(UserRole.Guest)]
    public async Task Handle_WithValidNonAdminRoles_ShouldSucceed(UserRole role)
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Password = "Password123!",
            Role = role,
            PreferredLanguage = "fr-FR",
            TimeZone = "Europe/Paris",
            AcceptTerms = true,
            AcceptPrivacyPolicy = true
        };

        var passwordValidation = new PasswordValidationResult { IsValid = true, Errors = new List<string>() };
        var createdUser = User.Create(command.Email, command.FirstName, command.LastName, command.Role, "system");

        _userRepositoryMock
            .Setup(x => x.GetByEmailAsync(command.Email, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        _passwordServiceMock
            .Setup(x => x.ValidatePasswordStrength(command.Password))
            .Returns(passwordValidation);

        _userRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(createdUser);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(role, result.Role);
    }
}

// Classe helper pour les tests
public class PasswordValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
}
