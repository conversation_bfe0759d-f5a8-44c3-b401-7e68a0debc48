using FluentAssertions;
using LexAI.LegalResearch.Domain.ValueObjects;
using Xunit;

namespace LexAI.LegalResearch.UnitTests.Domain.ValueObjects;

/// <summary>
/// Unit tests for DocumentSource value object
/// </summary>
public class DocumentSourceTests
{
    [Fact]
    public void Create_WithValidParameters_ShouldCreateDocumentSource()
    {
        // Arrange
        var name = "Légifrance";
        var url = "https://legifrance.gouv.fr/codes/article_lc/LEGIARTI000006900785";
        var type = SourceType.Official;
        var authority = AuthorityLevel.National;
        var jurisdiction = "France";
        var reliabilityScore = 1.0;

        // Act
        var source = DocumentSource.Create(name, url, type, authority, jurisdiction, reliabilityScore);

        // Assert
        source.Should().NotBeNull();
        source.Name.Should().Be(name);
        source.Url.Should().Be(url);
        source.Type.Should().Be(type);
        source.Authority.Should().Be(authority);
        source.Jurisdiction.Should().Be(jurisdiction);
        source.ReliabilityScore.Should().Be(reliabilityScore);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Create_WithInvalidName_ShouldThrowArgumentException(string invalidName)
    {
        // Arrange
        var url = "https://example.com";
        var type = SourceType.Official;
        var authority = AuthorityLevel.National;
        var jurisdiction = "France";

        // Act & Assert
        var action = () => DocumentSource.Create(invalidName, url, type, authority, jurisdiction);
        action.Should().Throw<ArgumentException>().WithMessage("Source name cannot be empty*");
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Create_WithInvalidUrl_ShouldThrowArgumentException(string invalidUrl)
    {
        // Arrange
        var name = "Test Source";
        var type = SourceType.Official;
        var authority = AuthorityLevel.National;
        var jurisdiction = "France";

        // Act & Assert
        var action = () => DocumentSource.Create(name, invalidUrl, type, authority, jurisdiction);
        action.Should().Throw<ArgumentException>().WithMessage("Source URL cannot be empty*");
    }

    [Theory]
    [InlineData(-0.1)]
    [InlineData(1.1)]
    [InlineData(2.0)]
    public void Create_WithInvalidReliabilityScore_ShouldThrowArgumentException(double invalidScore)
    {
        // Arrange
        var name = "Test Source";
        var url = "https://example.com";
        var type = SourceType.Official;
        var authority = AuthorityLevel.National;
        var jurisdiction = "France";

        // Act & Assert
        var action = () => DocumentSource.Create(name, url, type, authority, jurisdiction, invalidScore);
        action.Should().Throw<ArgumentException>().WithMessage("Reliability score must be between 0 and 1*");
    }

    [Fact]
    public void CreateLegifrance_ShouldCreateLegifranceSource()
    {
        // Arrange
        var url = "https://legifrance.gouv.fr/codes/article_lc/LEGIARTI000006900785";

        // Act
        var source = DocumentSource.CreateLegifrance(url);

        // Assert
        source.Name.Should().Be("Légifrance");
        source.Url.Should().Be(url);
        source.Type.Should().Be(SourceType.Official);
        source.Authority.Should().Be(AuthorityLevel.National);
        source.Jurisdiction.Should().Be("France");
        source.ReliabilityScore.Should().Be(1.0);
    }

    [Fact]
    public void CreateEurLex_ShouldCreateEurLexSource()
    {
        // Arrange
        var url = "https://eur-lex.europa.eu/legal-content/EN/TXT/?uri=CELEX:32016R0679";

        // Act
        var source = DocumentSource.CreateEurLex(url);

        // Assert
        source.Name.Should().Be("EUR-Lex");
        source.Url.Should().Be(url);
        source.Type.Should().Be(SourceType.Official);
        source.Authority.Should().Be(AuthorityLevel.European);
        source.Jurisdiction.Should().Be("European Union");
        source.ReliabilityScore.Should().Be(1.0);
    }

    [Theory]
    [InlineData("Cour de Cassation", AuthorityLevel.Supreme)]
    [InlineData("Conseil d'État", AuthorityLevel.Supreme)]
    [InlineData("Cour d'appel de Paris", AuthorityLevel.Appellate)]
    [InlineData("Tribunal de grande instance", AuthorityLevel.FirstInstance)]
    [InlineData("Tribunal administratif", AuthorityLevel.FirstInstance)]
    public void CreateCourtDecision_ShouldCreateCorrectAuthorityLevel(string courtName, AuthorityLevel expectedAuthority)
    {
        // Arrange
        var url = "https://example.com/decision";
        var jurisdiction = "France";

        // Act
        var source = DocumentSource.CreateCourtDecision(courtName, url, jurisdiction);

        // Assert
        source.Name.Should().Be(courtName);
        source.Url.Should().Be(url);
        source.Type.Should().Be(SourceType.Jurisprudence);
        source.Authority.Should().Be(expectedAuthority);
        source.Jurisdiction.Should().Be(jurisdiction);
        source.ReliabilityScore.Should().BeGreaterThan(0.8);
    }

    [Fact]
    public void CreateAcademic_ShouldCreateAcademicSource()
    {
        // Arrange
        var institutionName = "Université Paris 1 Panthéon-Sorbonne";
        var url = "https://university.com/publication";
        var jurisdiction = "France";

        // Act
        var source = DocumentSource.CreateAcademic(institutionName, url, jurisdiction);

        // Assert
        source.Name.Should().Be(institutionName);
        source.Url.Should().Be(url);
        source.Type.Should().Be(SourceType.Academic);
        source.Authority.Should().Be(AuthorityLevel.Academic);
        source.Jurisdiction.Should().Be(jurisdiction);
        source.ReliabilityScore.Should().Be(0.8);
    }

    [Fact]
    public void GetDisplayName_ShouldReturnFormattedName()
    {
        // Arrange
        var source = DocumentSource.CreateLegifrance("https://legifrance.gouv.fr/test");

        // Act
        var displayName = source.GetDisplayName();

        // Assert
        displayName.Should().Be("Légifrance (France)");
    }

    [Fact]
    public void IsOfficial_WithOfficialSource_ShouldReturnTrue()
    {
        // Arrange
        var source = DocumentSource.CreateLegifrance("https://legifrance.gouv.fr/test");

        // Act
        var isOfficial = source.IsOfficial();

        // Assert
        isOfficial.Should().BeTrue();
    }

    [Fact]
    public void IsOfficial_WithNonOfficialSource_ShouldReturnFalse()
    {
        // Arrange
        var source = DocumentSource.CreateAcademic("University", "https://university.com", "France");

        // Act
        var isOfficial = source.IsOfficial();

        // Assert
        isOfficial.Should().BeFalse();
    }

    [Theory]
    [InlineData(0.8, true)]
    [InlineData(0.9, true)]
    [InlineData(1.0, true)]
    [InlineData(0.7, false)]
    [InlineData(0.5, false)]
    public void IsHighlyReliable_ShouldReturnCorrectValue(double reliabilityScore, bool expectedResult)
    {
        // Arrange
        var source = DocumentSource.Create("Test", "https://test.com", SourceType.Academic, AuthorityLevel.Academic, "Test", reliabilityScore);

        // Act
        var isHighlyReliable = source.IsHighlyReliable();

        // Assert
        isHighlyReliable.Should().Be(expectedResult);
    }

    [Theory]
    [InlineData(AuthorityLevel.European, "Autorité européenne")]
    [InlineData(AuthorityLevel.National, "Autorité nationale")]
    [InlineData(AuthorityLevel.Supreme, "Juridiction suprême")]
    [InlineData(AuthorityLevel.Appellate, "Juridiction d'appel")]
    [InlineData(AuthorityLevel.FirstInstance, "Juridiction de première instance")]
    [InlineData(AuthorityLevel.Administrative, "Autorité administrative")]
    [InlineData(AuthorityLevel.Academic, "Source académique")]
    [InlineData(AuthorityLevel.Professional, "Source professionnelle")]
    public void GetAuthorityDescription_ShouldReturnCorrectDescription(AuthorityLevel authority, string expectedDescription)
    {
        // Arrange
        var source = DocumentSource.Create("Test", "https://test.com", SourceType.Official, authority, "Test");

        // Act
        var description = source.GetAuthorityDescription();

        // Assert
        description.Should().Be(expectedDescription);
    }

    [Fact]
    public void Equality_WithSameValues_ShouldBeEqual()
    {
        // Arrange
        var source1 = DocumentSource.CreateLegifrance("https://legifrance.gouv.fr/test");
        var source2 = DocumentSource.CreateLegifrance("https://legifrance.gouv.fr/test");

        // Act & Assert
        source1.Should().Be(source2);
        source1.GetHashCode().Should().Be(source2.GetHashCode());
    }

    [Fact]
    public void Equality_WithDifferentValues_ShouldNotBeEqual()
    {
        // Arrange
        var source1 = DocumentSource.CreateLegifrance("https://legifrance.gouv.fr/test1");
        var source2 = DocumentSource.CreateLegifrance("https://legifrance.gouv.fr/test2");

        // Act & Assert
        source1.Should().NotBe(source2);
    }
}
