# LexAI Database Migration Management Script
# This script helps manage Entity Framework migrations for LexAI services

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("add", "remove", "update", "list", "script", "drop")]
    [string]$Action,
    
    [string]$Name = "",
    [string]$Service = "Identity",
    [switch]$Force,
    [switch]$DryRun
)

$ErrorActionPreference = "Stop"

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Blue = "Blue"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Show-Help {
    Write-ColorOutput "LexAI Database Migration Management" $Blue
    Write-ColorOutput "===================================" $Blue
    Write-Host ""
    Write-ColorOutput "Usage:" $Yellow
    Write-Host "  .\scripts\manage-migrations.ps1 -Action <action> [OPTIONS]"
    Write-Host ""
    Write-ColorOutput "Actions:" $Yellow
    Write-Host "  add       Add a new migration"
    Write-Host "  remove    Remove the last migration"
    Write-Host "  update    Apply migrations to database"
    Write-Host "  list      List all migrations"
    Write-Host "  script    Generate SQL script for migrations"
    Write-Host "  drop      Drop the database"
    Write-Host ""
    Write-ColorOutput "Options:" $Yellow
    Write-Host "  -Name <name>      Migration name (required for 'add' action)"
    Write-Host "  -Service <name>   Service name (default: Identity)"
    Write-Host "  -Force            Force the operation"
    Write-Host "  -DryRun           Show what would be done without executing"
    Write-Host ""
    Write-ColorOutput "Examples:" $Yellow
    Write-Host "  .\scripts\manage-migrations.ps1 -Action add -Name 'InitialCreate'"
    Write-Host "  .\scripts\manage-migrations.ps1 -Action update"
    Write-Host "  .\scripts\manage-migrations.ps1 -Action list"
    Write-Host "  .\scripts\manage-migrations.ps1 -Action script"
}

function Get-ServiceInfo {
    param([string]$ServiceName)
    
    $services = @{
        "Identity" = @{
            ProjectPath = "src/Services/Identity/LexAI.Identity.Infrastructure"
            StartupProject = "src/Services/Identity/LexAI.Identity.API"
            ContextName = "IdentityDbContext"
        }
        # Add other services here as they are implemented
    }
    
    if (-not $services.ContainsKey($ServiceName)) {
        Write-ColorOutput "❌ Unknown service: $ServiceName" $Red
        Write-ColorOutput "Available services: $($services.Keys -join ', ')" $Yellow
        exit 1
    }
    
    return $services[$ServiceName]
}

function Test-Prerequisites {
    Write-ColorOutput "🔍 Checking prerequisites..." $Blue
    
    # Check .NET
    try {
        $dotnetVersion = dotnet --version
        Write-ColorOutput "✅ .NET SDK: $dotnetVersion" $Green
    }
    catch {
        Write-ColorOutput "❌ .NET SDK is not installed" $Red
        exit 1
    }
    
    # Check EF Core tools
    try {
        $efVersion = dotnet ef --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ EF Core Tools: $efVersion" $Green
        } else {
            Write-ColorOutput "⚠️  EF Core Tools not found, installing..." $Yellow
            dotnet tool install --global dotnet-ef
            Write-ColorOutput "✅ EF Core Tools installed" $Green
        }
    }
    catch {
        Write-ColorOutput "❌ Failed to install EF Core Tools" $Red
        exit 1
    }
}

function Add-Migration {
    param([string]$MigrationName, [hashtable]$ServiceInfo)
    
    if ([string]::IsNullOrWhiteSpace($MigrationName)) {
        Write-ColorOutput "❌ Migration name is required for 'add' action" $Red
        exit 1
    }
    
    Write-ColorOutput "📝 Adding migration '$MigrationName' for $Service service..." $Blue
    
    $command = @(
        "ef", "migrations", "add", $MigrationName,
        "--project", $ServiceInfo.ProjectPath,
        "--startup-project", $ServiceInfo.StartupProject,
        "--output-dir", "Data/Migrations",
        "--context", $ServiceInfo.ContextName,
        "--verbose"
    )
    
    if ($DryRun) {
        Write-ColorOutput "🔍 Would execute: dotnet $($command -join ' ')" $Yellow
        return
    }
    
    try {
        & dotnet @command
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Migration '$MigrationName' added successfully" $Green
        } else {
            Write-ColorOutput "❌ Failed to add migration" $Red
            exit 1
        }
    }
    catch {
        Write-ColorOutput "❌ Error adding migration: $_" $Red
        exit 1
    }
}

function Remove-Migration {
    param([hashtable]$ServiceInfo)
    
    Write-ColorOutput "🗑️  Removing last migration for $Service service..." $Blue
    
    $command = @(
        "ef", "migrations", "remove",
        "--project", $ServiceInfo.ProjectPath,
        "--startup-project", $ServiceInfo.StartupProject,
        "--context", $ServiceInfo.ContextName,
        "--verbose"
    )
    
    if ($Force) {
        $command += "--force"
    }
    
    if ($DryRun) {
        Write-ColorOutput "🔍 Would execute: dotnet $($command -join ' ')" $Yellow
        return
    }
    
    try {
        & dotnet @command
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Last migration removed successfully" $Green
        } else {
            Write-ColorOutput "❌ Failed to remove migration" $Red
            exit 1
        }
    }
    catch {
        Write-ColorOutput "❌ Error removing migration: $_" $Red
        exit 1
    }
}

function Update-Database {
    param([hashtable]$ServiceInfo)
    
    Write-ColorOutput "🔄 Updating database for $Service service..." $Blue
    
    $command = @(
        "ef", "database", "update",
        "--project", $ServiceInfo.ProjectPath,
        "--startup-project", $ServiceInfo.StartupProject,
        "--context", $ServiceInfo.ContextName,
        "--verbose"
    )
    
    if ($DryRun) {
        Write-ColorOutput "🔍 Would execute: dotnet $($command -join ' ')" $Yellow
        return
    }
    
    try {
        & dotnet @command
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Database updated successfully" $Green
        } else {
            Write-ColorOutput "❌ Failed to update database" $Red
            exit 1
        }
    }
    catch {
        Write-ColorOutput "❌ Error updating database: $_" $Red
        exit 1
    }
}

function List-Migrations {
    param([hashtable]$ServiceInfo)
    
    Write-ColorOutput "📋 Listing migrations for $Service service..." $Blue
    
    $command = @(
        "ef", "migrations", "list",
        "--project", $ServiceInfo.ProjectPath,
        "--startup-project", $ServiceInfo.StartupProject,
        "--context", $ServiceInfo.ContextName
    )
    
    try {
        & dotnet @command
    }
    catch {
        Write-ColorOutput "❌ Error listing migrations: $_" $Red
        exit 1
    }
}

function Generate-Script {
    param([hashtable]$ServiceInfo)
    
    Write-ColorOutput "📜 Generating SQL script for $Service service..." $Blue
    
    $outputFile = "migrations-$Service-$(Get-Date -Format 'yyyyMMdd-HHmmss').sql"
    
    $command = @(
        "ef", "migrations", "script",
        "--project", $ServiceInfo.ProjectPath,
        "--startup-project", $ServiceInfo.StartupProject,
        "--context", $ServiceInfo.ContextName,
        "--output", $outputFile,
        "--verbose"
    )
    
    if ($DryRun) {
        Write-ColorOutput "🔍 Would execute: dotnet $($command -join ' ')" $Yellow
        return
    }
    
    try {
        & dotnet @command
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ SQL script generated: $outputFile" $Green
        } else {
            Write-ColorOutput "❌ Failed to generate script" $Red
            exit 1
        }
    }
    catch {
        Write-ColorOutput "❌ Error generating script: $_" $Red
        exit 1
    }
}

function Drop-Database {
    param([hashtable]$ServiceInfo)
    
    Write-ColorOutput "⚠️  Dropping database for $Service service..." $Yellow
    
    if (-not $Force) {
        $confirmation = Read-Host "This will permanently delete all data. Type 'yes' to continue"
        if ($confirmation -ne "yes") {
            Write-ColorOutput "❌ Operation cancelled" $Yellow
            exit 0
        }
    }
    
    $command = @(
        "ef", "database", "drop",
        "--project", $ServiceInfo.ProjectPath,
        "--startup-project", $ServiceInfo.StartupProject,
        "--context", $ServiceInfo.ContextName,
        "--force",
        "--verbose"
    )
    
    if ($DryRun) {
        Write-ColorOutput "🔍 Would execute: dotnet $($command -join ' ')" $Yellow
        return
    }
    
    try {
        & dotnet @command
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Database dropped successfully" $Green
        } else {
            Write-ColorOutput "❌ Failed to drop database" $Red
            exit 1
        }
    }
    catch {
        Write-ColorOutput "❌ Error dropping database: $_" $Red
        exit 1
    }
}

# Main script logic
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = Split-Path -Parent $scriptPath
Set-Location $projectRoot

Write-ColorOutput "🗄️  LexAI Database Migration Management" $Blue
Write-ColorOutput "Working directory: $(Get-Location)" $Blue
Write-Host ""

Test-Prerequisites

$serviceInfo = Get-ServiceInfo -ServiceName $Service

# Check if project paths exist
if (-not (Test-Path $serviceInfo.ProjectPath)) {
    Write-ColorOutput "❌ Project path not found: $($serviceInfo.ProjectPath)" $Red
    exit 1
}

if (-not (Test-Path $serviceInfo.StartupProject)) {
    Write-ColorOutput "❌ Startup project path not found: $($serviceInfo.StartupProject)" $Red
    exit 1
}

switch ($Action) {
    "add" { Add-Migration -MigrationName $Name -ServiceInfo $serviceInfo }
    "remove" { Remove-Migration -ServiceInfo $serviceInfo }
    "update" { Update-Database -ServiceInfo $serviceInfo }
    "list" { List-Migrations -ServiceInfo $serviceInfo }
    "script" { Generate-Script -ServiceInfo $serviceInfo }
    "drop" { Drop-Database -ServiceInfo $serviceInfo }
    default { Show-Help }
}

Write-Host ""
Write-ColorOutput "✨ Migration operation completed!" $Green
