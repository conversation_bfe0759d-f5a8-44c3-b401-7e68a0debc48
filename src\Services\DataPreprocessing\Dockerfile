# Utiliser l'image de base .NET 9 pour le runtime
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

# Utiliser l'image SDK pour la compilation
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# Copier les fichiers de projet et restaurer les dépendances
COPY ["src/Services/DataPreprocessing/LexAI.DataPreprocessing.API/LexAI.DataPreprocessing.API.csproj", "src/Services/DataPreprocessing/LexAI.DataPreprocessing.API/"]
COPY ["src/Services/DataPreprocessing/LexAI.DataPreprocessing.Application/LexAI.DataPreprocessing.Application.csproj", "src/Services/DataPreprocessing/LexAI.DataPreprocessing.Application/"]
COPY ["src/Services/DataPreprocessing/LexAI.DataPreprocessing.Domain/LexAI.DataPreprocessing.Domain.csproj", "src/Services/DataPreprocessing/LexAI.DataPreprocessing.Domain/"]
COPY ["src/Services/DataPreprocessing/LexAI.DataPreprocessing.Infrastructure/LexAI.DataPreprocessing.Infrastructure.csproj", "src/Services/DataPreprocessing/LexAI.DataPreprocessing.Infrastructure/"]
COPY ["src/Shared/LexAI.Shared.Domain/LexAI.Shared.Domain.csproj", "src/Shared/LexAI.Shared.Domain/"]
COPY ["src/Shared/LexAI.Shared.Infrastructure/LexAI.Shared.Infrastructure.csproj", "src/Shared/LexAI.Shared.Infrastructure/"]

RUN dotnet restore "src/Services/DataPreprocessing/LexAI.DataPreprocessing.API/LexAI.DataPreprocessing.API.csproj"

# Copier tout le code source
COPY . .

# Compiler l'application
WORKDIR "/src/src/Services/DataPreprocessing/LexAI.DataPreprocessing.API"
RUN dotnet build "LexAI.DataPreprocessing.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

# Publier l'application
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "LexAI.DataPreprocessing.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# Image finale
FROM base AS final
WORKDIR /app

# Installer les dépendances système nécessaires
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Créer un utilisateur non-root pour la sécurité
RUN addgroup --system --gid 1001 lexai && \
    adduser --system --uid 1001 --gid 1001 lexai

# Créer les répertoires nécessaires
RUN mkdir -p /app/storage/documents && \
    chown -R lexai:lexai /app/storage

# Copier l'application publiée
COPY --from=publish /app/publish .

# Changer vers l'utilisateur non-root
USER lexai

# Variables d'environnement par défaut
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_HTTP_PORTS=8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Point d'entrée
ENTRYPOINT ["dotnet", "LexAI.DataPreprocessing.API.dll"]
