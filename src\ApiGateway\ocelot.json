{"Routes": [{"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "identity-service", "Port": 8081}], "UpstreamPathTemplate": "/api/identity/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"], "Key": "identity-service", "SwaggerKey": "identity"}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "legal-research-service", "Port": 8082}], "UpstreamPathTemplate": "/api/legal-research/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"], "Key": "legal-research-service", "SwaggerKey": "legal-research", "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": []}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": true, "Period": "1m", "PeriodTimespan": 60, "Limit": 50}}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "ai-assistant-service", "Port": 8083}], "UpstreamPathTemplate": "/api/ai-assistant/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"], "Key": "ai-assistant-service", "SwaggerKey": "ai-assistant", "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": []}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": true, "Period": "1m", "PeriodTimespan": 60, "Limit": 30}}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "document-analysis-service", "Port": 8084}], "UpstreamPathTemplate": "/api/document-analysis/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"], "Key": "document-analysis-service", "SwaggerKey": "document-analysis", "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": []}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": true, "Period": "1m", "PeriodTimespan": 60, "Limit": 20}}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "document-generator-service", "Port": 8085}], "UpstreamPathTemplate": "/api/document-generator/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"], "Key": "document-generator-service", "SwaggerKey": "document-generator", "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": []}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": true, "Period": "1m", "PeriodTimespan": 60, "Limit": 25}}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "client-management-service", "Port": 8086}], "UpstreamPathTemplate": "/api/client-management/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"], "Key": "client-management-service", "SwaggerKey": "client-management", "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": []}}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "notification-service", "Port": 8087}], "UpstreamPathTemplate": "/api/notifications/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"], "Key": "notification-service", "SwaggerKey": "notifications", "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": []}}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "file-storage-service", "Port": 8088}], "UpstreamPathTemplate": "/api/files/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"], "Key": "file-storage-service", "SwaggerKey": "file-storage", "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": []}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": true, "Period": "1m", "PeriodTimespan": 60, "Limit": 10}}], "SwaggerEndPoints": [{"Key": "identity", "Config": [{"Name": "Identity Service API", "Version": "v1", "Url": "http://identity-service:8081/swagger/v1/swagger.json"}]}, {"Key": "legal-research", "Config": [{"Name": "Legal Research Service API", "Version": "v1", "Url": "http://legal-research-service:8082/swagger/v1/swagger.json"}]}, {"Key": "ai-assistant", "Config": [{"Name": "AI Assistant Service API", "Version": "v1", "Url": "http://ai-assistant-service:8083/swagger/v1/swagger.json"}]}, {"Key": "document-analysis", "Config": [{"Name": "Document Analysis Service API", "Version": "v1", "Url": "http://document-analysis-service:8084/swagger/v1/swagger.json"}]}, {"Key": "document-generator", "Config": [{"Name": "Document Generator Service API", "Version": "v1", "Url": "http://document-generator-service:8085/swagger/v1/swagger.json"}]}, {"Key": "client-management", "Config": [{"Name": "Client Management Service API", "Version": "v1", "Url": "http://client-management-service:8086/swagger/v1/swagger.json"}]}, {"Key": "notifications", "Config": [{"Name": "Notification Service API", "Version": "v1", "Url": "http://notification-service:8087/swagger/v1/swagger.json"}]}, {"Key": "file-storage", "Config": [{"Name": "File Storage Service API", "Version": "v1", "Url": "http://file-storage-service:8088/swagger/v1/swagger.json"}]}], "GlobalConfiguration": {"BaseUrl": "http://localhost:8080", "RateLimitOptions": {"DisableRateLimitHeaders": false, "QuotaExceededMessage": "Quota exceeded. Maximum allowed: {0} per {1}. Please try again in {2} second(s).", "HttpStatusCode": 429, "ClientIdHeader": "ClientId"}, "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 3, "DurationOfBreak": 5000, "TimeoutValue": 30000}, "LoadBalancerOptions": {"Type": "RoundR<PERSON>in"}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": true}, "ServiceDiscoveryProvider": {"Host": "localhost", "Port": 8500, "Type": "Consul"}}}