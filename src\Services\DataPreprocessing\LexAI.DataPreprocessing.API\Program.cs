using FluentValidation;
using Hangfire;
using Hangfire.Dashboard.BasicAuthorization;
using Hangfire.PostgreSql;
using LexAI.DataPreprocessing.Application.Commands;
using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Infrastructure.Agents;
using LexAI.Shared.Infrastructure.Configuration;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using System.Reflection;
using System.Text;
using System.Threading.RateLimiting;

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/lexai-data-preprocessing-.log", rollingInterval: RollingInterval.Day)
    .CreateLogger();

try
{
    var builder = WebApplication.CreateBuilder(args);

    // Add Serilog
    builder.Host.UseSerilog();

    // Add configuration
    builder.Configuration
        .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
        .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
        .AddEnvironmentVariables();

    // Configure JWT settings
    var jwtSettings = new JwtSettings();
    builder.Configuration.GetSection(JwtSettings.SectionName).Bind(jwtSettings);
    builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection(JwtSettings.SectionName));

    // Add services
    builder.Services.AddControllers();
    builder.Services.AddEndpointsApiExplorer();

    // Configure Swagger
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = "LexAI Data Preprocessing Service API",
            Version = "v1",
            Description = "Service de préprocessing de données avec agents spécialisés pour LexAI",
            Contact = new OpenApiContact
            {
                Name = "LexAI Support",
                Email = "<EMAIL>"
            }
        });

        // Add JWT authentication to Swagger
        c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
        {
            Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = "Bearer"
        });

        c.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                    }
                },
                Array.Empty<string>()
            }
        });

        // Include XML comments
        var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
        var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
        if (File.Exists(xmlPath))
        {
            c.IncludeXmlComments(xmlPath);
        }
    });

    // Configure JWT Authentication
    builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        .AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = jwtSettings.ValidateIssuer,
                ValidateAudience = jwtSettings.ValidateAudience,
                ValidateLifetime = jwtSettings.ValidateLifetime,
                ValidateIssuerSigningKey = jwtSettings.ValidateIssuerSigningKey,
                ValidIssuer = jwtSettings.Issuer,
                ValidAudience = jwtSettings.Audience,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings.SecretKey)),
                ClockSkew = TimeSpan.FromMinutes(jwtSettings.ClockSkewMinutes)
            };

            options.Events = new JwtBearerEvents
            {
                OnAuthenticationFailed = context =>
                {
                    Log.Warning("JWT Authentication failed: {Error}", context.Exception.Message);
                    return Task.CompletedTask;
                },
                OnTokenValidated = context =>
                {
                    Log.Information("JWT Token validated for user: {User}", context.Principal?.Identity?.Name);
                    return Task.CompletedTask;
                }
            };
        });

    // Configure Authorization
    builder.Services.AddAuthorization(options =>
    {
        options.AddPolicy("RequireAuthentication", policy =>
            policy.RequireAuthenticatedUser());
        
        options.AddPolicy("RequireLawyerRole", policy =>
            policy.RequireRole("Administrator", "SeniorLawyer", "Lawyer"));
    });

    // Configure Rate Limiting
    builder.Services.AddRateLimiter(options =>
    {
        options.AddFixedWindowLimiter("DocumentPolicy", configure =>
        {
            configure.PermitLimit = 10; // 10 document operations
            configure.Window = TimeSpan.FromMinutes(1); // per minute
            configure.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
            configure.QueueLimit = 2;
        });

        options.AddFixedWindowLimiter("ProcessingPolicy", configure =>
        {
            configure.PermitLimit = 5; // 5 processing operations
            configure.Window = TimeSpan.FromMinutes(1); // per minute
            configure.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
            configure.QueueLimit = 1;
        });

        options.OnRejected = async (context, token) =>
        {
            context.HttpContext.Response.StatusCode = 429;
            await context.HttpContext.Response.WriteAsync("Rate limit exceeded. Please try again later.", token);
        };
    });

    // Add MediatR
    builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(UploadDocumentCommand).Assembly));

    // Add FluentValidation
    builder.Services.AddValidatorsFromAssemblyContaining<UploadDocumentCommand>();

    // Add AutoMapper
    builder.Services.AddAutoMapper(typeof(UploadDocumentCommand).Assembly);

    // Add Memory Cache
    builder.Services.AddMemoryCache();

    // Configure Hangfire for background processing
    var hangfireConnectionString = builder.Configuration.GetConnectionString("Hangfire") 
        ?? builder.Configuration.GetConnectionString("PostgreSql");
    
    builder.Services.AddHangfire(configuration => configuration
        .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
        .UseSimpleAssemblyNameTypeSerializer()
        .UseRecommendedSerializerSettings()
        .UsePostgreSqlStorage(hangfireConnectionString, new PostgreSqlStorageOptions
        {
            QueuePollInterval = TimeSpan.FromSeconds(10),
            JobExpirationCheckInterval = TimeSpan.FromHours(1),
            CountersAggregateInterval = TimeSpan.FromMinutes(5),
            PrepareSchemaIfNecessary = true,
            DashboardJobListLimit = 25000,
            TransactionSynchronisationTimeout = TimeSpan.FromMinutes(5),
            TablesPrefix = "hangfire."
        }));

    builder.Services.AddHangfireServer(options =>
    {
        options.WorkerCount = Environment.ProcessorCount;
        options.Queues = new[] { "default", "processing", "vectorization" };
        options.ServerName = $"DataPreprocessing-{Environment.MachineName}";
    });

    // Register processing agents
    builder.Services.AddScoped<IExtractionAgent, ExtractionAgent>();
    builder.Services.AddScoped<IClassificationAgent, ClassificationAgent>();
    builder.Services.AddScoped<IChunkingAgent, ChunkingAgent>();
    builder.Services.AddScoped<IVectorizationAgent, VectorizationAgent>();
    builder.Services.AddScoped<IRoutingAgent, RoutingAgent>();
    builder.Services.AddScoped<IQualityAssuranceAgent, QualityAssuranceAgent>();
    builder.Services.AddScoped<IOrchestrationAgent, OrchestrationAgent>();

    // Configure CORS
    builder.Services.AddCors(options =>
    {
        options.AddPolicy("AllowedOrigins", policy =>
        {
            var allowedOrigins = builder.Configuration.GetSection("Cors:AllowedOrigins").Get<string[]>() 
                ?? new[] { "http://localhost:3000", "http://localhost:3001" };
            
            policy.WithOrigins(allowedOrigins)
                  .AllowAnyMethod()
                  .AllowAnyHeader()
                  .AllowCredentials();
        });
    });

    // Add Health Checks
    builder.Services.AddHealthChecks()
        .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy())
        .AddNpgSql(builder.Configuration.GetConnectionString("PostgreSql") ?? "Host=localhost;Database=data_preprocessing_db;Username=lexai_user;Password=lexai_password_2024!")
        .AddHangfire(options =>
        {
            options.MinimumAvailableServers = 1;
        });

    var app = builder.Build();

    // Configure the HTTP request pipeline
    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint("/swagger/v1/swagger.json", "LexAI Data Preprocessing Service API v1");
            c.RoutePrefix = "swagger";
            c.DocumentTitle = "LexAI Data Preprocessing Service API Documentation";
        });
    }

    // Configure Hangfire Dashboard
    app.UseHangfireDashboard("/hangfire", new DashboardOptions
    {
        Authorization = new[]
        {
            new BasicAuthAuthorizationFilter(new BasicAuthAuthorizationFilterOptions
            {
                RequireSsl = false,
                SslRedirect = false,
                LoginCaseSensitive = true,
                Users = new[]
                {
                    new BasicAuthAuthorizationUser
                    {
                        Login = builder.Configuration["Hangfire:Dashboard:Username"] ?? "admin",
                        PasswordClear = builder.Configuration["Hangfire:Dashboard:Password"] ?? "admin123"
                    }
                }
            })
        },
        DashboardTitle = "LexAI Data Preprocessing Jobs",
        StatsPollingInterval = 2000
    });

    // Security headers
    app.Use(async (context, next) =>
    {
        context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
        context.Response.Headers.Add("X-Frame-Options", "DENY");
        context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
        context.Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");
        
        if (!app.Environment.IsDevelopment())
        {
            context.Response.Headers.Add("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
        }
        
        await next();
    });

    // Request logging middleware
    app.Use(async (context, next) =>
    {
        var correlationId = Guid.NewGuid().ToString();
        context.Items["CorrelationId"] = correlationId;
        context.Response.Headers.Add("X-Correlation-ID", correlationId);
        
        Log.Information("Request {Method} {Path} started with correlation ID {CorrelationId}", 
            context.Request.Method, context.Request.Path, correlationId);
        
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        await next();
        
        stopwatch.Stop();
        
        Log.Information("Request {Method} {Path} completed in {ElapsedMs}ms with status {StatusCode} (Correlation ID: {CorrelationId})",
            context.Request.Method, context.Request.Path, stopwatch.ElapsedMilliseconds, context.Response.StatusCode, correlationId);
    });

    app.UseHttpsRedirection();
    app.UseCors("AllowedOrigins");
    app.UseRateLimiter();
    app.UseAuthentication();
    app.UseAuthorization();

    // Health checks endpoint
    app.MapHealthChecks("/health");

    // Map controllers
    app.MapControllers();

    // Schedule recurring jobs
    RecurringJob.AddOrUpdate(
        "cleanup-failed-documents",
        () => CleanupFailedDocuments(),
        Cron.Daily(2)); // Run at 2 AM daily

    RecurringJob.AddOrUpdate(
        "generate-processing-stats",
        () => GenerateProcessingStatistics(),
        Cron.Hourly); // Run every hour

    Log.Information("LexAI Data Preprocessing Service started successfully");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "LexAI Data Preprocessing Service failed to start");
}
finally
{
    Log.CloseAndFlush();
}

/// <summary>
/// Cleanup failed documents older than 7 days
/// </summary>
static void CleanupFailedDocuments()
{
    Log.Information("Starting cleanup of failed documents");
    // Implementation would go here
}

/// <summary>
/// Generate processing statistics
/// </summary>
static void GenerateProcessingStatistics()
{
    Log.Information("Generating processing statistics");
    // Implementation would go here
}
