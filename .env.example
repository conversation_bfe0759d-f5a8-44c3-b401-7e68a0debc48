# LexAI Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# OpenAI Configuration
# =============================================================================
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_ORGANIZATION_ID=your-organization-id-here
OPENAI_BASE_URL=https://api.openai.com/v1

# =============================================================================
# Database Configuration
# =============================================================================

# PostgreSQL
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=lexai_db
POSTGRES_USER=lexai_user
POSTGRES_PASSWORD=lexai_password_2024!

# MongoDB
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DATABASE=lexai_documents
MONGODB_USERNAME=lexai_admin
MONGODB_PASSWORD=lexai_mongo_password_2024!

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=lexai_redis_password_2024!

# =============================================================================
# RabbitMQ Configuration
# =============================================================================
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=lexai_user
RABBITMQ_PASSWORD=lexai_rabbitmq_password_2024!
RABBITMQ_VHOST=lexai_vhost

# =============================================================================
# JWT Configuration
# =============================================================================
JWT_SECRET_KEY=your-super-secret-jwt-key-that-is-at-least-32-characters-long-for-production
JWT_ISSUER=LexAI
JWT_AUDIENCE=LexAI-Users
JWT_ACCESS_TOKEN_EXPIRATION_MINUTES=60
JWT_REFRESH_TOKEN_EXPIRATION_DAYS=7

# =============================================================================
# Application Configuration
# =============================================================================
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=http://+:8080

# =============================================================================
# File Storage Configuration
# =============================================================================
FILE_STORAGE_PROVIDER=Local
FILE_STORAGE_LOCAL_PATH=./uploads
FILE_STORAGE_MAX_FILE_SIZE_MB=50

# Azure Blob Storage (if using Azure)
AZURE_STORAGE_CONNECTION_STRING=your-azure-storage-connection-string
AZURE_STORAGE_CONTAINER_NAME=lexai-documents

# AWS S3 (if using AWS)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET_NAME=lexai-documents
AWS_S3_REGION=eu-west-1

# =============================================================================
# Email Configuration
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=LexAI Assistant

# =============================================================================
# SMS Configuration (Twilio)
# =============================================================================
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+***********

# =============================================================================
# Logging Configuration
# =============================================================================
SERILOG_MINIMUM_LEVEL=Information
SERILOG_WRITE_TO_CONSOLE=true
SERILOG_WRITE_TO_FILE=true
SERILOG_FILE_PATH=logs/lexai-.log

# =============================================================================
# Security Configuration
# =============================================================================
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
ENABLE_HTTPS_REDIRECTION=false
ENABLE_RATE_LIMITING=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# =============================================================================
# Feature Flags
# =============================================================================
ENABLE_SWAGGER=true
ENABLE_HEALTH_CHECKS=true
ENABLE_METRICS=true
ENABLE_AUDIT_LOGGING=true
ENABLE_VIRUS_SCANNING=false

# =============================================================================
# External Services
# =============================================================================

# Document Processing
UNSTRUCTURED_API_URL=http://localhost:8000
UNSTRUCTURED_API_KEY=your-unstructured-api-key

# Legal Database APIs
LEGIFRANCE_API_KEY=your-legifrance-api-key
DOCTRINE_API_KEY=your-doctrine-api-key

# =============================================================================
# Development Configuration
# =============================================================================
ENABLE_SENSITIVE_DATA_LOGGING=false
ENABLE_DETAILED_ERRORS=true
ENABLE_DEVELOPER_EXCEPTION_PAGE=true

# =============================================================================
# Production Configuration
# =============================================================================
# Uncomment and configure for production deployment

# ASPNETCORE_ENVIRONMENT=Production
# ENABLE_HTTPS_REDIRECTION=true
# ENABLE_SENSITIVE_DATA_LOGGING=false
# ENABLE_DETAILED_ERRORS=false
# ENABLE_DEVELOPER_EXCEPTION_PAGE=false

# Application Insights (Azure)
# APPLICATIONINSIGHTS_CONNECTION_STRING=your-app-insights-connection-string

# DataDog (Alternative monitoring)
# DD_API_KEY=your-datadog-api-key
# DD_SITE=datadoghq.eu

# =============================================================================
# Docker Configuration
# =============================================================================
COMPOSE_PROJECT_NAME=lexai
DOCKER_REGISTRY=your-docker-registry.com
DOCKER_IMAGE_TAG=latest
