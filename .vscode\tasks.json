{"version": "2.0.0", "tasks": [{"label": "build-identity-api", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/Services/Identity/LexAI.Identity.API/LexAI.Identity.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "build-datapreprocessing-api", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/Services/DataPreprocessing/LexAI.DataPreprocessing.API/LexAI.DataPreprocessing.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "build-apigateway", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/ApiGateway/LexAI.ApiGateway.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "build-all", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/LexAI.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/LexAI.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/LexAI.sln"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "run-identity-api", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/src/Services/Identity/LexAI.Identity.API/LexAI.Identity.API.csproj"], "options": {"env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5001"}}, "problemMatcher": "$msCompile", "group": "build", "isBackground": true}, {"label": "run-datapreprocessing-api", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/src/Services/DataPreprocessing/LexAI.DataPreprocessing.API/LexAI.DataPreprocessing.API.csproj"], "options": {"env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5002"}}, "problemMatcher": "$msCompile", "group": "build", "isBackground": true}, {"label": "run-apigateway", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/src/ApiGateway/LexAI.ApiGateway.csproj"], "options": {"env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5000"}}, "problemMatcher": "$msCompile", "group": "build", "isBackground": true}]}