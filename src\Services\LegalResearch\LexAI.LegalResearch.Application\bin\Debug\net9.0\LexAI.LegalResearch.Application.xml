<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.LegalResearch.Application</name>
    </assembly>
    <members>
        <member name="T:LexAI.LegalResearch.Application.Commands.PerformSearchCommand">
            <summary>
            Command to perform a legal search
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Commands.PerformSearchCommand.Request">
            <summary>
            Search request
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler">
            <summary>
            Handler for PerformSearchCommand
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.#ctor(LexAI.LegalResearch.Application.Interfaces.ILegalSearchService,LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository,Microsoft.Extensions.Logging.ILogger{LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler})">
            <summary>
            Initializes a new instance of the PerformSearchCommandHandler
            </summary>
            <param name="searchService">Legal search service</param>
            <param name="queryRepository">Search query repository</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(LexAI.LegalResearch.Application.Commands.PerformSearchCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the PerformSearchCommand
            </summary>
            <param name="request">Search command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search response</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Commands.IndexDocumentCommand">
            <summary>
            Command to index a legal document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Commands.IndexDocumentCommand.DocumentId">
            <summary>
            Document ID to index
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Commands.IndexDocumentCommand.ForceReindex">
            <summary>
            Force re-indexing even if already indexed
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Commands.IndexDocumentCommandHandler">
            <summary>
            Handler for IndexDocumentCommand
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Commands.IndexDocumentCommandHandler.#ctor(LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository,LexAI.LegalResearch.Application.Interfaces.IDocumentChunkingService,LexAI.LegalResearch.Application.Interfaces.IEmbeddingService,LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService,Microsoft.Extensions.Logging.ILogger{LexAI.LegalResearch.Application.Commands.IndexDocumentCommandHandler})">
            <summary>
            Initializes a new instance of the IndexDocumentCommandHandler
            </summary>
            <param name="documentRepository">Document repository</param>
            <param name="chunkingService">Document chunking service</param>
            <param name="embeddingService">Embedding service</param>
            <param name="vectorService">Vector database service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Commands.IndexDocumentCommandHandler.Handle(LexAI.LegalResearch.Application.Commands.IndexDocumentCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the IndexDocumentCommand
            </summary>
            <param name="request">Index command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if indexing was successful</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommand">
            <summary>
            Command to provide feedback on search results
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommand.QueryId">
            <summary>
            Search query ID
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommand.Feedback">
            <summary>
            User feedback
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommandHandler">
            <summary>
            Handler for ProvideSearchFeedbackCommand
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommandHandler.#ctor(LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository,Microsoft.Extensions.Logging.ILogger{LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommandHandler})">
            <summary>
            Initializes a new instance of the ProvideSearchFeedbackCommandHandler
            </summary>
            <param name="queryRepository">Search query repository</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommandHandler.Handle(LexAI.LegalResearch.Application.Commands.ProvideSearchFeedbackCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the ProvideSearchFeedbackCommand
            </summary>
            <param name="request">Feedback command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if feedback was recorded successfully</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.DocumentChunkDto">
            <summary>
            Document chunk DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentChunkDto.Id">
            <summary>
            Chunk ID
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentChunkDto.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentChunkDto.Content">
            <summary>
            Chunk content
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentChunkDto.Type">
            <summary>
            Chunk type
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentChunkDto.SequenceNumber">
            <summary>
            Sequence number
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentChunkDto.StartPosition">
            <summary>
            Start position in document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentChunkDto.EndPosition">
            <summary>
            End position in document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentChunkDto.EmbeddingVector">
            <summary>
            Embedding vector
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentChunkDto.Keywords">
            <summary>
            Keywords
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentChunkDto.Metadata">
            <summary>
            Metadata
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.VectorSearchResultDto">
            <summary>
            Vector search result DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.VectorSearchResultDto.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.VectorSearchResultDto.SimilarityScore">
            <summary>
            Similarity score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.VectorSearchResultDto.MatchedChunk">
            <summary>
            Matched chunk
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.VectorSearchResultDto.Distance">
            <summary>
            Distance from query vector
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.VectorSearchResultDto.Metadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.VectorDatabaseStatsDto">
            <summary>
            Vector database statistics DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.VectorDatabaseStatsDto.TotalDocuments">
            <summary>
            Total number of documents indexed
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.VectorDatabaseStatsDto.TotalChunks">
            <summary>
            Total number of chunks stored
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.VectorDatabaseStatsDto.AverageChunksPerDocument">
            <summary>
            Average chunks per document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.VectorDatabaseStatsDto.IndexSizeBytes">
            <summary>
            Index size in bytes
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.VectorDatabaseStatsDto.LastIndexed">
            <summary>
            Last indexing timestamp
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.VectorDatabaseStatsDto.EmbeddingModel">
            <summary>
            Embedding model used
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.VectorDatabaseStatsDto.EmbeddingDimension">
            <summary>
            Embedding dimension
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.QueryAnalysisDto">
            <summary>
            Query analysis DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.QueryAnalysisDto.OriginalQuery">
            <summary>
            Original query
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.QueryAnalysisDto.ProcessedQuery">
            <summary>
            Processed query
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.QueryAnalysisDto.Intent">
            <summary>
            Detected intent
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.QueryAnalysisDto.Entities">
            <summary>
            Extracted entities
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.QueryAnalysisDto.ExpandedTerms">
            <summary>
            Expanded terms
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.QueryAnalysisDto.ConfidenceScore">
            <summary>
            Confidence score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.QueryAnalysisDto.SuggestedFilters">
            <summary>
            Suggested filters
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.LegalEntityDto">
            <summary>
            Legal entity DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.LegalEntityDto.Text">
            <summary>
            Entity text
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.LegalEntityDto.Type">
            <summary>
            Entity type
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.LegalEntityDto.StartPosition">
            <summary>
            Start position in query
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.LegalEntityDto.EndPosition">
            <summary>
            End position in query
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.LegalEntityDto.Confidence">
            <summary>
            Confidence score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.LegalEntityDto.Metadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.UserFeedbackDto">
            <summary>
            User feedback DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.UserFeedbackDto.OverallRating">
            <summary>
            Overall rating (1-5)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.UserFeedbackDto.RelevanceRating">
            <summary>
            Relevance rating (1-5)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.UserFeedbackDto.CompletenessRating">
            <summary>
            Completeness rating (1-5)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.UserFeedbackDto.UsefulnessRating">
            <summary>
            Usefulness rating (1-5)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.UserFeedbackDto.Comments">
            <summary>
            Additional comments
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.UserFeedbackDto.CreatedAt">
            <summary>
            Feedback timestamp
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.SearchAnalyticsDto">
            <summary>
            Search analytics DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchAnalyticsDto.TotalSearches">
            <summary>
            Total number of searches
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchAnalyticsDto.AverageExecutionTime">
            <summary>
            Average execution time in milliseconds
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchAnalyticsDto.AverageResultCount">
            <summary>
            Average number of results per search
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchAnalyticsDto.TopQueries">
            <summary>
            Most popular queries
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchAnalyticsDto.TopDomains">
            <summary>
            Most searched legal domains
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchAnalyticsDto.SearchTrends">
            <summary>
            Search trends over time
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchAnalyticsDto.SuccessRate">
            <summary>
            Success rate (searches with results)
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchAnalyticsDto.AverageSatisfactionScore">
            <summary>
            Average user satisfaction score
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.SearchRequestDto">
            <summary>
            Search request DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.Query">
            <summary>
            Search query text
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.UserId">
            <summary>
            User ID performing the search
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.SessionId">
            <summary>
            Session ID for grouping searches
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.Method">
            <summary>
            Search method to use
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.DomainFilter">
            <summary>
            Legal domain filter
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.TypeFilter">
            <summary>
            Document type filter
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.LanguageFilter">
            <summary>
            Language filter
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.DateFilter">
            <summary>
            Date range filter
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.Limit">
            <summary>
            Maximum number of results
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.Offset">
            <summary>
            Result offset for pagination
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.MinRelevanceScore">
            <summary>
            Minimum relevance score threshold
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.IncludeHighlights">
            <summary>
            Include highlights in results
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.IncludeSimilar">
            <summary>
            Include similar documents
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.SortOrder">
            <summary>
            Sort order for results
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchRequestDto.Parameters">
            <summary>
            Additional search parameters
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.SearchResponseDto">
            <summary>
            Search response DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.QueryId">
            <summary>
            Search query ID
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.Query">
            <summary>
            Original query text
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.ProcessedQuery">
            <summary>
            Processed query text
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.Results">
            <summary>
            Search results
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.TotalResults">
            <summary>
            Total number of results found
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.ExecutionTimeMs">
            <summary>
            Search execution time in milliseconds
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.Method">
            <summary>
            Search method used
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.Intent">
            <summary>
            Query intent detected
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.QualityScore">
            <summary>
            Search quality score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.IsCached">
            <summary>
            Whether results were cached
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.Suggestions">
            <summary>
            Search suggestions for query improvement
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.RelatedTerms">
            <summary>
            Related search terms
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.Facets">
            <summary>
            Faceted search results
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResponseDto.Metadata">
            <summary>
            Search metadata
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.SearchResultDto">
            <summary>
            Search result DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.Title">
            <summary>
            Document title
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.Summary">
            <summary>
            Document summary
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.RelevanceScore">
            <summary>
            Relevance score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.SimilarityScore">
            <summary>
            Semantic similarity score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.KeywordScore">
            <summary>
            Keyword match score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.DocumentType">
            <summary>
            Document type
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.LegalDomain">
            <summary>
            Legal domain
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.Source">
            <summary>
            Document source
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.Highlights">
            <summary>
            Text highlights
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.MatchedChunks">
            <summary>
            Matched chunks
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.PublicationDate">
            <summary>
            Document publication date
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.EffectiveDate">
            <summary>
            Document effective date
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.Tags">
            <summary>
            Document tags
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.DocumentUrl">
            <summary>
            Document URL
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.Rank">
            <summary>
            Search rank
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.MatchExplanation">
            <summary>
            Match explanation
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchResultDto.SimilarDocuments">
            <summary>
            Similar documents
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.DocumentSourceDto">
            <summary>
            Document source DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentSourceDto.Name">
            <summary>
            Source name
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentSourceDto.Url">
            <summary>
            Source URL
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentSourceDto.Type">
            <summary>
            Source type
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentSourceDto.Authority">
            <summary>
            Authority level
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentSourceDto.Jurisdiction">
            <summary>
            Jurisdiction
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DocumentSourceDto.ReliabilityScore">
            <summary>
            Reliability score
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.TextHighlightDto">
            <summary>
            Text highlight DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.TextHighlightDto.Text">
            <summary>
            Highlighted text
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.TextHighlightDto.StartPosition">
            <summary>
            Start position in document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.TextHighlightDto.EndPosition">
            <summary>
            End position in document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.TextHighlightDto.Score">
            <summary>
            Highlight score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.TextHighlightDto.Type">
            <summary>
            Highlight type
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.MatchedChunkDto">
            <summary>
            Matched chunk DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.MatchedChunkDto.ChunkId">
            <summary>
            Chunk ID
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.MatchedChunkDto.Content">
            <summary>
            Chunk content
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.MatchedChunkDto.Type">
            <summary>
            Chunk type
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.MatchedChunkDto.SimilarityScore">
            <summary>
            Similarity score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.MatchedChunkDto.StartPosition">
            <summary>
            Start position in document
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.MatchedChunkDto.EndPosition">
            <summary>
            End position in document
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.SimilarDocumentDto">
            <summary>
            Similar document DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SimilarDocumentDto.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SimilarDocumentDto.Title">
            <summary>
            Document title
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SimilarDocumentDto.SimilarityScore">
            <summary>
            Similarity score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SimilarDocumentDto.DocumentType">
            <summary>
            Document type
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SimilarDocumentDto.LegalDomain">
            <summary>
            Legal domain
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.DateRangeDto">
            <summary>
            Date range DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DateRangeDto.StartDate">
            <summary>
            Start date
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.DateRangeDto.EndDate">
            <summary>
            End date
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.SearchFacetsDto">
            <summary>
            Search facets DTO
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchFacetsDto.DocumentTypes">
            <summary>
            Document type facets
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchFacetsDto.LegalDomains">
            <summary>
            Legal domain facets
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchFacetsDto.Sources">
            <summary>
            Source facets
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchFacetsDto.Years">
            <summary>
            Year facets
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.DTOs.SearchFacetsDto.Tags">
            <summary>
            Tag facets
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.DTOs.SearchSortOrder">
            <summary>
            Search sort order enumeration
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Application.DTOs.SearchSortOrder.Relevance">
            <summary>
            Sort by relevance score
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Application.DTOs.SearchSortOrder.DateDesc">
            <summary>
            Sort by publication date (newest first)
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Application.DTOs.SearchSortOrder.DateAsc">
            <summary>
            Sort by publication date (oldest first)
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Application.DTOs.SearchSortOrder.Title">
            <summary>
            Sort by title alphabetically
            </summary>
        </member>
        <member name="F:LexAI.LegalResearch.Application.DTOs.SearchSortOrder.Authority">
            <summary>
            Sort by authority level
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Interfaces.ILegalSearchService">
            <summary>
            Interface for legal search service
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalSearchService.SearchAsync(LexAI.LegalResearch.Application.DTOs.SearchRequestDto,System.Threading.CancellationToken)">
            <summary>
            Performs a semantic search for legal documents
            </summary>
            <param name="request">Search request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search response with results</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalSearchService.HybridSearchAsync(LexAI.LegalResearch.Application.DTOs.SearchRequestDto,System.Threading.CancellationToken)">
            <summary>
            Performs a hybrid search combining keyword and semantic search
            </summary>
            <param name="request">Search request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search response with results</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalSearchService.FindSimilarDocumentsAsync(System.Guid,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Finds similar documents to a given document
            </summary>
            <param name="documentId">Document ID</param>
            <param name="limit">Maximum number of similar documents</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Similar documents</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalSearchService.GetSearchSuggestionsAsync(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets search suggestions based on partial query
            </summary>
            <param name="partialQuery">Partial query text</param>
            <param name="limit">Maximum number of suggestions</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search suggestions</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalSearchService.AnalyzeQueryAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Analyzes query intent and extracts entities
            </summary>
            <param name="query">Query text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Query analysis result</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalSearchService.GetSearchAnalyticsAsync(System.Nullable{System.Guid},System.String,System.Threading.CancellationToken)">
            <summary>
            Gets search analytics for a user or session
            </summary>
            <param name="userId">User ID</param>
            <param name="sessionId">Session ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search analytics</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Interfaces.IEmbeddingService">
            <summary>
            Interface for document embedding service
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IEmbeddingService.GenerateEmbeddingAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Generates embedding vector for text
            </summary>
            <param name="text">Text to embed</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Embedding vector</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IEmbeddingService.GenerateEmbeddingsAsync(System.Collections.Generic.IEnumerable{System.String},System.Threading.CancellationToken)">
            <summary>
            Generates embeddings for multiple texts
            </summary>
            <param name="texts">Texts to embed</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Embedding vectors</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IEmbeddingService.CalculateSimilarity(System.Single[],System.Single[])">
            <summary>
            Calculates cosine similarity between two embedding vectors
            </summary>
            <param name="vector1">First vector</param>
            <param name="vector2">Second vector</param>
            <returns>Cosine similarity score</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IEmbeddingService.GetEmbeddingDimension">
            <summary>
            Gets the embedding model dimension
            </summary>
            <returns>Embedding dimension</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Interfaces.IDocumentChunkingService">
            <summary>
            Interface for document chunking service
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IDocumentChunkingService.ChunkDocumentAsync(System.Guid,System.String,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Splits document content into chunks for vector search
            </summary>
            <param name="documentId">Document ID</param>
            <param name="content">Document content</param>
            <param name="chunkSize">Maximum chunk size in characters</param>
            <param name="overlap">Overlap between chunks in characters</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Document chunks</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IDocumentChunkingService.ExtractKeywordsAsync(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Extracts keywords from text
            </summary>
            <param name="text">Text to analyze</param>
            <param name="maxKeywords">Maximum number of keywords</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extracted keywords</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IDocumentChunkingService.IdentifyChunkTypeAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Identifies the type of text chunk
            </summary>
            <param name="text">Text to analyze</param>
            <param name="context">Surrounding context</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Chunk type</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService">
            <summary>
            Interface for vector database operations
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService.StoreChunksAsync(System.Collections.Generic.IEnumerable{LexAI.LegalResearch.Application.DTOs.DocumentChunkDto},System.Threading.CancellationToken)">
            <summary>
            Stores document chunks with embeddings in vector database
            </summary>
            <param name="chunks">Document chunks with embeddings</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService.SearchSimilarAsync(System.Single[],System.Int32,System.Double,System.Collections.Generic.Dictionary{System.String,System.Object},System.Threading.CancellationToken)">
            <summary>
            Performs vector similarity search
            </summary>
            <param name="queryVector">Query embedding vector</param>
            <param name="limit">Maximum number of results</param>
            <param name="threshold">Minimum similarity threshold</param>
            <param name="filters">Optional filters</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Similar chunks with scores</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService.UpdateDocumentEmbeddingsAsync(System.Guid,System.Collections.Generic.IEnumerable{LexAI.LegalResearch.Application.DTOs.DocumentChunkDto},System.Threading.CancellationToken)">
            <summary>
            Updates embeddings for a document
            </summary>
            <param name="documentId">Document ID</param>
            <param name="chunks">Updated chunks</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService.DeleteDocumentEmbeddingsAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Deletes document embeddings from vector database
            </summary>
            <param name="documentId">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService.GetStatsAsync(System.Threading.CancellationToken)">
            <summary>
            Gets vector database statistics
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Database statistics</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Interfaces.IQueryProcessingService">
            <summary>
            Interface for query processing service
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IQueryProcessingService.ProcessQueryAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Processes and normalizes search query
            </summary>
            <param name="query">Raw query</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Processed query</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IQueryProcessingService.ExpandQueryAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Expands query with synonyms and related terms
            </summary>
            <param name="query">Original query</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Expanded query terms</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IQueryProcessingService.DetectIntentAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Detects query intent
            </summary>
            <param name="query">Query text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Query intent</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.IQueryProcessingService.ExtractEntitiesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Extracts legal entities from query
            </summary>
            <param name="query">Query text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extracted entities</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository">
            <summary>
            Repository interface for legal documents
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a document by ID
            </summary>
            <param name="id">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Document or null if not found</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository.AddAsync(LexAI.LegalResearch.Domain.Entities.LegalDocument,System.Threading.CancellationToken)">
            <summary>
            Adds a new document
            </summary>
            <param name="document">Document to add</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository.UpdateAsync(LexAI.LegalResearch.Domain.Entities.LegalDocument,System.Threading.CancellationToken)">
            <summary>
            Updates a document
            </summary>
            <param name="document">Document to update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository.DeleteAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Deletes a document
            </summary>
            <param name="id">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository.GetByDomainAsync(LexAI.LegalResearch.Domain.ValueObjects.LegalDomain,System.Threading.CancellationToken)">
            <summary>
            Gets documents by legal domain
            </summary>
            <param name="domain">Legal domain</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Documents in the domain</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository.GetByTypeAsync(LexAI.LegalResearch.Domain.ValueObjects.DocumentType,System.Threading.CancellationToken)">
            <summary>
            Gets documents by type
            </summary>
            <param name="type">Document type</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Documents of the type</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository.GetBySourceAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets documents by source
            </summary>
            <param name="sourceName">Source name</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Documents from the source</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository.GetByPublicationDateRangeAsync(System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Gets documents published within a date range
            </summary>
            <param name="startDate">Start date</param>
            <param name="endDate">End date</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Documents published in the date range</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository.GetUnindexedDocumentsAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets documents that need indexing
            </summary>
            <param name="limit">Maximum number of documents</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Documents that need indexing</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository.SearchByContentAsync(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Searches documents by text content
            </summary>
            <param name="searchText">Search text</param>
            <param name="limit">Maximum number of results</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Matching documents</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository.GetByTagsAsync(System.Collections.Generic.IEnumerable{System.String},System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Gets documents with tags
            </summary>
            <param name="tags">Tags to search for</param>
            <param name="matchAll">Whether to match all tags or any tag</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Documents with matching tags</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository.GetStatisticsAsync(System.Threading.CancellationToken)">
            <summary>
            Gets document statistics
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Document statistics</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository">
            <summary>
            Repository interface for search queries
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a search query by ID
            </summary>
            <param name="id">Query ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search query or null if not found</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository.AddAsync(LexAI.LegalResearch.Domain.Entities.SearchQuery,System.Threading.CancellationToken)">
            <summary>
            Adds a new search query
            </summary>
            <param name="query">Query to add</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository.UpdateAsync(LexAI.LegalResearch.Domain.Entities.SearchQuery,System.Threading.CancellationToken)">
            <summary>
            Updates a search query
            </summary>
            <param name="query">Query to update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository.DeleteAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Deletes a search query
            </summary>
            <param name="id">Query ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository.GetByUserAsync(System.Guid,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets search queries by user
            </summary>
            <param name="userId">User ID</param>
            <param name="limit">Maximum number of queries</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>User's search queries</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository.GetBySessionAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets search queries by session
            </summary>
            <param name="sessionId">Session ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Session's search queries</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository.GetByDateRangeAsync(System.DateTime,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Gets search queries within a date range
            </summary>
            <param name="startDate">Start date</param>
            <param name="endDate">End date</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search queries in the date range</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository.GetPopularQueriesAsync(System.Int32,System.Nullable{System.TimeSpan},System.Threading.CancellationToken)">
            <summary>
            Gets most popular search queries
            </summary>
            <param name="limit">Maximum number of queries</param>
            <param name="timeRange">Time range for popularity calculation</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Popular search queries</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository.GetQueriesWithFeedbackAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets search queries with feedback
            </summary>
            <param name="minRating">Minimum feedback rating</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search queries with feedback</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Application.Interfaces.ISearchQueryRepository.GetAnalyticsAsync(System.Nullable{System.Guid},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Gets search analytics
            </summary>
            <param name="userId">Optional user ID filter</param>
            <param name="startDate">Start date for analytics</param>
            <param name="endDate">End date for analytics</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search analytics</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Interfaces.DocumentStatistics">
            <summary>
            Document statistics model
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.DocumentStatistics.TotalDocuments">
            <summary>
            Total number of documents
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.DocumentStatistics.IndexedDocuments">
            <summary>
            Number of indexed documents
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.DocumentStatistics.DocumentsByType">
            <summary>
            Number of documents by type
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.DocumentStatistics.DocumentsByDomain">
            <summary>
            Number of documents by domain
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.DocumentStatistics.DocumentsBySource">
            <summary>
            Number of documents by source
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.DocumentStatistics.AverageDocumentLength">
            <summary>
            Average document length in characters
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.DocumentStatistics.TotalStorageSize">
            <summary>
            Total storage size in bytes
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.DocumentStatistics.LastUpdated">
            <summary>
            Last update timestamp
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Application.Interfaces.SearchAnalytics">
            <summary>
            Search analytics model
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.SearchAnalytics.TotalSearches">
            <summary>
            Total number of searches
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.SearchAnalytics.SuccessfulSearches">
            <summary>
            Number of successful searches
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.SearchAnalytics.AverageExecutionTime">
            <summary>
            Average execution time in milliseconds
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.SearchAnalytics.AverageResultCount">
            <summary>
            Average number of results per search
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.SearchAnalytics.PopularTerms">
            <summary>
            Most popular search terms
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.SearchAnalytics.SearchTrends">
            <summary>
            Search trends by date
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.SearchAnalytics.AverageSatisfactionScore">
            <summary>
            Average user satisfaction score
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.SearchAnalytics.SearchesByDomain">
            <summary>
            Number of searches by domain
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.SearchAnalytics.SearchesByMethod">
            <summary>
            Number of searches by method
            </summary>
        </member>
        <member name="P:LexAI.LegalResearch.Application.Interfaces.SearchAnalytics.GeneratedAt">
            <summary>
            Analytics generation timestamp
            </summary>
        </member>
    </members>
</doc>
