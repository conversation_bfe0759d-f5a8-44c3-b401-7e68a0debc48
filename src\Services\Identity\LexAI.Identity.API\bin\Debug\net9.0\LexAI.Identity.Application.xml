<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.Identity.Application</name>
    </assembly>
    <members>
        <member name="T:LexAI.Identity.Application.Commands.LoginCommand">
            <summary>
            Command to authenticate a user
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.LoginCommand.Email">
            <summary>
            User's email address
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.LoginCommand.Password">
            <summary>
            User's password
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.LoginCommand.RememberMe">
            <summary>
            Remember me flag for extended session
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.LoginCommand.IpAddress">
            <summary>
            IP address of the login attempt
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.LoginCommand.UserAgent">
            <summary>
            User agent of the client
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.Commands.LoginCommandHandler">
            <summary>
            Handler for LoginCommand
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Commands.LoginCommandHandler.#ctor(LexAI.Identity.Application.Interfaces.IUserRepository,LexAI.Identity.Application.Interfaces.IRefreshTokenRepository,LexAI.Identity.Application.Interfaces.ITokenService,Microsoft.Extensions.Logging.ILogger{LexAI.Identity.Application.Commands.LoginCommandHandler})">
            <summary>
            Initializes a new instance of the LoginCommandHandler
            </summary>
            <param name="userRepository">User repository</param>
            <param name="refreshTokenRepository">Refresh token repository</param>
            <param name="tokenService">Token service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LexAI.Identity.Application.Commands.LoginCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the LoginCommand
            </summary>
            <param name="request">Login command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Authentication response</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Commands.RefreshTokenCommand">
            <summary>
            Command to refresh an access token
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.RefreshTokenCommand.RefreshToken">
            <summary>
            Refresh token value
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.RefreshTokenCommand.IpAddress">
            <summary>
            IP address of the refresh request
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.RefreshTokenCommand.UserAgent">
            <summary>
            User agent of the client
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.Commands.RefreshTokenCommandHandler">
            <summary>
            Handler for RefreshTokenCommand
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.#ctor(LexAI.Identity.Application.Interfaces.IUserRepository,LexAI.Identity.Application.Interfaces.IRefreshTokenRepository,LexAI.Identity.Application.Interfaces.ITokenService,Microsoft.Extensions.Logging.ILogger{LexAI.Identity.Application.Commands.RefreshTokenCommandHandler})">
            <summary>
            Initializes a new instance of the RefreshTokenCommandHandler
            </summary>
            <param name="userRepository">User repository</param>
            <param name="refreshTokenRepository">Refresh token repository</param>
            <param name="tokenService">Token service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(LexAI.Identity.Application.Commands.RefreshTokenCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the RefreshTokenCommand
            </summary>
            <param name="request">Refresh token command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Authentication response</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Commands.LogoutCommand">
            <summary>
            Command to logout a user
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.LogoutCommand.RefreshToken">
            <summary>
            Refresh token to revoke
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.LogoutCommand.UserId">
            <summary>
            User ID (if refresh token is not provided)
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.LogoutCommand.RevokeAllTokens">
            <summary>
            Revoke all user tokens
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.Commands.LogoutCommandHandler">
            <summary>
            Handler for LogoutCommand
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Commands.LogoutCommandHandler.#ctor(LexAI.Identity.Application.Interfaces.IRefreshTokenRepository,Microsoft.Extensions.Logging.ILogger{LexAI.Identity.Application.Commands.LogoutCommandHandler})">
            <summary>
            Initializes a new instance of the LogoutCommandHandler
            </summary>
            <param name="refreshTokenRepository">Refresh token repository</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.Identity.Application.Commands.LogoutCommandHandler.Handle(LexAI.Identity.Application.Commands.LogoutCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the LogoutCommand
            </summary>
            <param name="request">Logout command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if logout was successful</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Commands.ChangePasswordCommand">
            <summary>
            Command for changing a user's password
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.ChangePasswordCommand.UserId">
            <summary>
            The unique identifier of the user whose password is being changed
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.ChangePasswordCommand.CurrentPassword">
            <summary>
            The current password of the user
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.ChangePasswordCommand.NewPassword">
            <summary>
            The new password to set for the user
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.ChangePasswordCommand.ChangedBy">
            <summary>
            The identifier of the entity or person who initiated the password change
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.Commands.CreateUserCommand">
            <summary>
            Command to create a new user
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.CreateUserCommand.Email">
            <summary>
            User's email address
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.CreateUserCommand.FirstName">
            <summary>
            User's first name
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.CreateUserCommand.LastName">
            <summary>
            User's last name
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.CreateUserCommand.PhoneNumber">
            <summary>
            User's phone number (optional)
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.CreateUserCommand.Password">
            <summary>
            User's password
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.CreateUserCommand.Role">
            <summary>
            User's role in the system
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.CreateUserCommand.PreferredLanguage">
            <summary>
            User's preferred language
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.CreateUserCommand.TimeZone">
            <summary>
            User's timezone
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.CreateUserCommand.CreatedBy">
            <summary>
            ID of the user creating this user
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.Commands.CreateUserCommandHandler">
            <summary>
            Handler for CreateUserCommand
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Commands.CreateUserCommandHandler.#ctor(LexAI.Identity.Application.Interfaces.IUserRepository,LexAI.Identity.Application.Interfaces.IPasswordService,Microsoft.Extensions.Logging.ILogger{LexAI.Identity.Application.Commands.CreateUserCommandHandler})">
            <summary>
            Initializes a new instance of the CreateUserCommandHandler
            </summary>
            <param name="userRepository">User repository</param>
            <param name="passwordService">Password service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.Identity.Application.Commands.CreateUserCommandHandler.Handle(LexAI.Identity.Application.Commands.CreateUserCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the CreateUserCommand
            </summary>
            <param name="request">Create user command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Created user DTO</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Commands.UpdateUserCommand">
            <summary>
            Command to update user information
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.UpdateUserCommand.UserId">
            <summary>
            User ID to update
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.UpdateUserCommand.FirstName">
            <summary>
            User's first name
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.UpdateUserCommand.LastName">
            <summary>
            User's last name
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.UpdateUserCommand.PhoneNumber">
            <summary>
            User's phone number (optional)
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.UpdateUserCommand.PreferredLanguage">
            <summary>
            User's preferred language
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.UpdateUserCommand.TimeZone">
            <summary>
            User's timezone
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Commands.UpdateUserCommand.UpdatedBy">
            <summary>
            ID of the user performing the update
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.Commands.UpdateUserCommandHandler">
            <summary>
            Handler for UpdateUserCommand
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Commands.UpdateUserCommandHandler.#ctor(LexAI.Identity.Application.Interfaces.IUserRepository,Microsoft.Extensions.Logging.ILogger{LexAI.Identity.Application.Commands.UpdateUserCommandHandler})">
            <summary>
            Initializes a new instance of the UpdateUserCommandHandler
            </summary>
            <param name="userRepository">User repository</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.Identity.Application.Commands.UpdateUserCommandHandler.Handle(LexAI.Identity.Application.Commands.UpdateUserCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the UpdateUserCommand
            </summary>
            <param name="request">Update user command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Updated user DTO</returns>
        </member>
        <member name="T:LexAI.Identity.Application.DTOs.UserDto">
            <summary>
            Data transfer object for user information
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.Id">
            <summary>
            User's unique identifier
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.Email">
            <summary>
            User's email address
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.FirstName">
            <summary>
            User's first name
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.LastName">
            <summary>
            User's last name
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.FullName">
            <summary>
            User's full name
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.PhoneNumber">
            <summary>
            User's phone number
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.Role">
            <summary>
            User's role in the system
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.IsEmailVerified">
            <summary>
            Indicates if the user's email is verified
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.IsActive">
            <summary>
            Indicates if the user account is active
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.IsLocked">
            <summary>
            Indicates if the user account is locked
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.LastLoginAt">
            <summary>
            Date and time of the last login
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.PreferredLanguage">
            <summary>
            User's preferred language
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.TimeZone">
            <summary>
            User's timezone
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.ProfilePictureUrl">
            <summary>
            User's profile picture URL
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.CreatedAt">
            <summary>
            Date and time when the user was created
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UserDto.UpdatedAt">
            <summary>
            Date and time when the user was last updated
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.DTOs.CreateUserDto">
            <summary>
            Data transfer object for creating a new user
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.CreateUserDto.Email">
            <summary>
            User's email address
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.CreateUserDto.FirstName">
            <summary>
            User's first name
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.CreateUserDto.LastName">
            <summary>
            User's last name
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.CreateUserDto.PhoneNumber">
            <summary>
            User's phone number (optional)
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.CreateUserDto.Password">
            <summary>
            User's password
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.CreateUserDto.Role">
            <summary>
            User's role in the system
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.CreateUserDto.PreferredLanguage">
            <summary>
            User's preferred language
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.CreateUserDto.TimeZone">
            <summary>
            User's timezone
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.DTOs.UpdateUserDto">
            <summary>
            Data transfer object for updating user information
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UpdateUserDto.FirstName">
            <summary>
            User's first name
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UpdateUserDto.LastName">
            <summary>
            User's last name
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UpdateUserDto.PhoneNumber">
            <summary>
            User's phone number (optional)
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UpdateUserDto.PreferredLanguage">
            <summary>
            User's preferred language
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.UpdateUserDto.TimeZone">
            <summary>
            User's timezone
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.DTOs.LoginDto">
            <summary>
            Data transfer object for user login
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.LoginDto.Email">
            <summary>
            User's email address
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.LoginDto.Password">
            <summary>
            User's password
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.LoginDto.RememberMe">
            <summary>
            Remember me flag for extended session
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.DTOs.AuthenticationResponseDto">
            <summary>
            Data transfer object for authentication response
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.AuthenticationResponseDto.AccessToken">
            <summary>
            JWT access token
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.AuthenticationResponseDto.RefreshToken">
            <summary>
            Refresh token for obtaining new access tokens
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.AuthenticationResponseDto.TokenType">
            <summary>
            Token type (usually "Bearer")
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.AuthenticationResponseDto.ExpiresIn">
            <summary>
            Access token expiration time in seconds
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.AuthenticationResponseDto.User">
            <summary>
            User information
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.DTOs.ChangePasswordDto">
            <summary>
            Data transfer object for changing password
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.ChangePasswordDto.CurrentPassword">
            <summary>
            Current password
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.ChangePasswordDto.NewPassword">
            <summary>
            New password
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.ChangePasswordDto.ConfirmPassword">
            <summary>
            Confirmation of new password
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.DTOs.ForgotPasswordDto">
            <summary>
            Data transfer object for password reset request
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.ForgotPasswordDto.Email">
            <summary>
            User's email address
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.DTOs.ResetPasswordDto">
            <summary>
            Data transfer object for password reset
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.ResetPasswordDto.Token">
            <summary>
            Password reset token
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.ResetPasswordDto.Email">
            <summary>
            User's email address
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.ResetPasswordDto.NewPassword">
            <summary>
            New password
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.ResetPasswordDto.ConfirmPassword">
            <summary>
            Confirmation of new password
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.DTOs.RefreshTokenDto">
            <summary>
            Data transfer object for refresh token request
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.DTOs.RefreshTokenDto.RefreshToken">
            <summary>
            Refresh token
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.Interfaces.ITokenService">
            <summary>
            Service interface for JWT token operations
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.ITokenService.GenerateAccessToken(LexAI.Identity.Domain.Entities.User,System.Collections.Generic.IEnumerable{System.Security.Claims.Claim})">
            <summary>
            Generates a JWT access token for the user
            </summary>
            <param name="user">User entity</param>
            <param name="additionalClaims">Additional claims to include in the token</param>
            <returns>JWT access token</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.ITokenService.GenerateRefreshToken(System.Guid,System.String,System.String)">
            <summary>
            Generates a refresh token for the user
            </summary>
            <param name="userId">User ID</param>
            <param name="ipAddress">IP address where the token was created</param>
            <param name="userAgent">User agent of the client</param>
            <returns>Refresh token entity</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.ITokenService.ValidateAccessToken(System.String)">
            <summary>
            Validates a JWT access token
            </summary>
            <param name="token">JWT token to validate</param>
            <returns>ClaimsPrincipal if valid, null otherwise</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.ITokenService.GetUserIdFromToken(System.String)">
            <summary>
            Extracts user ID from a JWT token
            </summary>
            <param name="token">JWT token</param>
            <returns>User ID if found, null otherwise</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.ITokenService.GetClaimsFromToken(System.String)">
            <summary>
            Extracts claims from a JWT token
            </summary>
            <param name="token">JWT token</param>
            <returns>Collection of claims</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.ITokenService.IsTokenExpired(System.String)">
            <summary>
            Checks if a JWT token is expired
            </summary>
            <param name="token">JWT token</param>
            <returns>True if expired, false otherwise</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.ITokenService.GetTokenExpiration(System.String)">
            <summary>
            Gets the expiration time of a JWT token
            </summary>
            <param name="token">JWT token</param>
            <returns>Expiration time or null if invalid</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Interfaces.IPasswordService">
            <summary>
            Service interface for password operations
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IPasswordService.HashPassword(System.String)">
            <summary>
            Hashes a password using a secure algorithm
            </summary>
            <param name="password">Plain text password</param>
            <returns>Hashed password</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IPasswordService.VerifyPassword(System.String,System.String)">
            <summary>
            Verifies a password against its hash
            </summary>
            <param name="password">Plain text password</param>
            <param name="hash">Hashed password</param>
            <returns>True if password matches hash</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IPasswordService.GeneratePassword(System.Int32,System.Boolean)">
            <summary>
            Generates a secure random password
            </summary>
            <param name="length">Password length (minimum 8)</param>
            <param name="includeSpecialChars">Include special characters</param>
            <returns>Generated password</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IPasswordService.ValidatePasswordStrength(System.String)">
            <summary>
            Validates password strength
            </summary>
            <param name="password">Password to validate</param>
            <returns>Password validation result</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IPasswordService.GeneratePasswordResetToken">
            <summary>
            Generates a password reset token
            </summary>
            <returns>Password reset token</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IPasswordService.GenerateEmailVerificationToken">
            <summary>
            Generates an email verification token
            </summary>
            <returns>Email verification token</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Interfaces.PasswordValidationResult">
            <summary>
            Result of password validation
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Interfaces.PasswordValidationResult.IsValid">
            <summary>
            Indicates if the password is valid
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Interfaces.PasswordValidationResult.Score">
            <summary>
            Password strength score (0-100)
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Interfaces.PasswordValidationResult.Errors">
            <summary>
            List of validation errors
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Interfaces.PasswordValidationResult.Suggestions">
            <summary>
            List of suggestions for improvement
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Interfaces.PasswordValidationResult.Strength">
            <summary>
            Password strength level
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.Interfaces.PasswordStrength">
            <summary>
            Password strength levels
            </summary>
        </member>
        <member name="F:LexAI.Identity.Application.Interfaces.PasswordStrength.VeryWeak">
            <summary>
            Very weak password
            </summary>
        </member>
        <member name="F:LexAI.Identity.Application.Interfaces.PasswordStrength.Weak">
            <summary>
            Weak password
            </summary>
        </member>
        <member name="F:LexAI.Identity.Application.Interfaces.PasswordStrength.Fair">
            <summary>
            Fair password
            </summary>
        </member>
        <member name="F:LexAI.Identity.Application.Interfaces.PasswordStrength.Good">
            <summary>
            Good password
            </summary>
        </member>
        <member name="F:LexAI.Identity.Application.Interfaces.PasswordStrength.Strong">
            <summary>
            Strong password
            </summary>
        </member>
        <member name="F:LexAI.Identity.Application.Interfaces.PasswordStrength.VeryStrong">
            <summary>
            Very strong password
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.Interfaces.IUserRepository">
            <summary>
            Repository interface for User entity operations
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IUserRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a user by their unique identifier
            </summary>
            <param name="id">User ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>User entity or null if not found</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IUserRepository.GetByEmailAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets a user by their email address
            </summary>
            <param name="email">Email address</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>User entity or null if not found</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IUserRepository.GetAllAsync(System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets all users with pagination
            </summary>
            <param name="pageNumber">Page number (1-based)</param>
            <param name="pageSize">Number of items per page</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Paginated list of users</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IUserRepository.SearchAsync(System.String,System.Nullable{LexAI.Shared.Domain.Enums.UserRole},System.Nullable{System.Boolean},System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Searches users by criteria
            </summary>
            <param name="searchTerm">Search term for name or email</param>
            <param name="role">Optional role filter</param>
            <param name="isActive">Optional active status filter</param>
            <param name="pageNumber">Page number (1-based)</param>
            <param name="pageSize">Number of items per page</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Paginated search results</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IUserRepository.AddAsync(LexAI.Identity.Domain.Entities.User,System.Threading.CancellationToken)">
            <summary>
            Adds a new user to the repository
            </summary>
            <param name="user">User entity to add</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Added user entity</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IUserRepository.UpdateAsync(LexAI.Identity.Domain.Entities.User,System.Threading.CancellationToken)">
            <summary>
            Updates an existing user
            </summary>
            <param name="user">User entity to update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Updated user entity</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IUserRepository.DeleteAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Soft deletes a user
            </summary>
            <param name="id">User ID to delete</param>
            <param name="deletedBy">ID of the user performing the deletion</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if deletion was successful</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IUserRepository.ExistsAsync(System.String,System.Nullable{System.Guid},System.Threading.CancellationToken)">
            <summary>
            Checks if a user exists with the given email
            </summary>
            <param name="email">Email address to check</param>
            <param name="excludeUserId">Optional user ID to exclude from the check</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if user exists</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IUserRepository.GetByRoleAsync(LexAI.Shared.Domain.Enums.UserRole,System.Threading.CancellationToken)">
            <summary>
            Gets users by role
            </summary>
            <param name="role">User role</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of users with the specified role</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IUserRepository.GetLockedUsersAsync(System.Threading.CancellationToken)">
            <summary>
            Gets locked users
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of locked users</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IUserRepository.GetUnverifiedUsersAsync(System.Threading.CancellationToken)">
            <summary>
            Gets users with unverified emails
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of users with unverified emails</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Interfaces.IRefreshTokenRepository">
            <summary>
            Repository interface for RefreshToken entity operations
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IRefreshTokenRepository.GetByTokenAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets a refresh token by its value
            </summary>
            <param name="token">Token value</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>RefreshToken entity or null if not found</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IRefreshTokenRepository.GetActiveTokensByUserIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets all active refresh tokens for a user
            </summary>
            <param name="userId">User ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of active refresh tokens</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IRefreshTokenRepository.AddAsync(LexAI.Identity.Domain.Entities.RefreshToken,System.Threading.CancellationToken)">
            <summary>
            Adds a new refresh token
            </summary>
            <param name="refreshToken">RefreshToken entity to add</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Added refresh token</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IRefreshTokenRepository.UpdateAsync(LexAI.Identity.Domain.Entities.RefreshToken,System.Threading.CancellationToken)">
            <summary>
            Updates an existing refresh token
            </summary>
            <param name="refreshToken">RefreshToken entity to update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Updated refresh token</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IRefreshTokenRepository.RevokeAllUserTokensAsync(System.Guid,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Revokes all refresh tokens for a user
            </summary>
            <param name="userId">User ID</param>
            <param name="revokedBy">ID of the user revoking the tokens</param>
            <param name="reason">Reason for revocation</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Number of tokens revoked</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Interfaces.IRefreshTokenRepository.RemoveExpiredTokensAsync(System.Threading.CancellationToken)">
            <summary>
            Removes expired refresh tokens
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Number of tokens removed</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Queries.GetUserByIdQuery">
            <summary>
            Query to get a user by ID
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Queries.GetUserByIdQuery.UserId">
            <summary>
            User ID to retrieve
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Queries.GetUserByIdQuery.#ctor(System.Guid)">
            <summary>
            Initializes a new instance of GetUserByIdQuery
            </summary>
            <param name="userId">User ID</param>
        </member>
        <member name="T:LexAI.Identity.Application.Queries.GetUserByIdQueryHandler">
            <summary>
            Handler for GetUserByIdQuery
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Queries.GetUserByIdQueryHandler.#ctor(LexAI.Identity.Application.Interfaces.IUserRepository,Microsoft.Extensions.Logging.ILogger{LexAI.Identity.Application.Queries.GetUserByIdQueryHandler})">
            <summary>
            Initializes a new instance of the GetUserByIdQueryHandler
            </summary>
            <param name="userRepository">User repository</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.Identity.Application.Queries.GetUserByIdQueryHandler.Handle(LexAI.Identity.Application.Queries.GetUserByIdQuery,System.Threading.CancellationToken)">
            <summary>
            Handles the GetUserByIdQuery
            </summary>
            <param name="request">Get user by ID query</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>User DTO</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Queries.GetUserByEmailQuery">
            <summary>
            Query to get a user by email
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Queries.GetUserByEmailQuery.Email">
            <summary>
            Email address to search for
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Queries.GetUserByEmailQuery.#ctor(System.String)">
            <summary>
            Initializes a new instance of GetUserByEmailQuery
            </summary>
            <param name="email">Email address</param>
        </member>
        <member name="T:LexAI.Identity.Application.Queries.GetUserByEmailQueryHandler">
            <summary>
            Handler for GetUserByEmailQuery
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Queries.GetUserByEmailQueryHandler.#ctor(LexAI.Identity.Application.Interfaces.IUserRepository,Microsoft.Extensions.Logging.ILogger{LexAI.Identity.Application.Queries.GetUserByEmailQueryHandler})">
            <summary>
            Initializes a new instance of the GetUserByEmailQueryHandler
            </summary>
            <param name="userRepository">User repository</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.Identity.Application.Queries.GetUserByEmailQueryHandler.Handle(LexAI.Identity.Application.Queries.GetUserByEmailQuery,System.Threading.CancellationToken)">
            <summary>
            Handles the GetUserByEmailQuery
            </summary>
            <param name="request">Get user by email query</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>User DTO or null if not found</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Queries.GetUsersQuery">
            <summary>
            Query to get all users with pagination
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Queries.GetUsersQuery.PageNumber">
            <summary>
            Page number (1-based)
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Queries.GetUsersQuery.PageSize">
            <summary>
            Number of items per page
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Queries.GetUsersQuery.SearchTerm">
            <summary>
            Search term for name or email
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Queries.GetUsersQuery.Role">
            <summary>
            Optional role filter
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Queries.GetUsersQuery.IsActive">
            <summary>
            Optional active status filter
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.Queries.GetUsersQueryHandler">
            <summary>
            Handler for GetUsersQuery
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Queries.GetUsersQueryHandler.#ctor(LexAI.Identity.Application.Interfaces.IUserRepository,Microsoft.Extensions.Logging.ILogger{LexAI.Identity.Application.Queries.GetUsersQueryHandler})">
            <summary>
            Initializes a new instance of the GetUsersQueryHandler
            </summary>
            <param name="userRepository">User repository</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.Identity.Application.Queries.GetUsersQueryHandler.Handle(LexAI.Identity.Application.Queries.GetUsersQuery,System.Threading.CancellationToken)">
            <summary>
            Handles the GetUsersQuery
            </summary>
            <param name="request">Get users query</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Paginated list of user DTOs</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Queries.GetUsersByRoleQuery">
            <summary>
            Query to get users by role
            </summary>
        </member>
        <member name="P:LexAI.Identity.Application.Queries.GetUsersByRoleQuery.Role">
            <summary>
            User role to filter by
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Queries.GetUsersByRoleQuery.#ctor(LexAI.Shared.Domain.Enums.UserRole)">
            <summary>
            Initializes a new instance of GetUsersByRoleQuery
            </summary>
            <param name="role">User role</param>
        </member>
        <member name="T:LexAI.Identity.Application.Queries.GetUsersByRoleQueryHandler">
            <summary>
            Handler for GetUsersByRoleQuery
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Queries.GetUsersByRoleQueryHandler.#ctor(LexAI.Identity.Application.Interfaces.IUserRepository,Microsoft.Extensions.Logging.ILogger{LexAI.Identity.Application.Queries.GetUsersByRoleQueryHandler})">
            <summary>
            Initializes a new instance of the GetUsersByRoleQueryHandler
            </summary>
            <param name="userRepository">User repository</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.Identity.Application.Queries.GetUsersByRoleQueryHandler.Handle(LexAI.Identity.Application.Queries.GetUsersByRoleQuery,System.Threading.CancellationToken)">
            <summary>
            Handles the GetUsersByRoleQuery
            </summary>
            <param name="request">Get users by role query</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of user DTOs</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Validators.CreateUserCommandValidator">
            <summary>
            Validator for CreateUserCommand
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Validators.CreateUserCommandValidator.#ctor(LexAI.Identity.Application.Interfaces.IUserRepository)">
            <summary>
            Initializes a new instance of the CreateUserCommandValidator
            </summary>
            <param name="userRepository">User repository for email uniqueness validation</param>
        </member>
        <member name="M:LexAI.Identity.Application.Validators.CreateUserCommandValidator.BeUniqueEmail(System.String,System.Threading.CancellationToken)">
            <summary>
            Validates that the email is unique
            </summary>
            <param name="email">Email to validate</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if email is unique</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Validators.CreateUserCommandValidator.BeValidLanguageCode(System.String)">
            <summary>
            Validates that the language code is valid
            </summary>
            <param name="languageCode">Language code to validate</param>
            <returns>True if valid</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Validators.CreateUserCommandValidator.BeValidTimeZone(System.String)">
            <summary>
            Validates that the time zone is valid
            </summary>
            <param name="timeZone">Time zone to validate</param>
            <returns>True if valid</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Validators.UpdateUserCommandValidator">
            <summary>
            Validator for UpdateUserCommand
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Validators.UpdateUserCommandValidator.#ctor">
            <summary>
            Initializes a new instance of the UpdateUserCommandValidator
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Validators.UpdateUserCommandValidator.BeValidLanguageCode(System.String)">
            <summary>
            Validates that the language code is valid
            </summary>
            <param name="languageCode">Language code to validate</param>
            <returns>True if valid</returns>
        </member>
        <member name="M:LexAI.Identity.Application.Validators.UpdateUserCommandValidator.BeValidTimeZone(System.String)">
            <summary>
            Validates that the time zone is valid
            </summary>
            <param name="timeZone">Time zone to validate</param>
            <returns>True if valid</returns>
        </member>
        <member name="T:LexAI.Identity.Application.Validators.LoginCommandValidator">
            <summary>
            Validator for LoginCommand
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Validators.LoginCommandValidator.#ctor">
            <summary>
            Initializes a new instance of the LoginCommandValidator
            </summary>
        </member>
        <member name="T:LexAI.Identity.Application.Validators.RefreshTokenCommandValidator">
            <summary>
            Validator for RefreshTokenCommand
            </summary>
        </member>
        <member name="M:LexAI.Identity.Application.Validators.RefreshTokenCommandValidator.#ctor">
            <summary>
            Initializes a new instance of the RefreshTokenCommandValidator
            </summary>
        </member>
    </members>
</doc>
