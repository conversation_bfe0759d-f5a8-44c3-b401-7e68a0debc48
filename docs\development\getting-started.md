# Guide de Développement LexAI

## 🚀 Démarrage Rapide

### P<PERSON>requis

- **.NET 9 SDK** - [Télécharger](https://dotnet.microsoft.com/download/dotnet/9.0)
- **Docker Desktop** - [Télécharger](https://www.docker.com/products/docker-desktop)
- **Visual Studio 2022** ou **VS Code** avec extensions C#
- **Git** pour le contrôle de version

### Installation

1. **Cloner le repository**
```bash
git clone <repository-url>
cd LexAi_Backend_V1
```

2. **Configurer les variables d'environnement**
```bash
# Copier le fichier d'exemple
cp .env.example .env

# Éditer le fichier .env avec vos clés API
# OPENAI_API_KEY=your-openai-api-key-here
```

3. **Lancer l'infrastructure avec Docker**
```bash
# Démarrer PostgreSQL, MongoDB, Redis, <PERSON><PERSON><PERSON>
docker-compose up -d postgres mongodb redis rabbitmq

# Ou avec ce script PowerShell
.\scripts\start-dev.ps1 -Infrastructure

# Attendre que tous les services soient prêts (environ 30 secondes)
docker-compose logs -f
```

4. **Restaurer les packages NuGet**
```bash
dotnet restore
```

5. **Appliquer les migrations de base de données**
```bash
# Utiliser le script powershell de migration : la premiere fois, il faut executer cette commande pour créer la migration du service identity
.\scripts\manage-migrations.ps1 -Action add -Name "InitialCreate"

# Appliquer les migrations avec le script poweshell pour chaque service avec une base de données
.\scripts\manage-migrations.ps1 -Action update
# Ou utiliser directement la commande docker pour chaque service avec une base de données
dotnet ef database update --project src/Services/Identity/LexAI.Identity.Infrastructure
```

6. **Lancer les services en développement**
```bash
# Option 1: Lancer tous les services avec Docker
docker-compose up

# Option 2: Lancer individuellement pour le développement
dotnet run --project src/ApiGateway
dotnet run --project src/Services/Identity/LexAI.Identity.API
dotnet run --project src/Services/LegalResearch/LexAI.LegalResearch.API
```

## 🏗️ Structure du Projet

```
LexAi_Backend_V1/
├── src/
│   ├── ApiGateway/                 # Point d'entrée principal
│   ├── Services/                   # Microservices
│   │   ├── Identity/              # Service d'authentification
│   │   │   ├── Domain/            # Entités et logique métier
│   │   │   ├── Application/       # Use cases et DTOs
│   │   │   ├── Infrastructure/    # Accès données et services externes
│   │   │   └── API/              # Contrôleurs et configuration
│   │   ├── LegalResearch/         # Service de recherche juridique
│   │   ├── AIAssistant/           # Service chatbot IA
│   │   ├── DocumentAnalysis/      # Service d'analyse de documents
│   │   ├── DocumentGenerator/     # Service de génération de documents
│   │   └── ClientManagement/      # Service de gestion clients
│   └── Shared/                    # Bibliothèques partagées
│       ├── Domain/               # Entités et exceptions communes
│       └── Infrastructure/       # Services d'infrastructure
├── tests/                         # Tests unitaires et d'intégration
├── docs/                         # Documentation
├── scripts/                      # Scripts d'initialisation
└── docker-compose.yml           # Configuration Docker
```

## 🧩 Clean Architecture

Chaque microservice suit le pattern Clean Architecture avec 4 couches :

### 1. Domain Layer (Couche Domaine)
- **Entités** : Objets métier avec logique
- **Value Objects** : Objets immuables
- **Domain Services** : Logique métier complexe
- **Interfaces** : Contrats pour les couches externes

```csharp
// Exemple d'entité
public class User : AuditableEntity
{
    public Email Email { get; private set; }
    public string FirstName { get; private set; }
    
    public static User Create(string email, string firstName)
    {
        // Logique de création avec validation
    }
}
```

### 2. Application Layer (Couche Application)
- **Use Cases** : Orchestration des opérations
- **DTOs** : Objets de transfert de données
- **Validators** : Validation des entrées
- **Mappers** : Transformation des objets

```csharp
// Exemple de use case avec MediatR
public class CreateUserCommand : IRequest<UserDto>
{
    public string Email { get; set; }
    public string FirstName { get; set; }
}

public class CreateUserHandler : IRequestHandler<CreateUserCommand, UserDto>
{
    public async Task<UserDto> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        // Logique de création utilisateur
    }
}
```

### 3. Infrastructure Layer (Couche Infrastructure)
- **Repositories** : Accès aux données
- **External Services** : APIs externes
- **Configuration** : Paramètres et options

```csharp
// Exemple de repository
public class UserRepository : IUserRepository
{
    private readonly ApplicationDbContext _context;
    
    public async Task<User?> GetByEmailAsync(string email)
    {
        return await _context.Users
            .FirstOrDefaultAsync(u => u.Email == email);
    }
}
```

### 4. Presentation Layer (Couche Présentation)
- **Controllers** : Points d'entrée API
- **Middleware** : Traitement des requêtes
- **Configuration** : Injection de dépendances

```csharp
// Exemple de contrôleur
[ApiController]
[Route("api/[controller]")]
public class UsersController : ControllerBase
{
    private readonly IMediator _mediator;
    
    [HttpPost]
    public async Task<ActionResult<UserDto>> CreateUser(CreateUserCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }
}
```

## 🧪 Tests

### Tests Unitaires
```bash
# Lancer tous les tests unitaires
dotnet test tests/LexAI.Identity.UnitTests/

# Avec couverture de code
dotnet test --collect:"XPlat Code Coverage"
```

### Tests d'Intégration
```bash
# Lancer les tests d'intégration
dotnet test tests/LexAI.Identity.IntegrationTests/
```

### Structure des Tests
```csharp
// Exemple de test unitaire
[Fact]
public void User_Create_ShouldCreateValidUser()
{
    // Arrange
    var email = "<EMAIL>";
    var firstName = "John";
    
    // Act
    var user = User.Create(email, firstName, "Doe", UserRole.Lawyer, "admin");
    
    // Assert
    Assert.NotNull(user);
    Assert.Equal(email, user.Email.Value);
}
```

## 📝 Conventions de Code

### Naming Conventions
- **Classes** : PascalCase (`UserService`)
- **Méthodes** : PascalCase (`GetUserById`)
- **Variables** : camelCase (`userId`)
- **Constantes** : UPPER_CASE (`MAX_RETRY_ATTEMPTS`)

### Documentation
- Documenter toutes les classes et méthodes publiques
- Utiliser XML Documentation Comments
- Inclure des exemples d'utilisation

```csharp
/// <summary>
/// Creates a new user in the system
/// </summary>
/// <param name="email">User's email address</param>
/// <param name="firstName">User's first name</param>
/// <returns>Created user entity</returns>
/// <exception cref="ArgumentException">Thrown when email is invalid</exception>
public static User Create(string email, string firstName)
{
    // Implementation
}
```

## 🔧 Configuration

### appsettings.json
```json
{
  "ConnectionStrings": {
    "PostgreSql": "Host=localhost;Database=identity_db;Username=lexai_user;Password=lexai_password_2024!",
    "MongoDB": "mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_documents?authSource=admin",
    "Redis": "localhost:6379"
  },
  "Jwt": {
    "SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long",
    "Issuer": "LexAI",
    "Audience": "LexAI-Users",
    "AccessTokenExpirationMinutes": 60,
    "RefreshTokenExpirationDays": 7
  },
  "OpenAI": {
    "ApiKey": "your-openai-api-key",
    "DefaultModel": "gpt-4",
    "EmbeddingModel": "text-embedding-ada-002"
  }
}
```

## 🐛 Debugging

### Logs
- Utiliser Serilog pour les logs structurés
- Niveaux : Debug, Information, Warning, Error, Fatal
- Corrélation des requêtes avec CorrelationId

```csharp
_logger.LogInformation("User {UserId} created successfully", user.Id);
_logger.LogError(ex, "Failed to create user with email {Email}", email);
```

### Health Checks
- Accéder aux health checks : `http://localhost:8080/health`
- Vérifier la santé de chaque service individuellement

## 🚀 Déploiement

### Build
```bash
# Build de la solution complète
dotnet build --configuration Release

# Build d'un service spécifique
dotnet build src/Services/Identity/LexAI.Identity.API --configuration Release
```

### Docker
```bash
# Build des images Docker
docker-compose build

# Push vers le registry
docker-compose push
```

## 📚 Ressources Utiles

- [Documentation .NET 9](https://docs.microsoft.com/en-us/dotnet/)
- [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [MediatR Documentation](https://github.com/jbogard/MediatR)
- [Entity Framework Core](https://docs.microsoft.com/en-us/ef/core/)
- [OpenAI API Documentation](https://platform.openai.com/docs)
