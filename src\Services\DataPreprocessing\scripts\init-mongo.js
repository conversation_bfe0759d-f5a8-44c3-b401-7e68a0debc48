// Script d'initialisation MongoDB pour le service Data Preprocessing

// Se connecter à la base de données admin
db = db.getSiblingDB('admin');

// Créer l'utilisateur pour l'application
db.createUser({
  user: 'lexai_admin',
  pwd: 'lexai_mongo_password_2024!',
  roles: [
    { role: 'readWriteAnyDatabase', db: 'admin' },
    { role: 'dbAdminAnyDatabase', db: 'admin' },
    { role: 'userAdminAnyDatabase', db: 'admin' }
  ]
});

// Se connecter à la base de données de l'application
db = db.getSiblingDB('lexai_preprocessing');

// Créer les collections principales
db.createCollection('legal_documents');
db.createCollection('legal_commercial_chunks');
db.createCollection('legal_labor_chunks');
db.createCollection('legal_civil_chunks');
db.createCollection('legal_criminal_chunks');
db.createCollection('legal_tax_chunks');
db.createCollection('legal_realestate_chunks');
db.createCollection('legal_family_chunks');
db.createCollection('legal_administrative_chunks');
db.createCollection('legal_constitutional_chunks');
db.createCollection('legal_intellectualproperty_chunks');
db.createCollection('legal_environmental_chunks');
db.createCollection('legal_health_chunks');
db.createCollection('legal_immigration_chunks');
db.createCollection('legal_international_chunks');
db.createCollection('legal_european_chunks');
db.createCollection('legal_banking_chunks');
db.createCollection('legal_insurance_chunks');
db.createCollection('legal_technology_chunks');
db.createCollection('legal_competition_chunks');
db.createCollection('legal_consumer_chunks');
db.createCollection('legal_other_chunks');

// Créer des index pour optimiser les performances
db.legal_documents.createIndex({ "chunkId": 1 });
db.legal_documents.createIndex({ "documentId": 1 });
db.legal_documents.createIndex({ "metadata.domain": 1 });
db.legal_documents.createIndex({ "metadata.createdAt": 1 });

// Créer des index pour les collections de chunks
const collections = [
  'legal_commercial_chunks', 'legal_labor_chunks', 'legal_civil_chunks',
  'legal_criminal_chunks', 'legal_tax_chunks', 'legal_realestate_chunks',
  'legal_family_chunks', 'legal_administrative_chunks', 'legal_constitutional_chunks',
  'legal_intellectualproperty_chunks', 'legal_environmental_chunks', 'legal_health_chunks',
  'legal_immigration_chunks', 'legal_international_chunks', 'legal_european_chunks',
  'legal_banking_chunks', 'legal_insurance_chunks', 'legal_technology_chunks',
  'legal_competition_chunks', 'legal_consumer_chunks', 'legal_other_chunks'
];

collections.forEach(function(collectionName) {
  db.getCollection(collectionName).createIndex({ "chunkId": 1 });
  db.getCollection(collectionName).createIndex({ "documentId": 1 });
  db.getCollection(collectionName).createIndex({ "metadata.qualityScore": 1 });
  db.getCollection(collectionName).createIndex({ "metadata.importanceScore": 1 });
  db.getCollection(collectionName).createIndex({ "metadata.createdAt": 1 });
});

// Créer un utilisateur spécifique pour l'application avec des privilèges limités
db.createUser({
  user: 'lexai_app',
  pwd: 'lexai_app_password_2024!',
  roles: [
    { role: 'readWrite', db: 'lexai_preprocessing' }
  ]
});

// Insérer un document de test pour vérifier la configuration
db.legal_documents.insertOne({
  _id: "test-document-id",
  chunkId: "test-chunk-id",
  documentId: "test-doc-id",
  content: "Document de test pour vérifier la configuration MongoDB",
  vector: [0.1, 0.2, 0.3, 0.4, 0.5],
  metadata: {
    sequenceNumber: 1,
    qualityScore: 0.8,
    importanceScore: 0.7,
    tokenCount: 10,
    characterCount: 50,
    chunkType: "Paragraph",
    keywords: ["test", "configuration"],
    domain: "Other",
    createdAt: new Date()
  }
});

print("Initialisation MongoDB terminée pour le service Data Preprocessing");
print("Collections créées :", db.getCollectionNames().length);
print("Utilisateurs créés : lexai_admin, lexai_app");
print("Document de test inséré dans legal_documents");
