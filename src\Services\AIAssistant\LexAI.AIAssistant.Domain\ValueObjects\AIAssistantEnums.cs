namespace LexAI.AIAssistant.Domain.ValueObjects;

/// <summary>
/// Message role enumeration
/// </summary>
public enum MessageRole
{
    /// <summary>
    /// Message from user
    /// </summary>
    User,

    /// <summary>
    /// Message from AI assistant
    /// </summary>
    Assistant,

    /// <summary>
    /// System message
    /// </summary>
    System
}

/// <summary>
/// Message type enumeration
/// </summary>
public enum MessageType
{
    /// <summary>
    /// User question
    /// </summary>
    Question,

    /// <summary>
    /// AI assistant answer
    /// </summary>
    Answer,

    /// <summary>
    /// Follow-up question
    /// </summary>
    FollowUp,

    /// <summary>
    /// Clarification request
    /// </summary>
    Clarification,

    /// <summary>
    /// System message
    /// </summary>
    System,

    /// <summary>
    /// Error message
    /// </summary>
    Error,

    /// <summary>
    /// Information message
    /// </summary>
    Information,

    /// <summary>
    /// Warning message
    /// </summary>
    Warning
}

/// <summary>
/// Message status enumeration
/// </summary>
public enum MessageStatus
{
    /// <summary>
    /// Message is being processed
    /// </summary>
    Processing,

    /// <summary>
    /// Message has been sent
    /// </summary>
    Sent,

    /// <summary>
    /// Message processing failed
    /// </summary>
    Failed,

    /// <summary>
    /// Message was cancelled
    /// </summary>
    Cancelled
}

/// <summary>
/// Conversation status enumeration
/// </summary>
public enum ConversationStatus
{
    /// <summary>
    /// Conversation is active
    /// </summary>
    Active,

    /// <summary>
    /// Conversation is closed
    /// </summary>
    Closed,

    /// <summary>
    /// Conversation is archived
    /// </summary>
    Archived,

    /// <summary>
    /// Conversation is deleted
    /// </summary>
    Deleted
}

/// <summary>
/// Legal domain enumeration (reused from LegalResearch)
/// </summary>
public enum LegalDomain
{
    /// <summary>
    /// Civil law
    /// </summary>
    Civil,

    /// <summary>
    /// Criminal law
    /// </summary>
    Criminal,

    /// <summary>
    /// Administrative law
    /// </summary>
    Administrative,

    /// <summary>
    /// Constitutional law
    /// </summary>
    Constitutional,

    /// <summary>
    /// Commercial and business law
    /// </summary>
    Commercial,

    /// <summary>
    /// Labor and employment law
    /// </summary>
    Labor,

    /// <summary>
    /// Tax law
    /// </summary>
    Tax,

    /// <summary>
    /// Real estate law
    /// </summary>
    RealEstate,

    /// <summary>
    /// Family law
    /// </summary>
    Family,

    /// <summary>
    /// Intellectual property law
    /// </summary>
    IntellectualProperty,

    /// <summary>
    /// Environmental law
    /// </summary>
    Environmental,

    /// <summary>
    /// Health law
    /// </summary>
    Health,

    /// <summary>
    /// Immigration law
    /// </summary>
    Immigration,

    /// <summary>
    /// International law
    /// </summary>
    International,

    /// <summary>
    /// European Union law
    /// </summary>
    European,

    /// <summary>
    /// Banking and finance law
    /// </summary>
    Banking,

    /// <summary>
    /// Insurance law
    /// </summary>
    Insurance,

    /// <summary>
    /// Technology and data law
    /// </summary>
    Technology,

    /// <summary>
    /// Competition law
    /// </summary>
    Competition,

    /// <summary>
    /// Consumer protection law
    /// </summary>
    Consumer,

    /// <summary>
    /// Other legal domain
    /// </summary>
    Other
}

/// <summary>
/// Message intent enumeration
/// </summary>
public enum MessageIntent
{
    /// <summary>
    /// Unknown intent
    /// </summary>
    Unknown,

    /// <summary>
    /// Seeking legal information
    /// </summary>
    Information,

    /// <summary>
    /// Asking for legal advice
    /// </summary>
    Advice,

    /// <summary>
    /// Document analysis request
    /// </summary>
    DocumentAnalysis,

    /// <summary>
    /// Contract review request
    /// </summary>
    ContractReview,

    /// <summary>
    /// Legal research request
    /// </summary>
    Research,

    /// <summary>
    /// Procedure explanation request
    /// </summary>
    Procedure,

    /// <summary>
    /// Case law search
    /// </summary>
    CaseLaw,

    /// <summary>
    /// Legal definition request
    /// </summary>
    Definition,

    /// <summary>
    /// Compliance question
    /// </summary>
    Compliance,

    /// <summary>
    /// Risk assessment request
    /// </summary>
    RiskAssessment,

    /// <summary>
    /// General conversation
    /// </summary>
    General
}

/// <summary>
/// AI model type enumeration
/// </summary>
public enum AIModelType
{
    /// <summary>
    /// GPT-4 model
    /// </summary>
    GPT4,

    /// <summary>
    /// GPT-4 Turbo model
    /// </summary>
    GPT4Turbo,

    /// <summary>
    /// GPT-3.5 Turbo model
    /// </summary>
    GPT35Turbo,

    /// <summary>
    /// Claude model
    /// </summary>
    Claude,

    /// <summary>
    /// Custom legal model
    /// </summary>
    CustomLegal
}

/// <summary>
/// Response quality enumeration
/// </summary>
public enum ResponseQuality
{
    /// <summary>
    /// Low quality response
    /// </summary>
    Low,

    /// <summary>
    /// Medium quality response
    /// </summary>
    Medium,

    /// <summary>
    /// High quality response
    /// </summary>
    High,

    /// <summary>
    /// Excellent quality response
    /// </summary>
    Excellent
}

/// <summary>
/// Citation type enumeration
/// </summary>
public enum CitationType
{
    /// <summary>
    /// Legal document citation
    /// </summary>
    LegalDocument,

    /// <summary>
    /// Case law citation
    /// </summary>
    CaseLaw,

    /// <summary>
    /// Statute citation
    /// </summary>
    Statute,

    /// <summary>
    /// Regulation citation
    /// </summary>
    Regulation,

    /// <summary>
    /// Academic source citation
    /// </summary>
    Academic,

    /// <summary>
    /// News article citation
    /// </summary>
    News,

    /// <summary>
    /// Website citation
    /// </summary>
    Website,

    /// <summary>
    /// Other citation type
    /// </summary>
    Other
}

/// <summary>
/// Attachment type enumeration
/// </summary>
public enum AttachmentType
{
    /// <summary>
    /// PDF document
    /// </summary>
    PDF,

    /// <summary>
    /// Word document
    /// </summary>
    Word,

    /// <summary>
    /// Text file
    /// </summary>
    Text,

    /// <summary>
    /// Image file
    /// </summary>
    Image,

    /// <summary>
    /// Spreadsheet
    /// </summary>
    Spreadsheet,

    /// <summary>
    /// Other file type
    /// </summary>
    Other
}

/// <summary>
/// Conversation mode enumeration
/// </summary>
public enum ConversationMode
{
    /// <summary>
    /// Standard conversation mode
    /// </summary>
    Standard,

    /// <summary>
    /// Research mode with enhanced search
    /// </summary>
    Research,

    /// <summary>
    /// Document analysis mode
    /// </summary>
    DocumentAnalysis,

    /// <summary>
    /// Legal advice mode
    /// </summary>
    LegalAdvice,

    /// <summary>
    /// Quick question mode
    /// </summary>
    QuickQuestion
}
