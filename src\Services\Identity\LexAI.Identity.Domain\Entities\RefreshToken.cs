using LexAI.Shared.Domain.Common;

namespace LexAI.Identity.Domain.Entities;

/// <summary>
/// Represents a refresh token for JWT authentication
/// </summary>
public class RefreshToken : BaseEntity
{
    /// <summary>
    /// The refresh token value
    /// </summary>
    public string Token { get; private set; } = string.Empty;

    /// <summary>
    /// The user ID this token belongs to
    /// </summary>
    public Guid UserId { get; private set; }

    /// <summary>
    /// The user this token belongs to
    /// </summary>
    public virtual User User { get; private set; } = null!;

    /// <summary>
    /// When the token expires
    /// </summary>
    public DateTime ExpiresAt { get; private set; }

    /// <summary>
    /// Indicates if the token has been revoked
    /// </summary>
    public bool IsRevoked { get; private set; } = false;

    /// <summary>
    /// When the token was revoked
    /// </summary>
    public DateTime? RevokedAt { get; private set; }

    /// <summary>
    /// Who revoked the token
    /// </summary>
    public string? RevokedBy { get; private set; }

    /// <summary>
    /// Reason for revocation
    /// </summary>
    public string? RevocationReason { get; private set; }

    /// <summary>
    /// IP address where the token was created
    /// </summary>
    public string? IpAddress { get; private set; }

    /// <summary>
    /// User agent of the client that created the token
    /// </summary>
    public string? UserAgent { get; private set; }

    /// <summary>
    /// Indicates if the token is active (not expired and not revoked)
    /// </summary>
    public bool IsActive => !IsRevoked && DateTime.UtcNow < ExpiresAt;

    /// <summary>
    /// Private constructor for EF Core
    /// </summary>
    private RefreshToken() { }

    /// <summary>
    /// Creates a new refresh token
    /// </summary>
    /// <param name="userId">User ID the token belongs to</param>
    /// <param name="expiresAt">When the token expires</param>
    /// <param name="ipAddress">IP address where the token was created</param>
    /// <param name="userAgent">User agent of the client</param>
    /// <returns>New refresh token instance</returns>
    public static RefreshToken Create(
        Guid userId,
        DateTime expiresAt,
        string? ipAddress = null,
        string? userAgent = null)
    {
        return new RefreshToken
        {
            Token = GenerateToken(),
            UserId = userId,
            ExpiresAt = expiresAt,
            IpAddress = ipAddress,
            UserAgent = userAgent
        };
    }

    /// <summary>
    /// Revokes the refresh token
    /// </summary>
    /// <param name="revokedBy">Who is revoking the token</param>
    /// <param name="reason">Reason for revocation</param>
    public void Revoke(string revokedBy, string? reason = null)
    {
        if (IsRevoked)
            return;

        IsRevoked = true;
        RevokedAt = DateTime.UtcNow;
        RevokedBy = revokedBy;
        RevocationReason = reason;
    }

    /// <summary>
    /// Generates a cryptographically secure random token
    /// </summary>
    /// <returns>Random token string</returns>
    private static string GenerateToken()
    {
        var randomBytes = new byte[64];
        using var rng = System.Security.Cryptography.RandomNumberGenerator.Create();
        rng.GetBytes(randomBytes);
        return Convert.ToBase64String(randomBytes);
    }
}

/// <summary>
/// Represents a user permission in the system
/// </summary>
public class UserPermission : BaseEntity
{
    /// <summary>
    /// The user ID this permission belongs to
    /// </summary>
    public Guid UserId { get; private set; }

    /// <summary>
    /// The user this permission belongs to
    /// </summary>
    public virtual User User { get; private set; } = null!;

    /// <summary>
    /// The permission name/code
    /// </summary>
    public string Permission { get; private set; } = string.Empty;

    /// <summary>
    /// The resource this permission applies to (optional)
    /// </summary>
    public string? Resource { get; private set; }

    /// <summary>
    /// The action this permission allows
    /// </summary>
    public string Action { get; private set; } = string.Empty;

    /// <summary>
    /// When this permission was granted
    /// </summary>
    public DateTime GrantedAt { get; private set; }

    /// <summary>
    /// Who granted this permission
    /// </summary>
    public string GrantedBy { get; private set; } = string.Empty;

    /// <summary>
    /// When this permission expires (optional)
    /// </summary>
    public DateTime? ExpiresAt { get; private set; }

    /// <summary>
    /// Indicates if the permission is active
    /// </summary>
    public bool IsActive => ExpiresAt == null || DateTime.UtcNow < ExpiresAt;

    /// <summary>
    /// Private constructor for EF Core
    /// </summary>
    private UserPermission() { }

    /// <summary>
    /// Creates a new user permission
    /// </summary>
    /// <param name="userId">User ID the permission belongs to</param>
    /// <param name="permission">Permission name/code</param>
    /// <param name="action">Action allowed</param>
    /// <param name="grantedBy">Who granted the permission</param>
    /// <param name="resource">Resource the permission applies to</param>
    /// <param name="expiresAt">When the permission expires</param>
    /// <returns>New user permission instance</returns>
    public static UserPermission Create(
        Guid userId,
        string permission,
        string action,
        string grantedBy,
        string? resource = null,
        DateTime? expiresAt = null)
    {
        return new UserPermission
        {
            UserId = userId,
            Permission = permission ?? throw new ArgumentNullException(nameof(permission)),
            Action = action ?? throw new ArgumentNullException(nameof(action)),
            Resource = resource,
            GrantedBy = grantedBy ?? throw new ArgumentNullException(nameof(grantedBy)),
            GrantedAt = DateTime.UtcNow,
            ExpiresAt = expiresAt
        };
    }
}
