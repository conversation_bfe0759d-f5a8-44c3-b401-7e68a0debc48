{"version": "0.2.0", "configurations": [{"name": "Launch Identity API", "type": "coreclr", "request": "launch", "preLaunchTask": "build-identity-api", "program": "${workspaceFolder}/src/Services/Identity/LexAI.Identity.API/bin/Debug/net9.0/LexAI.Identity.API.dll", "args": [], "cwd": "${workspaceFolder}/src/Services/Identity/LexAI.Identity.API", "console": "internalConsole", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5001"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Launch DataPreprocessing API", "type": "coreclr", "request": "launch", "preLaunchTask": "build-datapreprocessing-api", "program": "${workspaceFolder}/src/Services/DataPreprocessing/LexAI.DataPreprocessing.API/bin/Debug/net9.0/LexAI.DataPreprocessing.API.dll", "args": [], "cwd": "${workspaceFolder}/src/Services/DataPreprocessing/LexAI.DataPreprocessing.API", "console": "internalConsole", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5002"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Launch API Gateway", "type": "coreclr", "request": "launch", "preLaunchTask": "build-apigateway", "program": "${workspaceFolder}/src/ApiGateway/bin/Debug/net9.0/LexAI.ApiGateway.dll", "args": [], "cwd": "${workspaceFolder}/src/ApiGateway", "console": "internalConsole", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5000"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Launch All Services", "type": "coreclr", "request": "launch", "preLaunchTask": "build-all", "program": "${workspaceFolder}/src/ApiGateway/bin/Debug/net9.0/LexAI.ApiGateway.dll", "args": [], "cwd": "${workspaceFolder}/src/ApiGateway", "console": "internalConsole", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5000"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}, "compounds": ["Launch Identity API", "Launch DataPreprocessing API"]}], "compounds": [{"name": "Launch All Services", "configurations": ["Launch Identity API", "Launch DataPreprocessing API", "Launch API Gateway"]}]}