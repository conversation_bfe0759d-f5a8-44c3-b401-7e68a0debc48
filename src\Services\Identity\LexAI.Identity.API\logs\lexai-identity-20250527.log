2025-05-27 23:51:40.988 +04:00 [FTL] LexAI Identity Service failed to start
System.IO.FileNotFoundException: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.ConfigurationManager.AddSource(IConfigurationSource source)
   at Microsoft.Extensions.Configuration.ConfigurationManager.Microsoft.Extensions.Configuration.IConfigurationBuilder.Add(IConfigurationSource source)
   at Microsoft.Extensions.Configuration.JsonConfigurationExtensions.AddJsonFile(IConfigurationBuilder builder, String path, Boolean optional, Boolean reloadOnChange)
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Program.cs:line 31
2025-05-27 23:53:21.832 +04:00 [FTL] LexAI Identity Service failed to start
System.IO.FileNotFoundException: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.ConfigurationManager.AddSource(IConfigurationSource source)
   at Microsoft.Extensions.Configuration.ConfigurationManager.Microsoft.Extensions.Configuration.IConfigurationBuilder.Add(IConfigurationSource source)
   at Microsoft.Extensions.Configuration.JsonConfigurationExtensions.AddJsonFile(IConfigurationBuilder builder, String path, Boolean optional, Boolean reloadOnChange)
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Program.cs:line 31
2025-05-27 23:54:19.616 +04:00 [FTL] LexAI Identity Service failed to start
System.IO.FileNotFoundException: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.ConfigurationManager.AddSource(IConfigurationSource source)
   at Microsoft.Extensions.Configuration.ConfigurationManager.Microsoft.Extensions.Configuration.IConfigurationBuilder.Add(IConfigurationSource source)
   at Microsoft.Extensions.Configuration.JsonConfigurationExtensions.AddJsonFile(IConfigurationBuilder builder, String path, Boolean optional, Boolean reloadOnChange)
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Program.cs:line 31
