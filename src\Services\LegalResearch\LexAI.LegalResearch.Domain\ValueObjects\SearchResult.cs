using LexAI.Shared.Domain.Common;

namespace LexAI.LegalResearch.Domain.ValueObjects;

/// <summary>
/// Represents a search result for a legal document
/// </summary>
public class SearchResult : ValueObject
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; private set; }

    /// <summary>
    /// Document title
    /// </summary>
    public string Title { get; private set; }

    /// <summary>
    /// Document summary or excerpt
    /// </summary>
    public string Summary { get; private set; }

    /// <summary>
    /// Relevance score (0-1)
    /// </summary>
    public double RelevanceScore { get; private set; }

    /// <summary>
    /// Semantic similarity score (0-1)
    /// </summary>
    public double SimilarityScore { get; private set; }

    /// <summary>
    /// Keyword match score (0-1)
    /// </summary>
    public double KeywordScore { get; private set; }

    /// <summary>
    /// Document type
    /// </summary>
    public DocumentType DocumentType { get; private set; }

    /// <summary>
    /// Legal domain
    /// </summary>
    public LegalDomain LegalDomain { get; private set; }

    /// <summary>
    /// Document source
    /// </summary>
    public DocumentSource Source { get; private set; }

    /// <summary>
    /// Highlighted text snippets
    /// </summary>
    public List<TextHighlight> Highlights { get; private set; }

    /// <summary>
    /// Matched chunks from the document
    /// </summary>
    public List<MatchedChunk> MatchedChunks { get; private set; }

    /// <summary>
    /// Document publication date
    /// </summary>
    public DateTime? PublicationDate { get; private set; }

    /// <summary>
    /// Document effective date
    /// </summary>
    public DateTime? EffectiveDate { get; private set; }

    /// <summary>
    /// Document tags
    /// </summary>
    public List<string> Tags { get; private set; }

    /// <summary>
    /// Document URL or file path
    /// </summary>
    public string? DocumentUrl { get; private set; }

    /// <summary>
    /// Search ranking position
    /// </summary>
    public int Rank { get; private set; }

    /// <summary>
    /// Explanation of why this document was matched
    /// </summary>
    public string? MatchExplanation { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private SearchResult() 
    {
        Title = string.Empty;
        Summary = string.Empty;
        Source = null!;
        Highlights = new List<TextHighlight>();
        MatchedChunks = new List<MatchedChunk>();
        Tags = new List<string>();
    }

    /// <summary>
    /// Creates a new search result
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="title">Document title</param>
    /// <param name="summary">Document summary</param>
    /// <param name="relevanceScore">Relevance score</param>
    /// <param name="documentType">Document type</param>
    /// <param name="legalDomain">Legal domain</param>
    /// <param name="source">Document source</param>
    /// <returns>New search result</returns>
    public static SearchResult Create(
        Guid documentId,
        string title,
        string summary,
        double relevanceScore,
        DocumentType documentType,
        LegalDomain legalDomain,
        DocumentSource source)
    {
        if (documentId == Guid.Empty)
            throw new ArgumentException("Document ID cannot be empty", nameof(documentId));

        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));

        if (string.IsNullOrWhiteSpace(summary))
            throw new ArgumentException("Summary cannot be empty", nameof(summary));

        if (relevanceScore < 0.0 || relevanceScore > 1.0)
            throw new ArgumentException("Relevance score must be between 0 and 1", nameof(relevanceScore));

        if (source == null)
            throw new ArgumentNullException(nameof(source));

        return new SearchResult
        {
            DocumentId = documentId,
            Title = title.Trim(),
            Summary = summary.Trim(),
            RelevanceScore = relevanceScore,
            DocumentType = documentType,
            LegalDomain = legalDomain,
            Source = source,
            Highlights = new List<TextHighlight>(),
            MatchedChunks = new List<MatchedChunk>(),
            Tags = new List<string>()
        };
    }

    /// <summary>
    /// Sets the similarity score
    /// </summary>
    /// <param name="similarityScore">Similarity score</param>
    public SearchResult WithSimilarityScore(double similarityScore)
    {
        if (similarityScore < 0.0 || similarityScore > 1.0)
            throw new ArgumentException("Similarity score must be between 0 and 1", nameof(similarityScore));

        var result = Clone();
        result.SimilarityScore = similarityScore;
        return result;
    }

    /// <summary>
    /// Sets the keyword score
    /// </summary>
    /// <param name="keywordScore">Keyword score</param>
    public SearchResult WithKeywordScore(double keywordScore)
    {
        if (keywordScore < 0.0 || keywordScore > 1.0)
            throw new ArgumentException("Keyword score must be between 0 and 1", nameof(keywordScore));

        var result = Clone();
        result.KeywordScore = keywordScore;
        return result;
    }

    /// <summary>
    /// Adds text highlights
    /// </summary>
    /// <param name="highlights">Text highlights</param>
    public SearchResult WithHighlights(IEnumerable<TextHighlight> highlights)
    {
        if (highlights == null)
            throw new ArgumentNullException(nameof(highlights));

        var result = Clone();
        result.Highlights.AddRange(highlights);
        return result;
    }

    /// <summary>
    /// Adds matched chunks
    /// </summary>
    /// <param name="chunks">Matched chunks</param>
    public SearchResult WithMatchedChunks(IEnumerable<MatchedChunk> chunks)
    {
        if (chunks == null)
            throw new ArgumentNullException(nameof(chunks));

        var result = Clone();
        result.MatchedChunks.AddRange(chunks);
        return result;
    }

    /// <summary>
    /// Sets document dates
    /// </summary>
    /// <param name="publicationDate">Publication date</param>
    /// <param name="effectiveDate">Effective date</param>
    public SearchResult WithDates(DateTime? publicationDate, DateTime? effectiveDate)
    {
        var result = Clone();
        result.PublicationDate = publicationDate;
        result.EffectiveDate = effectiveDate;
        return result;
    }

    /// <summary>
    /// Sets document tags
    /// </summary>
    /// <param name="tags">Document tags</param>
    public SearchResult WithTags(IEnumerable<string> tags)
    {
        if (tags == null)
            throw new ArgumentNullException(nameof(tags));

        var result = Clone();
        result.Tags.AddRange(tags.Where(t => !string.IsNullOrWhiteSpace(t)));
        return result;
    }

    /// <summary>
    /// Sets document URL
    /// </summary>
    /// <param name="url">Document URL</param>
    public SearchResult WithUrl(string url)
    {
        var result = Clone();
        result.DocumentUrl = url;
        return result;
    }

    /// <summary>
    /// Sets search rank
    /// </summary>
    /// <param name="rank">Search rank</param>
    public SearchResult WithRank(int rank)
    {
        if (rank < 1)
            throw new ArgumentException("Rank must be greater than 0", nameof(rank));

        var result = Clone();
        result.Rank = rank;
        return result;
    }

    /// <summary>
    /// Sets match explanation
    /// </summary>
    /// <param name="explanation">Match explanation</param>
    public SearchResult WithExplanation(string explanation)
    {
        var result = Clone();
        result.MatchExplanation = explanation;
        return result;
    }

    /// <summary>
    /// Gets the overall match score combining all scoring factors
    /// </summary>
    /// <returns>Overall match score</returns>
    public double GetOverallScore()
    {
        // Weighted combination of different scores
        var weights = new
        {
            Relevance = 0.4,
            Similarity = 0.3,
            Keyword = 0.2,
            Authority = 0.1
        };

        var authorityScore = Source.ReliabilityScore;

        return (RelevanceScore * weights.Relevance) +
               (SimilarityScore * weights.Similarity) +
               (KeywordScore * weights.Keyword) +
               (authorityScore * weights.Authority);
    }

    /// <summary>
    /// Gets the best text snippet for display
    /// </summary>
    /// <param name="maxLength">Maximum snippet length</param>
    /// <returns>Best text snippet</returns>
    public string GetBestSnippet(int maxLength = 300)
    {
        // Prefer highlighted text if available
        var bestHighlight = Highlights
            .OrderByDescending(h => h.Score)
            .FirstOrDefault();

        if (bestHighlight != null)
        {
            var snippet = bestHighlight.Text;
            if (snippet.Length <= maxLength)
                return snippet;
            
            return snippet.Substring(0, maxLength - 3) + "...";
        }

        // Fall back to summary
        if (Summary.Length <= maxLength)
            return Summary;

        return Summary.Substring(0, maxLength - 3) + "...";
    }

    /// <summary>
    /// Checks if the result is highly relevant
    /// </summary>
    /// <returns>True if relevance score >= 0.8</returns>
    public bool IsHighlyRelevant()
    {
        return RelevanceScore >= 0.8;
    }

    /// <summary>
    /// Checks if the result is from an authoritative source
    /// </summary>
    /// <returns>True if source is highly reliable</returns>
    public bool IsAuthoritative()
    {
        return Source.IsHighlyReliable();
    }

    private SearchResult Clone()
    {
        return new SearchResult
        {
            DocumentId = DocumentId,
            Title = Title,
            Summary = Summary,
            RelevanceScore = RelevanceScore,
            SimilarityScore = SimilarityScore,
            KeywordScore = KeywordScore,
            DocumentType = DocumentType,
            LegalDomain = LegalDomain,
            Source = Source,
            Highlights = new List<TextHighlight>(Highlights),
            MatchedChunks = new List<MatchedChunk>(MatchedChunks),
            PublicationDate = PublicationDate,
            EffectiveDate = EffectiveDate,
            Tags = new List<string>(Tags),
            DocumentUrl = DocumentUrl,
            Rank = Rank,
            MatchExplanation = MatchExplanation
        };
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return DocumentId;
        yield return RelevanceScore;
        yield return SimilarityScore;
        yield return KeywordScore;
    }
}
