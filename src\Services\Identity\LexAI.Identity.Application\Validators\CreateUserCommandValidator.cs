using FluentValidation;
using LexAI.Identity.Application.Commands;
using LexAI.Identity.Application.Interfaces;
using LexAI.Shared.Domain.Enums;

namespace LexAI.Identity.Application.Validators;

/// <summary>
/// Validator for CreateUserCommand
/// </summary>
public class CreateUserCommandValidator : AbstractValidator<CreateUserCommand>
{
    private readonly IUserRepository _userRepository;

    /// <summary>
    /// Initializes a new instance of the CreateUserCommandValidator
    /// </summary>
    /// <param name="userRepository">User repository for email uniqueness validation</param>
    public CreateUserCommandValidator(IUserRepository userRepository)
    {
        _userRepository = userRepository;

        RuleFor(x => x.Email)
            .NotEmpty()
            .WithMessage("Email is required")
            .EmailAddress()
            .WithMessage("Email must be a valid email address")
            .MaximumLength(254)
            .WithMessage("Email must not exceed 254 characters")
            .MustAsync(BeUniqueEmail)
            .WithMessage("Email is already in use");

        RuleFor(x => x.FirstName)
            .NotEmpty()
            .WithMessage("First name is required")
            .MaximumLength(50)
            .WithMessage("First name must not exceed 50 characters")
            .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$")
            .WithMessage("First name can only contain letters, spaces, hyphens, and apostrophes");

        RuleFor(x => x.LastName)
            .NotEmpty()
            .WithMessage("Last name is required")
            .MaximumLength(50)
            .WithMessage("Last name must not exceed 50 characters")
            .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$")
            .WithMessage("Last name can only contain letters, spaces, hyphens, and apostrophes");

        RuleFor(x => x.Password)
            .NotEmpty()
            .WithMessage("Password is required")
            .MinimumLength(8)
            .WithMessage("Password must be at least 8 characters long")
            .MaximumLength(128)
            .WithMessage("Password must not exceed 128 characters")
            .Matches(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]")
            .WithMessage("Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special character");

        RuleFor(x => x.PhoneNumber)
            .Matches(@"^\+?[1-9]\d{1,14}$")
            .WithMessage("Phone number must be in international format")
            .When(x => !string.IsNullOrWhiteSpace(x.PhoneNumber));

        RuleFor(x => x.Role)
            .IsInEnum()
            .WithMessage("Role must be a valid user role")
            .NotEqual(UserRole.Guest)
            .WithMessage("Cannot create user with Guest role");

        RuleFor(x => x.PreferredLanguage)
            .NotEmpty()
            .WithMessage("Preferred language is required")
            .Must(BeValidLanguageCode)
            .WithMessage("Preferred language must be a valid language code (e.g., 'fr-FR', 'en-US')");

        RuleFor(x => x.TimeZone)
            .NotEmpty()
            .WithMessage("Time zone is required")
            .Must(BeValidTimeZone)
            .WithMessage("Time zone must be a valid IANA time zone identifier");

        RuleFor(x => x.CreatedBy)
            .NotEmpty()
            .WithMessage("CreatedBy is required");
    }

    /// <summary>
    /// Validates that the email is unique
    /// </summary>
    /// <param name="email">Email to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if email is unique</returns>
    private async Task<bool> BeUniqueEmail(string email, CancellationToken cancellationToken)
    {
        return !await _userRepository.ExistsAsync(email, null, cancellationToken);
    }

    /// <summary>
    /// Validates that the language code is valid
    /// </summary>
    /// <param name="languageCode">Language code to validate</param>
    /// <returns>True if valid</returns>
    private static bool BeValidLanguageCode(string languageCode)
    {
        try
        {
            var culture = new System.Globalization.CultureInfo(languageCode);
            return !culture.IsNeutralCulture;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Validates that the time zone is valid
    /// </summary>
    /// <param name="timeZone">Time zone to validate</param>
    /// <returns>True if valid</returns>
    private static bool BeValidTimeZone(string timeZone)
    {
        try
        {
            TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            return true;
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Validator for UpdateUserCommand
/// </summary>
public class UpdateUserCommandValidator : AbstractValidator<UpdateUserCommand>
{
    /// <summary>
    /// Initializes a new instance of the UpdateUserCommandValidator
    /// </summary>
    public UpdateUserCommandValidator()
    {
        RuleFor(x => x.UserId)
            .NotEmpty()
            .WithMessage("User ID is required");

        RuleFor(x => x.FirstName)
            .NotEmpty()
            .WithMessage("First name is required")
            .MaximumLength(50)
            .WithMessage("First name must not exceed 50 characters")
            .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$")
            .WithMessage("First name can only contain letters, spaces, hyphens, and apostrophes");

        RuleFor(x => x.LastName)
            .NotEmpty()
            .WithMessage("Last name is required")
            .MaximumLength(50)
            .WithMessage("Last name must not exceed 50 characters")
            .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$")
            .WithMessage("Last name can only contain letters, spaces, hyphens, and apostrophes");

        RuleFor(x => x.PhoneNumber)
            .Matches(@"^\+?[1-9]\d{1,14}$")
            .WithMessage("Phone number must be in international format")
            .When(x => !string.IsNullOrWhiteSpace(x.PhoneNumber));

        RuleFor(x => x.PreferredLanguage)
            .NotEmpty()
            .WithMessage("Preferred language is required")
            .Must(BeValidLanguageCode)
            .WithMessage("Preferred language must be a valid language code (e.g., 'fr-FR', 'en-US')");

        RuleFor(x => x.TimeZone)
            .NotEmpty()
            .WithMessage("Time zone is required")
            .Must(BeValidTimeZone)
            .WithMessage("Time zone must be a valid IANA time zone identifier");

        RuleFor(x => x.UpdatedBy)
            .NotEmpty()
            .WithMessage("UpdatedBy is required");
    }

    /// <summary>
    /// Validates that the language code is valid
    /// </summary>
    /// <param name="languageCode">Language code to validate</param>
    /// <returns>True if valid</returns>
    private static bool BeValidLanguageCode(string languageCode)
    {
        try
        {
            var culture = new System.Globalization.CultureInfo(languageCode);
            return !culture.IsNeutralCulture;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Validates that the time zone is valid
    /// </summary>
    /// <param name="timeZone">Time zone to validate</param>
    /// <returns>True if valid</returns>
    private static bool BeValidTimeZone(string timeZone)
    {
        try
        {
            TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            return true;
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Validator for LoginCommand
/// </summary>
public class LoginCommandValidator : AbstractValidator<LoginCommand>
{
    /// <summary>
    /// Initializes a new instance of the LoginCommandValidator
    /// </summary>
    public LoginCommandValidator()
    {
        RuleFor(x => x.Email)
            .NotEmpty()
            .WithMessage("Email is required")
            .EmailAddress()
            .WithMessage("Email must be a valid email address");

        RuleFor(x => x.Password)
            .NotEmpty()
            .WithMessage("Password is required")
            .MinimumLength(1)
            .WithMessage("Password cannot be empty");
    }
}

/// <summary>
/// Validator for RefreshTokenCommand
/// </summary>
public class RefreshTokenCommandValidator : AbstractValidator<RefreshTokenCommand>
{
    /// <summary>
    /// Initializes a new instance of the RefreshTokenCommandValidator
    /// </summary>
    public RefreshTokenCommandValidator()
    {
        RuleFor(x => x.RefreshToken)
            .NotEmpty()
            .WithMessage("Refresh token is required");
    }
}
