﻿# Script pour corriger manuellement les bases de données
param(
    [switch]$PostgreSQL = $false,
    [switch]$MongoDB = $false,
    [switch]$All = $false
)

if ($All) {
    $PostgreSQL = $true
    $MongoDB = $true
}

if (-not $PostgreSQL -and -not $MongoDB) {
    Write-Host "Usage: .\fix-databases.ps1 [-PostgreSQL] [-MongoDB] [-All]" -ForegroundColor Yellow
    exit 1
}

Write-Host "🔧 Correction manuelle des bases de données" -ForegroundColor Green

# Naviguer vers le répertoire du service
$serviceDir = "src/Services/DataPreprocessing"
if (!(Test-Path $serviceDir)) {
    Write-Error "Répertoire du service non trouvé : $serviceDir"
    exit 1
}

Set-Location $serviceDir

try {
    # Vérifier que les conteneurs sont en cours d'exécution
    $runningContainers = docker-compose ps --services --filter "status=running"
    
    if (-not $runningContainers) {
        Write-Host "🚀 Démarrage des conteneurs..." -ForegroundColor Yellow
        docker-compose up -d
        Start-Sleep -Seconds 10
    }

    if ($PostgreSQL) {
        Write-Host ""
        Write-Host "🐘 Correction PostgreSQL..." -ForegroundColor Cyan
        
        # Attendre que PostgreSQL soit prêt
        Write-Host "⏳ Attente de PostgreSQL..." -ForegroundColor Yellow
        $maxWait = 30
        $waited = 0
        
        while ($waited -lt $maxWait) {
            $pgReady = docker-compose exec -T postgres pg_isready -U postgres 2>$null
            if ($pgReady -match "accepting connections") {
                Write-Host "✅ PostgreSQL prêt" -ForegroundColor Green
                break
            }
            Start-Sleep -Seconds 2
            $waited += 2
        }
        
        if ($waited -ge $maxWait) {
            Write-Error "❌ PostgreSQL n'est pas prêt après $maxWait secondes"
            exit 1
        }
        
        # Exécuter le script d'initialisation
        Write-Host "📝 Exécution du script d'initialisation PostgreSQL..." -ForegroundColor Yellow
        $initScript = Get-Content "scripts/init-postgres.sql" -Raw
        
        $result = echo $initScript | docker-compose exec -T postgres psql -U postgres -d postgres 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Script PostgreSQL exécuté avec succès" -ForegroundColor Green
            
            # Vérifier les bases de données créées
            Write-Host "🔍 Vérification des bases de données..." -ForegroundColor Yellow
            $databases = docker-compose exec -T postgres psql -U postgres -d postgres -c "\l" | Select-String "data_preprocessing"
            
            if ($databases) {
                Write-Host "✅ Bases de données trouvées :" -ForegroundColor Green
                $databases | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
            } else {
                Write-Warning "⚠️ Aucune base de données data_preprocessing trouvée"
            }
            
            # Tester la connexion avec lexai_user
            Write-Host "🔍 Test de connexion lexai_user..." -ForegroundColor Yellow
            $testConnection = docker-compose exec -T postgres psql -U lexai_user -d data_preprocessing_db -c "SELECT current_user, current_database();" 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Connexion lexai_user réussie" -ForegroundColor Green
            } else {
                Write-Warning "⚠️ Problème de connexion lexai_user : $testConnection"
            }
        } else {
            Write-Error "❌ Erreur lors de l'exécution du script PostgreSQL : $result"
        }
    }

    if ($MongoDB) {
        Write-Host ""
        Write-Host "🍃 Correction MongoDB..." -ForegroundColor Cyan
        
        # Attendre que MongoDB soit prêt
        Write-Host "⏳ Attente de MongoDB..." -ForegroundColor Yellow
        $maxWait = 30
        $waited = 0
        
        while ($waited -lt $maxWait) {
            $mongoReady = docker-compose exec -T mongodb mongosh --eval "db.adminCommand('ping')" 2>$null
            if ($mongoReady -match "ok.*1") {
                Write-Host "✅ MongoDB prêt" -ForegroundColor Green
                break
            }
            Start-Sleep -Seconds 2
            $waited += 2
        }
        
        if ($waited -ge $maxWait) {
            Write-Error "❌ MongoDB n'est pas prêt après $maxWait secondes"
            exit 1
        }
        
        # Exécuter le script d'initialisation
        Write-Host "📝 Exécution du script d'initialisation MongoDB..." -ForegroundColor Yellow
        
        $result = docker-compose exec -T mongodb mongosh < scripts/init-mongo.js 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Script MongoDB exécuté avec succès" -ForegroundColor Green
            Write-Host $result -ForegroundColor Gray
            
            # Vérifier les bases de données créées
            Write-Host "🔍 Vérification des bases de données..." -ForegroundColor Yellow
            $databases = docker-compose exec -T mongodb mongosh --eval "show dbs" 2>&1
            
            if ($databases -match "lexai_preprocessing") {
                Write-Host "✅ Base lexai_preprocessing trouvée" -ForegroundColor Green
                
                # Vérifier les collections
                $collections = docker-compose exec -T mongodb mongosh lexai_preprocessing --eval "show collections" 2>&1
                Write-Host "📋 Collections :" -ForegroundColor Cyan
                $collections | ForEach-Object { 
                    if ($_ -match "legal_") {
                        Write-Host "  $_" -ForegroundColor White 
                    }
                }
            } else {
                Write-Warning "⚠️ Base lexai_preprocessing non trouvée"
                Write-Host "Bases disponibles :" -ForegroundColor Yellow
                Write-Host $databases -ForegroundColor Gray
            }
        } else {
            Write-Error "❌ Erreur lors de l'exécution du script MongoDB : $result"
        }
    }

    Write-Host ""
    Write-Host "🎉 Correction terminée !" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Prochaines étapes :" -ForegroundColor Cyan
    Write-Host "  1. Redémarrez le service Data Preprocessing" -ForegroundColor White
    Write-Host "  2. Vérifiez les logs : docker-compose logs datapreprocessing-api" -ForegroundColor White
    Write-Host "  3. Testez l'API : http://localhost:5001/health" -ForegroundColor White

}
catch {
    Write-Error "❌ Erreur lors de la correction : $_"
    exit 1
}
finally {
    # Retourner au répertoire racine
    Set-Location ../../..
}
