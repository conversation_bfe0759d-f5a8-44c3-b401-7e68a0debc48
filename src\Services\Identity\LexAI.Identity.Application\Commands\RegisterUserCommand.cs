using LexAI.Identity.Application.DTOs;
using LexAI.Identity.Application.Interfaces;
using LexAI.Shared.Domain.Enums;
using LexAI.Identity.Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using DomainException = LexAI.Shared.Domain.Exceptions.DomainException;
using InvalidDataException = LexAI.Shared.Domain.Exceptions.InvalidDataException;
using EntityAlreadyExistsException = LexAI.Shared.Domain.Exceptions.EntityAlreadyExistsException;

namespace LexAI.Identity.Application.Commands;

/// <summary>
/// Command to register a new user (public registration)
/// </summary>
public class RegisterUserCommand : IRequest<UserDto>
{
    /// <summary>
    /// User's email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// User's first name
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// User's last name
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// User's phone number (optional)
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// User's password
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// User's role in the system (excluding Administrator)
    /// </summary>
    public UserRole Role { get; set; }

    /// <summary>
    /// User's preferred language
    /// </summary>
    public string PreferredLanguage { get; set; } = "fr-FR";

    /// <summary>
    /// User's timezone
    /// </summary>
    public string TimeZone { get; set; } = "Europe/Paris";

    /// <summary>
    /// Terms and conditions acceptance
    /// </summary>
    public bool AcceptTerms { get; set; }

    /// <summary>
    /// Privacy policy acceptance
    /// </summary>
    public bool AcceptPrivacyPolicy { get; set; }

    /// <summary>
    /// IP address of the registering user
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// User agent of the registering user
    /// </summary>
    public string? UserAgent { get; set; }
}

/// <summary>
/// Handler for RegisterUserCommand
/// </summary>
public class RegisterUserCommandHandler : IRequestHandler<RegisterUserCommand, UserDto>
{
    private readonly IUserRepository _userRepository;
    private readonly IPasswordService _passwordService;
    private readonly ILogger<RegisterUserCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the RegisterUserCommandHandler
    /// </summary>
    /// <param name="userRepository">User repository</param>
    /// <param name="passwordService">Password service</param>
    /// <param name="logger">Logger</param>
    public RegisterUserCommandHandler(
        IUserRepository userRepository,
        IPasswordService passwordService,
        ILogger<RegisterUserCommandHandler> logger)
    {
        _userRepository = userRepository;
        _passwordService = passwordService;
        _logger = logger;
    }

    /// <summary>
    /// Handles the RegisterUserCommand
    /// </summary>
    /// <param name="request">Register user command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created user DTO</returns>
    public async Task<UserDto> Handle(RegisterUserCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Public registration attempt for email {Email}", request.Email);

        // Validate that the role is not Administrator
        if (request.Role == UserRole.Administrator)
        {
            _logger.LogWarning("Attempt to register with Administrator role for email {Email}", request.Email);
            throw new InvalidDataException("Administrator role cannot be selected during public registration", "Role");
        }

        // Validate terms and privacy policy acceptance
        if (!request.AcceptTerms)
        {
            throw new InvalidDataException("Terms and conditions must be accepted", "AcceptTerms");
        }

        if (!request.AcceptPrivacyPolicy)
        {
            throw new InvalidDataException("Privacy policy must be accepted", "AcceptPrivacyPolicy");
        }

        // Check if user already exists
        var existingUser = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);
        if (existingUser != null)
        {
            _logger.LogWarning("Attempt to register with existing email {Email}", request.Email);
            throw new EntityAlreadyExistsException("User", request.Email);
        }

        // Validate password strength
        var passwordValidation = _passwordService.ValidatePasswordStrength(request.Password);
        if (!passwordValidation.IsValid)
        {
            _logger.LogWarning("Password validation failed for user {Email}: {Errors}",
                request.Email, string.Join(", ", passwordValidation.Errors));
            throw new InvalidDataException($"Password validation failed: {string.Join(", ", passwordValidation.Errors)}", "Password");
        }

        try
        {
            // Create user entity
            var user = User.Create(
                request.Email,
                request.FirstName,
                request.LastName,
                request.Role,
                "system"); // Public registration is created by system

            // Set password
            user.SetPassword(request.Password, "system");

            // Set optional properties
            if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
            {
                user.UpdateProfile(
                    request.FirstName,
                    request.LastName,
                    request.PhoneNumber,
                    "system");
            }

            // Set language and timezone preferences
            user.UpdatePreferences(request.PreferredLanguage, request.TimeZone, "system");

            // Save user
            var createdUser = await _userRepository.AddAsync(user, cancellationToken);

            _logger.LogInformation("User {UserId} registered successfully with email {Email} and role {Role}",
                createdUser.Id, createdUser.Email, createdUser.Role);

            // Map to DTO
            return new UserDto
            {
                Id = createdUser.Id,
                Email = createdUser.Email,
                FirstName = createdUser.FirstName,
                LastName = createdUser.LastName,
                FullName = createdUser.FullName,
                PhoneNumber = createdUser.PhoneNumber?.Value,
                Role = createdUser.Role,
                IsEmailVerified = createdUser.IsEmailVerified,
                IsActive = createdUser.IsActive,
                IsLocked = createdUser.IsLocked,
                LastLoginAt = createdUser.LastLoginAt,
                PreferredLanguage = createdUser.PreferredLanguage,
                TimeZone = createdUser.TimeZone,
                ProfilePictureUrl = createdUser.ProfilePictureUrl,
                CreatedAt = createdUser.CreatedAt,
                UpdatedAt = createdUser.UpdatedAt
            };
        }
        catch (Exception ex) when (!(ex is DomainException))
        {
            _logger.LogError(ex, "Error registering user with email {Email}", request.Email);
            throw;
        }
    }
}
