<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.LegalResearch.Infrastructure</name>
    </assembly>
    <members>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService">
            <summary>
            Legal search service implementation
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.#ctor(LexAI.LegalResearch.Application.Interfaces.IEmbeddingService,LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService,LexAI.LegalResearch.Application.Interfaces.IQueryProcessingService,LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository,Microsoft.Extensions.Caching.Memory.IMemoryCache,Microsoft.Extensions.Logging.ILogger{LexAI.LegalResearch.Infrastructure.Services.LegalSearchService})">
            <summary>
            Initializes a new instance of the LegalSearchService
            </summary>
            <param name="embeddingService">Embedding service</param>
            <param name="vectorService">Vector database service</param>
            <param name="queryProcessor">Query processing service</param>
            <param name="documentRepository">Document repository</param>
            <param name="cache">Memory cache</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(LexAI.LegalResearch.Application.DTOs.SearchRequestDto,System.Threading.CancellationToken)">
            <summary>
            Performs a semantic search for legal documents
            </summary>
            <param name="request">Search request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search response with results</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(LexAI.LegalResearch.Application.DTOs.SearchRequestDto,System.Threading.CancellationToken)">
            <summary>
            Performs a hybrid search combining keyword and semantic search
            </summary>
            <param name="request">Search request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search response with results</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.FindSimilarDocumentsAsync(System.Guid,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Finds similar documents to a given document
            </summary>
            <param name="documentId">Document ID</param>
            <param name="limit">Maximum number of similar documents</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Similar documents</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.GetSearchSuggestionsAsync(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets search suggestions based on partial query
            </summary>
            <param name="partialQuery">Partial query text</param>
            <param name="limit">Maximum number of suggestions</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search suggestions</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.AnalyzeQueryAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Analyzes query intent and extracts entities
            </summary>
            <param name="query">Query text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Query analysis result</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.GetSearchAnalyticsAsync(System.Nullable{System.Guid},System.String,System.Threading.CancellationToken)">
            <summary>
            Gets search analytics for a user or session
            </summary>
            <param name="userId">User ID</param>
            <param name="sessionId">Session ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search analytics</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService">
            <summary>
            OpenAI embedding service implementation
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.#ctor(System.Net.Http.HttpClient,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Logging.ILogger{LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService})">
            <summary>
            Initializes a new instance of the OpenAIEmbeddingService
            </summary>
            <param name="httpClient">HTTP client</param>
            <param name="configuration">Configuration</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Generates embedding vector for text
            </summary>
            <param name="text">Text to embed</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Embedding vector</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingsAsync(System.Collections.Generic.IEnumerable{System.String},System.Threading.CancellationToken)">
            <summary>
            Generates embeddings for multiple texts
            </summary>
            <param name="texts">Texts to embed</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Embedding vectors</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.CalculateSimilarity(System.Single[],System.Single[])">
            <summary>
            Calculates cosine similarity between two embedding vectors
            </summary>
            <param name="vector1">First vector</param>
            <param name="vector2">Second vector</param>
            <returns>Cosine similarity score</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GetEmbeddingDimension">
            <summary>
            Gets the embedding model dimension
            </summary>
            <returns>Embedding dimension</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.OpenAIEmbeddingResponse">
            <summary>
            OpenAI embedding response model
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.OpenAIEmbeddingData">
            <summary>
            OpenAI embedding data model
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.OpenAIUsage">
            <summary>
            OpenAI usage model
            </summary>
        </member>
    </members>
</doc>
