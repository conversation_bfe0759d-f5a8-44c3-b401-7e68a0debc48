<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.AIAssistant.API</name>
    </assembly>
    <members>
        <member name="T:LexAI.AIAssistant.API.Controllers.ChatController">
            <summary>
            Controller for AI assistant chat operations
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.API.Controllers.ChatController.#ctor(MediatR.IMediator,LexAI.AIAssistant.Application.Interfaces.IAIAssistantService,LexAI.AIAssistant.Application.Interfaces.IConversationService,Microsoft.Extensions.Logging.ILogger{LexAI.AIAssistant.API.Controllers.ChatController})">
            <summary>
            Initializes a new instance of the ChatController
            </summary>
            <param name="mediator">MediatR mediator</param>
            <param name="aiAssistantService">AI assistant service</param>
            <param name="conversationService">Conversation service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.AIAssistant.API.Controllers.ChatController.SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto)">
            <summary>
            Sends a message to the AI assistant
            </summary>
            <param name="request">Chat request</param>
            <returns>AI response</returns>
            <response code="200">Message processed successfully</response>
            <response code="400">Invalid request</response>
            <response code="401">User not authenticated</response>
            <response code="429">Rate limit exceeded</response>
        </member>
        <member name="M:LexAI.AIAssistant.API.Controllers.ChatController.ContinueConversation(System.Guid,LexAI.AIAssistant.API.Controllers.ContinueConversationRequestDto)">
            <summary>
            Continues an existing conversation
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="request">Message request</param>
            <returns>AI response</returns>
            <response code="200">Message processed successfully</response>
            <response code="400">Invalid request</response>
            <response code="404">Conversation not found</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.AIAssistant.API.Controllers.ChatController.AnalyzeDocument(LexAI.AIAssistant.Application.DTOs.DocumentAnalysisRequestDto)">
            <summary>
            Analyzes a legal document
            </summary>
            <param name="request">Document analysis request</param>
            <returns>Document analysis response</returns>
            <response code="200">Document analyzed successfully</response>
            <response code="400">Invalid request</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.AIAssistant.API.Controllers.ChatController.PerformLegalResearch(LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto)">
            <summary>
            Performs legal research
            </summary>
            <param name="request">Legal research request</param>
            <returns>Legal research response</returns>
            <response code="200">Research completed successfully</response>
            <response code="400">Invalid request</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.AIAssistant.API.Controllers.ChatController.GenerateDocument(LexAI.AIAssistant.Application.DTOs.DocumentGenerationRequestDto)">
            <summary>
            Generates a legal document
            </summary>
            <param name="request">Document generation request</param>
            <returns>Generated document</returns>
            <response code="200">Document generated successfully</response>
            <response code="400">Invalid request</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.AIAssistant.API.Controllers.ChatController.RateMessage(System.Guid,LexAI.AIAssistant.API.Controllers.RateMessageRequestDto)">
            <summary>
            Rates a message
            </summary>
            <param name="messageId">Message ID</param>
            <param name="request">Rating request</param>
            <returns>Rating confirmation</returns>
            <response code="200">Message rated successfully</response>
            <response code="400">Invalid request</response>
            <response code="404">Message not found</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="T:LexAI.AIAssistant.API.Controllers.ContinueConversationRequestDto">
            <summary>
            Continue conversation request DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.API.Controllers.ContinueConversationRequestDto.Message">
            <summary>
            User message
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.API.Controllers.RateMessageRequestDto">
            <summary>
            Rate message request DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.API.Controllers.RateMessageRequestDto.Rating">
            <summary>
            Rating (1-5)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.API.Controllers.RateMessageRequestDto.Feedback">
            <summary>
            Optional feedback
            </summary>
        </member>
    </members>
</doc>
