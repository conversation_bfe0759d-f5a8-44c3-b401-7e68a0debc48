<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.AIAssistant.Application</name>
    </assembly>
    <members>
        <member name="T:LexAI.AIAssistant.Application.Commands.SendMessageCommand">
            <summary>
            Command to send a message to the AI assistant
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.Commands.SendMessageCommand.Request">
            <summary>
            Chat request
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler">
            <summary>
            Handler for SendMessageCommand
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.#ctor(LexAI.AIAssistant.Application.Interfaces.IAIAssistantService,LexAI.AIAssistant.Application.Commands.IConversationRepository,LexAI.AIAssistant.Application.Interfaces.IMessageProcessingService,LexAI.AIAssistant.Application.Interfaces.IContentModerationService,Microsoft.Extensions.Logging.ILogger{LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler})">
            <summary>
            Initializes a new instance of the SendMessageCommandHandler
            </summary>
            <param name="aiAssistantService">AI assistant service</param>
            <param name="conversationRepository">Conversation repository</param>
            <param name="messageProcessingService">Message processing service</param>
            <param name="contentModerationService">Content moderation service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.Handle(LexAI.AIAssistant.Application.Commands.SendMessageCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the SendMessageCommand
            </summary>
            <param name="request">Send message command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Chat response</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Application.Commands.ContinueConversationCommand">
            <summary>
            Command to continue an existing conversation
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.Commands.ContinueConversationCommand.ConversationId">
            <summary>
            Conversation ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.Commands.ContinueConversationCommand.Message">
            <summary>
            User message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.Commands.ContinueConversationCommand.UserId">
            <summary>
            User ID
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.Commands.ContinueConversationCommandHandler">
            <summary>
            Handler for ContinueConversationCommand
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Commands.ContinueConversationCommandHandler.#ctor(MediatR.IMediator)">
            <summary>
            Initializes a new instance of the ContinueConversationCommandHandler
            </summary>
            <param name="mediator">MediatR mediator</param>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Commands.ContinueConversationCommandHandler.Handle(LexAI.AIAssistant.Application.Commands.ContinueConversationCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the ContinueConversationCommand
            </summary>
            <param name="request">Continue conversation command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Chat response</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Application.Commands.RateMessageCommand">
            <summary>
            Command to rate a message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.Commands.RateMessageCommand.MessageId">
            <summary>
            Message ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.Commands.RateMessageCommand.Rating">
            <summary>
            User rating (1-5)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.Commands.RateMessageCommand.Feedback">
            <summary>
            Optional feedback
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.Commands.RateMessageCommand.UserId">
            <summary>
            User ID
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.Commands.RateMessageCommandHandler">
            <summary>
            Handler for RateMessageCommand
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Commands.RateMessageCommandHandler.#ctor(LexAI.AIAssistant.Application.Commands.IConversationRepository,Microsoft.Extensions.Logging.ILogger{LexAI.AIAssistant.Application.Commands.RateMessageCommandHandler})">
            <summary>
            Initializes a new instance of the RateMessageCommandHandler
            </summary>
            <param name="conversationRepository">Conversation repository</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Commands.RateMessageCommandHandler.Handle(LexAI.AIAssistant.Application.Commands.RateMessageCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the RateMessageCommand
            </summary>
            <param name="request">Rate message command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if rating was successful</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Application.Commands.IConversationRepository">
            <summary>
            Repository interface for conversations
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Commands.IConversationRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a conversation by ID
            </summary>
            <param name="id">Conversation ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Conversation</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Commands.IConversationRepository.GetByMessageIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a conversation by message ID
            </summary>
            <param name="messageId">Message ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Conversation</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Commands.IConversationRepository.AddAsync(LexAI.AIAssistant.Domain.Entities.Conversation,System.Threading.CancellationToken)">
            <summary>
            Adds a new conversation
            </summary>
            <param name="conversation">Conversation to add</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Commands.IConversationRepository.UpdateAsync(LexAI.AIAssistant.Domain.Entities.Conversation,System.Threading.CancellationToken)">
            <summary>
            Updates a conversation
            </summary>
            <param name="conversation">Conversation to update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Commands.IConversationRepository.GetByUserIdAsync(System.Guid,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets conversations for a user
            </summary>
            <param name="userId">User ID</param>
            <param name="limit">Maximum number of conversations</param>
            <param name="offset">Offset for pagination</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>User conversations</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisRequestDto">
            <summary>
            Document analysis request DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisRequestDto.UserId">
            <summary>
            User ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisRequestDto.DocumentName">
            <summary>
            Document name
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisRequestDto.DocumentContent">
            <summary>
            Document content
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisRequestDto.DocumentType">
            <summary>
            Document type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisRequestDto.FocusAreas">
            <summary>
            Analysis focus areas
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisRequestDto.Context">
            <summary>
            Additional context
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisResponseDto">
            <summary>
            Document analysis response DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisResponseDto.DocumentName">
            <summary>
            Document name
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisResponseDto.Analysis">
            <summary>
            Analysis result
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisResponseDto.KeyFindings">
            <summary>
            Key findings
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisResponseDto.RiskAssessment">
            <summary>
            Risk assessment
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisResponseDto.Recommendations">
            <summary>
            Recommendations
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisResponseDto.Citations">
            <summary>
            Citations
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisResponseDto.ProcessingTimeMs">
            <summary>
            Processing time in milliseconds
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisResponseDto.TokensUsed">
            <summary>
            Tokens used
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentAnalysisResponseDto.EstimatedCost">
            <summary>
            Estimated cost
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto">
            <summary>
            Legal research request DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto.UserId">
            <summary>
            User ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto.Query">
            <summary>
            Research query
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto.Domain">
            <summary>
            Legal domain filter
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto.Jurisdiction">
            <summary>
            Jurisdiction
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto.Depth">
            <summary>
            Research depth
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto.IncludeCaseLaw">
            <summary>
            Include case law
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto.IncludeLegislation">
            <summary>
            Include legislation
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto.IncludeAcademicSources">
            <summary>
            Include academic sources
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.LegalResearchResponseDto">
            <summary>
            Legal research response DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchResponseDto.Query">
            <summary>
            Research query
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchResponseDto.Research">
            <summary>
            Research result
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchResponseDto.KeyInsights">
            <summary>
            Key insights
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchResponseDto.RelevantDocuments">
            <summary>
            Relevant documents
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchResponseDto.Citations">
            <summary>
            Citations
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchResponseDto.ProcessingTimeMs">
            <summary>
            Processing time in milliseconds
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchResponseDto.TokensUsed">
            <summary>
            Tokens used
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalResearchResponseDto.EstimatedCost">
            <summary>
            Estimated cost
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.DocumentGenerationRequestDto">
            <summary>
            Document generation request DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentGenerationRequestDto.UserId">
            <summary>
            User ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentGenerationRequestDto.DocumentType">
            <summary>
            Document type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentGenerationRequestDto.Requirements">
            <summary>
            Requirements and specifications
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentGenerationRequestDto.Template">
            <summary>
            Template to use
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentGenerationRequestDto.Jurisdiction">
            <summary>
            Jurisdiction
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentGenerationRequestDto.Parameters">
            <summary>
            Additional parameters
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.DocumentGenerationResponseDto">
            <summary>
            Document generation response DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentGenerationResponseDto.DocumentType">
            <summary>
            Document type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentGenerationResponseDto.GeneratedContent">
            <summary>
            Generated content
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentGenerationResponseDto.Metadata">
            <summary>
            Document metadata
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentGenerationResponseDto.ProcessingTimeMs">
            <summary>
            Processing time in milliseconds
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentGenerationResponseDto.TokensUsed">
            <summary>
            Tokens used
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.DocumentGenerationResponseDto.EstimatedCost">
            <summary>
            Estimated cost
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.ConversationSummaryDto">
            <summary>
            Conversation summary DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationSummaryDto.ConversationId">
            <summary>
            Conversation ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationSummaryDto.Summary">
            <summary>
            Conversation summary
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationSummaryDto.KeyTopics">
            <summary>
            Key topics discussed
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationSummaryDto.MainConclusions">
            <summary>
            Main conclusions
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationSummaryDto.ActionItems">
            <summary>
            Action items
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationSummaryDto.GeneratedAt">
            <summary>
            Generated timestamp
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.ConversationDto">
            <summary>
            Conversation DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.Id">
            <summary>
            Conversation ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.Title">
            <summary>
            Conversation title
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.UserId">
            <summary>
            User ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.SessionId">
            <summary>
            Session ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.Status">
            <summary>
            Conversation status
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.PrimaryDomain">
            <summary>
            Primary legal domain
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.Tags">
            <summary>
            Conversation tags
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.Messages">
            <summary>
            Messages in the conversation
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.Context">
            <summary>
            Conversation context
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.CreatedAt">
            <summary>
            Created timestamp
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.LastActivityAt">
            <summary>
            Last activity timestamp
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.MessageCount">
            <summary>
            Message count
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.TotalTokensUsed">
            <summary>
            Total tokens used
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.EstimatedCost">
            <summary>
            Estimated cost
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationDto.UserRating">
            <summary>
            User rating
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.CreateConversationRequestDto">
            <summary>
            Create conversation request DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.CreateConversationRequestDto.Title">
            <summary>
            Conversation title
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.CreateConversationRequestDto.UserId">
            <summary>
            User ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.CreateConversationRequestDto.SessionId">
            <summary>
            Session ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.CreateConversationRequestDto.Context">
            <summary>
            Conversation context
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.UpdateConversationRequestDto">
            <summary>
            Update conversation request DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.UpdateConversationRequestDto.Title">
            <summary>
            New title
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.UpdateConversationRequestDto.Tags">
            <summary>
            Tags to add or update
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.UpdateConversationRequestDto.Context">
            <summary>
            Updated context
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.AIModelRequestDto">
            <summary>
            AI model request DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.AIModelRequestDto.ModelType">
            <summary>
            Model type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.AIModelRequestDto.Messages">
            <summary>
            Messages for the conversation
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.AIModelRequestDto.MaxTokens">
            <summary>
            Maximum tokens
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.AIModelRequestDto.Temperature">
            <summary>
            Temperature
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.AIModelRequestDto.Parameters">
            <summary>
            Additional parameters
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.AIModelResponseDto">
            <summary>
            AI model response DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.AIModelResponseDto.Content">
            <summary>
            Generated content
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.AIModelResponseDto.TokensUsed">
            <summary>
            Tokens used
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.AIModelResponseDto.EstimatedCost">
            <summary>
            Estimated cost
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.AIModelResponseDto.ModelUsed">
            <summary>
            Model used
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.AIModelResponseDto.Metadata">
            <summary>
            Response metadata
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.AIMessageDto">
            <summary>
            AI message DTO for model requests
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.AIMessageDto.Role">
            <summary>
            Message role
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.AIMessageDto.Content">
            <summary>
            Message content
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.ContentModerationResultDto">
            <summary>
            Content moderation result DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ContentModerationResultDto.IsApproved">
            <summary>
            Whether content is approved
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ContentModerationResultDto.Reason">
            <summary>
            Moderation reason if not approved
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ContentModerationResultDto.ConfidenceScore">
            <summary>
            Confidence score
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ContentModerationResultDto.DetectedCategories">
            <summary>
            Detected categories
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.ResearchDepth">
            <summary>
            Research depth enumeration
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Application.DTOs.ResearchDepth.Quick">
            <summary>
            Quick overview
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Application.DTOs.ResearchDepth.Standard">
            <summary>
            Standard research
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Application.DTOs.ResearchDepth.Deep">
            <summary>
            Deep comprehensive research
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Application.DTOs.ResearchDepth.Exhaustive">
            <summary>
            Exhaustive research
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.ChatRequestDto">
            <summary>
            Chat request DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatRequestDto.Message">
            <summary>
            User message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatRequestDto.UserId">
            <summary>
            User ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatRequestDto.SessionId">
            <summary>
            Session ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatRequestDto.ConversationId">
            <summary>
            Conversation ID (optional for new conversations)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatRequestDto.Context">
            <summary>
            Conversation context
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatRequestDto.IncludeLegalResearch">
            <summary>
            Whether to include legal research
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatRequestDto.IncludeCitations">
            <summary>
            Whether to include citations
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatRequestDto.Attachments">
            <summary>
            Message attachments
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatRequestDto.Metadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.ChatResponseDto">
            <summary>
            Chat response DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.ConversationId">
            <summary>
            Conversation ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.Response">
            <summary>
            AI response message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.MessageId">
            <summary>
            Message ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.ResponseType">
            <summary>
            Response type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.DetectedIntent">
            <summary>
            Detected intent
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.DetectedDomain">
            <summary>
            Detected legal domain
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.ConfidenceScore">
            <summary>
            Confidence score
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.Citations">
            <summary>
            Citations used in the response
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.RelatedDocuments">
            <summary>
            Related legal documents
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.FollowUpQuestions">
            <summary>
            Follow-up questions
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.ProcessingTimeMs">
            <summary>
            Processing time in milliseconds
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.TokensUsed">
            <summary>
            Tokens used
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.EstimatedCost">
            <summary>
            Estimated cost
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.Quality">
            <summary>
            Response quality score
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.IsCached">
            <summary>
            Whether the response was cached
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ChatResponseDto.Metadata">
            <summary>
            Response metadata
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.ConversationContextDto">
            <summary>
            Conversation context DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationContextDto.ModelType">
            <summary>
            AI model type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationContextDto.Mode">
            <summary>
            Conversation mode
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationContextDto.SystemPrompt">
            <summary>
            System prompt
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationContextDto.MaxTokens">
            <summary>
            Maximum tokens
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationContextDto.Temperature">
            <summary>
            Temperature
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationContextDto.IncludeLegalResearch">
            <summary>
            Include legal research
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationContextDto.IncludeCitations">
            <summary>
            Include citations
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationContextDto.Language">
            <summary>
            Language
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationContextDto.Jurisdiction">
            <summary>
            Jurisdiction
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationContextDto.UserRole">
            <summary>
            User role
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ConversationContextDto.Preferences">
            <summary>
            Additional preferences
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.MessageDto">
            <summary>
            Message DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.Id">
            <summary>
            Message ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.ConversationId">
            <summary>
            Conversation ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.Content">
            <summary>
            Message content
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.Role">
            <summary>
            Message role
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.Type">
            <summary>
            Message type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.Status">
            <summary>
            Message status
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.DetectedDomain">
            <summary>
            Detected domain
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.DetectedIntent">
            <summary>
            Detected intent
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.ConfidenceScore">
            <summary>
            Confidence score
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.Citations">
            <summary>
            Citations
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.Attachments">
            <summary>
            Attachments
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.TokensUsed">
            <summary>
            Tokens used
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.EstimatedCost">
            <summary>
            Estimated cost
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.ProcessingTime">
            <summary>
            Processing time
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.UserRating">
            <summary>
            User rating
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.UserFeedback">
            <summary>
            User feedback
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.CreatedAt">
            <summary>
            Created timestamp
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.CreatedBy">
            <summary>
            Created by
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.IsEdited">
            <summary>
            Whether message is edited
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageDto.ErrorMessage">
            <summary>
            Error message if failed
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.CitationDto">
            <summary>
            Citation DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.CitationDto.Id">
            <summary>
            Citation ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.CitationDto.Type">
            <summary>
            Citation type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.CitationDto.Title">
            <summary>
            Document title
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.CitationDto.Url">
            <summary>
            Document URL
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.CitationDto.Source">
            <summary>
            Source name
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.CitationDto.PublicationDate">
            <summary>
            Publication date
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.CitationDto.RelevanceScore">
            <summary>
            Relevance score
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.CitationDto.Excerpt">
            <summary>
            Excerpt from the document
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.CitationDto.Metadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.MessageAttachmentDto">
            <summary>
            Message attachment DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAttachmentDto.Id">
            <summary>
            Attachment ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAttachmentDto.FileName">
            <summary>
            File name
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAttachmentDto.FileSize">
            <summary>
            File size in bytes
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAttachmentDto.MimeType">
            <summary>
            MIME type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAttachmentDto.Type">
            <summary>
            Attachment type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAttachmentDto.Url">
            <summary>
            File URL or path
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAttachmentDto.Content">
            <summary>
            File content (for small files)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAttachmentDto.UploadedAt">
            <summary>
            Upload timestamp
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAttachmentDto.Metadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.MessageAnalysisDto">
            <summary>
            Message analysis DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAnalysisDto.Message">
            <summary>
            Original message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAnalysisDto.Intent">
            <summary>
            Detected intent
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAnalysisDto.Domain">
            <summary>
            Detected legal domain
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAnalysisDto.ConfidenceScore">
            <summary>
            Confidence score
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAnalysisDto.Entities">
            <summary>
            Extracted entities
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAnalysisDto.SuggestedResponseType">
            <summary>
            Suggested response type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAnalysisDto.ComplexityScore">
            <summary>
            Complexity score
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAnalysisDto.RequiresLegalResearch">
            <summary>
            Requires legal research
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.MessageAnalysisDto.Metadata">
            <summary>
            Analysis metadata
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.ExtractedEntityDto">
            <summary>
            Extracted entity DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ExtractedEntityDto.Text">
            <summary>
            Entity text
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ExtractedEntityDto.Type">
            <summary>
            Entity type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ExtractedEntityDto.StartPosition">
            <summary>
            Start position
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ExtractedEntityDto.EndPosition">
            <summary>
            End position
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ExtractedEntityDto.Confidence">
            <summary>
            Confidence score
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.ExtractedEntityDto.Metadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.LegalDocumentSummaryDto">
            <summary>
            Legal document summary DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentSummaryDto.Id">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentSummaryDto.Title">
            <summary>
            Document title
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentSummaryDto.Summary">
            <summary>
            Document summary
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentSummaryDto.Url">
            <summary>
            Document URL
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentSummaryDto.Source">
            <summary>
            Source name
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentSummaryDto.PublicationDate">
            <summary>
            Publication date
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentSummaryDto.RelevanceScore">
            <summary>
            Relevance score
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentSummaryDto.Domain">
            <summary>
            Legal domain
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.DTOs.LegalDocumentDetailsDto">
            <summary>
            Legal document details DTO
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentDetailsDto.Id">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentDetailsDto.Title">
            <summary>
            Document title
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentDetailsDto.Content">
            <summary>
            Document content
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentDetailsDto.Summary">
            <summary>
            Document summary
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentDetailsDto.Url">
            <summary>
            Document URL
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentDetailsDto.Source">
            <summary>
            Source information
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentDetailsDto.PublicationDate">
            <summary>
            Publication date
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentDetailsDto.Domain">
            <summary>
            Legal domain
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Application.DTOs.LegalDocumentDetailsDto.Tags">
            <summary>
            Document tags
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Application.Interfaces.IAIAssistantService">
            <summary>
            Interface for AI assistant service
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IAIAssistantService.SendMessageAsync(LexAI.AIAssistant.Application.DTOs.ChatRequestDto,System.Threading.CancellationToken)">
            <summary>
            Sends a message to the AI assistant and gets a response
            </summary>
            <param name="request">Chat request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>AI response</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IAIAssistantService.ContinueConversationAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Continues an existing conversation
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="message">User message</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>AI response</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IAIAssistantService.AnalyzeDocumentAsync(LexAI.AIAssistant.Application.DTOs.DocumentAnalysisRequestDto,System.Threading.CancellationToken)">
            <summary>
            Analyzes a legal document
            </summary>
            <param name="request">Document analysis request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Document analysis response</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IAIAssistantService.PerformLegalResearchAsync(LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto,System.Threading.CancellationToken)">
            <summary>
            Performs legal research and provides insights
            </summary>
            <param name="request">Research request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Research response</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IAIAssistantService.GenerateDocumentAsync(LexAI.AIAssistant.Application.DTOs.DocumentGenerationRequestDto,System.Threading.CancellationToken)">
            <summary>
            Generates a legal document based on user requirements
            </summary>
            <param name="request">Document generation request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Generated document</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IAIAssistantService.SummarizeConversationAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Summarizes a conversation
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Conversation summary</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Application.Interfaces.IAIModelService">
            <summary>
            Interface for AI model service
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IAIModelService.GenerateResponseAsync(LexAI.AIAssistant.Application.DTOs.AIModelRequestDto,System.Threading.CancellationToken)">
            <summary>
            Generates a response using the specified AI model
            </summary>
            <param name="request">Model request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Model response</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IAIModelService.GenerateEmbeddingAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Generates embeddings for text
            </summary>
            <param name="text">Text to embed</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Embedding vector</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IAIModelService.EstimateCost(LexAI.AIAssistant.Domain.ValueObjects.AIModelType,System.Int32,System.Int32)">
            <summary>
            Estimates the cost of a request
            </summary>
            <param name="modelType">AI model type</param>
            <param name="inputTokens">Input tokens</param>
            <param name="outputTokens">Output tokens</param>
            <returns>Estimated cost</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IAIModelService.GetMaxContextLength(LexAI.AIAssistant.Domain.ValueObjects.AIModelType)">
            <summary>
            Gets the maximum context length for a model
            </summary>
            <param name="modelType">AI model type</param>
            <returns>Maximum context length</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Application.Interfaces.ILegalResearchIntegrationService">
            <summary>
            Interface for legal research integration service
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.ILegalResearchIntegrationService.SearchLegalDocumentsAsync(System.String,System.Nullable{LexAI.AIAssistant.Domain.ValueObjects.LegalDomain},System.Int32,System.Threading.CancellationToken)">
            <summary>
            Searches for relevant legal documents
            </summary>
            <param name="query">Search query</param>
            <param name="domain">Legal domain filter</param>
            <param name="limit">Maximum number of results</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search results</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.ILegalResearchIntegrationService.GetLegalDocumentAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets detailed information about a legal document
            </summary>
            <param name="documentId">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Document details</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.ILegalResearchIntegrationService.FindSimilarDocumentsAsync(System.Guid,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Finds similar legal documents
            </summary>
            <param name="documentId">Reference document ID</param>
            <param name="limit">Maximum number of results</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Similar documents</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Application.Interfaces.IConversationService">
            <summary>
            Interface for conversation management service
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IConversationService.CreateConversationAsync(LexAI.AIAssistant.Application.DTOs.CreateConversationRequestDto,System.Threading.CancellationToken)">
            <summary>
            Creates a new conversation
            </summary>
            <param name="request">Conversation creation request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Created conversation</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IConversationService.GetConversationAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a conversation by ID
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Conversation details</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IConversationService.GetUserConversationsAsync(System.Guid,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets conversations for a user
            </summary>
            <param name="userId">User ID</param>
            <param name="limit">Maximum number of conversations</param>
            <param name="offset">Offset for pagination</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>User conversations</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IConversationService.UpdateConversationAsync(System.Guid,LexAI.AIAssistant.Application.DTOs.UpdateConversationRequestDto,System.Threading.CancellationToken)">
            <summary>
            Updates a conversation
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="request">Update request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Updated conversation</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IConversationService.DeleteConversationAsync(System.Guid,System.Guid,System.Threading.CancellationToken)">
            <summary>
            Deletes a conversation
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="userId">User ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IConversationService.ArchiveConversationAsync(System.Guid,System.Guid,System.Threading.CancellationToken)">
            <summary>
            Archives a conversation
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="userId">User ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Application.Interfaces.IMessageProcessingService">
            <summary>
            Interface for message processing service
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IMessageProcessingService.ProcessMessageAsync(System.Guid,System.String,LexAI.AIAssistant.Application.DTOs.ConversationContextDto,System.Threading.CancellationToken)">
            <summary>
            Processes a user message and generates AI response
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="userMessage">User message</param>
            <param name="context">Conversation context</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>AI response message</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IMessageProcessingService.AnalyzeMessageAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Analyzes message intent and extracts entities
            </summary>
            <param name="message">Message to analyze</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Message analysis</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IMessageProcessingService.GenerateFollowUpQuestionsAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Generates follow-up questions based on conversation context
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Follow-up questions</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Application.Interfaces.IContentModerationService">
            <summary>
            Interface for content moderation service
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IContentModerationService.ModerateContentAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Checks if content is appropriate and safe
            </summary>
            <param name="content">Content to check</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Moderation result</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IContentModerationService.ContainsSensitiveInformationAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Checks if a message contains sensitive legal information
            </summary>
            <param name="message">Message to check</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if message contains sensitive information</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Application.Interfaces.IContentModerationService.SanitizeContentAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Sanitizes content by removing or masking sensitive information
            </summary>
            <param name="content">Content to sanitize</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Sanitized content</returns>
        </member>
    </members>
</doc>
