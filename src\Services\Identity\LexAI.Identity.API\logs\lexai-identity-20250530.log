2025-05-30 00:10:01.023 +04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:59999/api/auth/login - null null
2025-05-30 00:10:01.046 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 5d1fd29b-6aa4-4031-907f-a00ee5d7addb
2025-05-30 00:10:01.053 +04:00 [INF] Request OPTIONS /api/auth/login completed in 3ms with status 307 (Correlation ID: 5d1fd29b-6aa4-4031-907f-a00ee5d7addb)
2025-05-30 00:10:01.069 +04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:59999/api/auth/login - 307 0 null 46.9847ms
2025-05-30 00:12:05.990 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.html - null null
2025-05-30 00:12:06.009 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.html - 200 null text/html;charset=utf-8 19.3581ms
2025-05-30 00:12:06.052 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.js - null null
2025-05-30 00:12:06.054 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-05-30 00:12:06.061 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.js - 200 null application/javascript;charset=utf-8 9.183ms
2025-05-30 00:12:06.070 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-05-30 00:12:06.076 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 22.5749ms
2025-05-30 00:12:06.105 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 34.9238ms
2025-05-30 00:12:06.445 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - null null
2025-05-30 00:12:06.471 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 26.2115ms
2025-05-30 00:12:36.488 +04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:59999/api/auth/login - null null
2025-05-30 00:12:36.511 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID f0c1b0b5-fdfd-4406-94ba-db6d282773f6
2025-05-30 00:12:36.519 +04:00 [INF] Request OPTIONS /api/auth/login completed in 0ms with status 307 (Correlation ID: f0c1b0b5-fdfd-4406-94ba-db6d282773f6)
2025-05-30 00:12:36.529 +04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:59999/api/auth/login - 307 0 null 40.35ms
2025-05-30 00:13:53.268 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-30 00:13:53.303 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-30 00:13:53.312 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-05-30 00:13:53.621 +04:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-05-30 00:13:53.634 +04:00 [INF] LexAI Identity Service started successfully
2025-05-30 00:13:53.651 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-30 00:13:53.762 +04:00 [INF] Now listening on: https://localhost:59998
2025-05-30 00:13:53.764 +04:00 [INF] Now listening on: http://localhost:59999
2025-05-30 00:13:53.796 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-30 00:13:53.800 +04:00 [INF] Hosting environment: Development
2025-05-30 00:13:53.802 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-05-30 00:15:28.482 +04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:59999/api/auth/login - null null
2025-05-30 00:15:28.519 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 5bf13c79-73e1-47b6-a3ae-df4ec6df345e
2025-05-30 00:15:28.524 +04:00 [INF] Request OPTIONS /api/auth/login completed in 0ms with status 307 (Correlation ID: 5bf13c79-73e1-47b6-a3ae-df4ec6df345e)
2025-05-30 00:15:28.535 +04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:59999/api/auth/login - 307 0 null 54.0565ms
2025-05-30 00:16:50.148 +04:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:59999/api/auth/login - null null
2025-05-30 00:16:50.166 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID d5224c72-8565-4368-8773-2f0b123cae0c
2025-05-30 00:16:50.178 +04:00 [INF] Request OPTIONS /api/auth/login completed in 0ms with status 307 (Correlation ID: d5224c72-8565-4368-8773-2f0b123cae0c)
2025-05-30 00:16:50.186 +04:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:59999/api/auth/login - 307 0 null 38.0048ms
2025-05-30 00:17:07.003 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:59999/api/auth/login - null null
2025-05-30 00:17:07.121 +04:00 [INF] Request GET /api/auth/login started with correlation ID 44dcaa0a-5b0f-4075-bf40-50033812d094
2025-05-30 00:17:07.127 +04:00 [INF] Request GET /api/auth/login completed in 0ms with status 307 (Correlation ID: 44dcaa0a-5b0f-4075-bf40-50033812d094)
2025-05-30 00:17:07.131 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:59999/api/auth/login - 307 0 null 127.3819ms
2025-05-30 00:17:07.197 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/api/auth/login - null null
2025-05-30 00:17:07.207 +04:00 [INF] Request GET /api/auth/login started with correlation ID e650c7de-4b96-418a-9d7b-60a11b31ba31
2025-05-30 00:17:07.247 +04:00 [INF] Executing endpoint '405 HTTP Method Not Supported'
2025-05-30 00:17:07.253 +04:00 [INF] Executed endpoint '405 HTTP Method Not Supported'
2025-05-30 00:17:07.255 +04:00 [INF] Request GET /api/auth/login completed in 46ms with status 405 (Correlation ID: e650c7de-4b96-418a-9d7b-60a11b31ba31)
2025-05-30 00:17:07.259 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/api/auth/login - 405 0 null 62.0512ms
2025-05-30 00:17:57.741 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-05-30 00:17:57.756 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 70b6f66c-1cf9-45b0-98b6-7fac21ed7b0e
2025-05-30 00:17:57.761 +04:00 [INF] CORS policy execution successful.
2025-05-30 00:17:57.764 +04:00 [INF] Request OPTIONS /api/auth/login completed in 4ms with status 204 (Correlation ID: 70b6f66c-1cf9-45b0-98b6-7fac21ed7b0e)
2025-05-30 00:17:57.768 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 26.9164ms
2025-05-30 00:17:57.773 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 51
2025-05-30 00:17:57.781 +04:00 [INF] Request POST /api/auth/login started with correlation ID 3ba61c6a-755b-4c12-a306-6a8c354b9d95
2025-05-30 00:17:57.784 +04:00 [INF] CORS policy execution successful.
2025-05-30 00:17:57.786 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 00:17:57.809 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 00:17:57.840 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-05-30 00:17:57.858 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-05-30 00:17:58.148 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-30 00:17:58.220 +04:00 [INF] Executed DbCommand (14ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 00:17:58.233 +04:00 [WRN] Login attempt with non-<NAME_EMAIL>
2025-05-30 00:17:58.236 +04:00 [WRN] Login failed <NAME_EMAIL>
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid email or password
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 83
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-05-30 00:17:58.256 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-30 00:17:58.277 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 460.4179ms
2025-05-30 00:17:58.283 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 00:17:58.288 +04:00 [INF] Request POST /api/auth/login completed in 505ms with status 401 (Correlation ID: 3ba61c6a-755b-4c12-a306-6a8c354b9d95)
2025-05-30 00:17:58.311 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 537.7028ms
2025-05-30 00:18:37.632 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-05-30 00:18:37.645 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 2774d57c-dcb9-498f-a1ad-3bee8c0b9cab
2025-05-30 00:18:37.652 +04:00 [INF] CORS policy execution successful.
2025-05-30 00:18:37.657 +04:00 [INF] Request OPTIONS /api/auth/login completed in 4ms with status 204 (Correlation ID: 2774d57c-dcb9-498f-a1ad-3bee8c0b9cab)
2025-05-30 00:18:37.665 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 33.3594ms
2025-05-30 00:18:37.671 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 57
2025-05-30 00:18:37.685 +04:00 [INF] Request POST /api/auth/login started with correlation ID ecaaa9fe-4f80-4ed8-90dd-e29d71111bd3
2025-05-30 00:18:37.695 +04:00 [INF] CORS policy execution successful.
2025-05-30 00:18:37.707 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 00:18:37.714 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 00:18:37.724 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-05-30 00:18:37.730 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-05-30 00:18:37.811 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 00:18:38.207 +04:00 [WRN] Failed login attempt for user "c59f970a-dd2e-402b-9c69-84503db4787e" - invalid password
2025-05-30 00:18:38.348 +04:00 [INF] Executed DbCommand (17ms) [Parameters=[@p15='7513f1e2-d27a-4b59-97cc-bef0a3406764', @p0='FailedLogin' (Nullable = false), @p1='"Failed login attempt from ::1. Attempts: 1"' (DbType = Object), @p2='2025-05-29T20:18:38.2114086Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='c59f970a-dd2e-402b-9c69-84503db4787e', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-05-29T20:18:38.2115708Z' (DbType = DateTime), @p11='2025-05-29T20:18:38.2627500Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='c59f970a-dd2e-402b-9c69-84503db4787e' (Nullable = false), @p16=NULL (DbType = Binary), @p38='c59f970a-dd2e-402b-9c69-84503db4787e', @p17='2025-05-29T19:58:59.7514010Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='1', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27=NULL (DbType = DateTime), @p28=NULL, @p29='Wilfried' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$inGFkGIadpCPU1I3TIVDWOhwQ5dLSKL3BEHqUHgu4OoXfryZScEe6' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-05-29T20:18:38.2626931Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39=NULL (DbType = Binary)], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND "Version" IS NULL
RETURNING "Version";
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND "Version" IS NULL
RETURNING "Version";
2025-05-30 00:18:38.363 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 107
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-05-30 00:18:38.372 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-30 00:18:38.374 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 652.7067ms
2025-05-30 00:18:38.376 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 00:18:38.377 +04:00 [INF] Request POST /api/auth/login completed in 683ms with status 401 (Correlation ID: ecaaa9fe-4f80-4ed8-90dd-e29d71111bd3)
2025-05-30 00:18:38.382 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 710.8621ms
2025-05-30 00:20:00.172 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-05-30 00:20:00.192 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 216a677a-ffa2-4ed2-bc8d-7b5bf779505b
2025-05-30 00:20:00.201 +04:00 [INF] CORS policy execution successful.
2025-05-30 00:20:00.206 +04:00 [INF] Request OPTIONS /api/auth/login completed in 5ms with status 204 (Correlation ID: 216a677a-ffa2-4ed2-bc8d-7b5bf779505b)
2025-05-30 00:20:00.218 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 45.9374ms
2025-05-30 00:20:00.224 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 57
2025-05-30 00:20:00.245 +04:00 [INF] Request POST /api/auth/login started with correlation ID 86c45cf5-e901-4714-aecf-75342d629233
2025-05-30 00:20:00.251 +04:00 [INF] CORS policy execution successful.
2025-05-30 00:20:00.255 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 00:20:00.259 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 00:20:00.264 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-05-30 00:20:00.273 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-05-30 00:20:00.306 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 00:20:00.596 +04:00 [WRN] Failed login attempt for user "c59f970a-dd2e-402b-9c69-84503db4787e" - invalid password
2025-05-30 00:20:00.606 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p15='929a0f94-d6d6-4d89-b495-9007db28bf12', @p0='FailedLogin' (Nullable = false), @p1='"Failed login attempt from ::1. Attempts: 1"' (DbType = Object), @p2='2025-05-29T20:20:00.6002071Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='c59f970a-dd2e-402b-9c69-84503db4787e', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-05-29T20:20:00.6002146Z' (DbType = DateTime), @p11='2025-05-29T20:20:00.6017102Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='c59f970a-dd2e-402b-9c69-84503db4787e' (Nullable = false), @p16=NULL (DbType = Binary), @p38='c59f970a-dd2e-402b-9c69-84503db4787e', @p17='2025-05-29T19:58:59.7514010Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='1', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27=NULL (DbType = DateTime), @p28=NULL, @p29='Wilfried' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$inGFkGIadpCPU1I3TIVDWOhwQ5dLSKL3BEHqUHgu4OoXfryZScEe6' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-05-29T20:20:00.6017084Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39=NULL (DbType = Binary)], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND "Version" IS NULL
RETURNING "Version";
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND "Version" IS NULL
RETURNING "Version";
2025-05-30 00:20:00.613 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 107
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-05-30 00:20:00.620 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-30 00:20:00.622 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 359.0228ms
2025-05-30 00:20:00.625 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 00:20:00.627 +04:00 [INF] Request POST /api/auth/login completed in 377ms with status 401 (Correlation ID: 86c45cf5-e901-4714-aecf-75342d629233)
2025-05-30 00:20:00.630 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 406.5873ms
2025-05-30 00:21:38.827 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-05-30 00:21:38.840 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 516df54d-88a0-4a36-9201-2705caecd33a
2025-05-30 00:21:38.850 +04:00 [INF] CORS policy execution successful.
2025-05-30 00:21:38.881 +04:00 [INF] Request OPTIONS /api/auth/login completed in 31ms with status 204 (Correlation ID: 516df54d-88a0-4a36-9201-2705caecd33a)
2025-05-30 00:21:38.892 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 65.2492ms
2025-05-30 00:21:38.896 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 58
2025-05-30 00:21:38.908 +04:00 [INF] Request POST /api/auth/login started with correlation ID 99af50dc-44d1-4e7a-b5c0-46d825da85ce
2025-05-30 00:21:38.912 +04:00 [INF] CORS policy execution successful.
2025-05-30 00:21:38.916 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 00:21:38.919 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 00:21:38.923 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-05-30 00:21:38.926 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-05-30 00:21:38.935 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 00:21:39.077 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@p15='bf160160-4320-4f7c-8617-6cec979c7b4c', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-05-29T20:21:39.0746348Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='c59f970a-dd2e-402b-9c69-84503db4787e', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-05-29T20:21:39.0746367Z' (DbType = DateTime), @p11='2025-05-29T20:21:39.0749445Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='c59f970a-dd2e-402b-9c69-84503db4787e' (Nullable = false), @p16=NULL (DbType = Binary), @p38='c59f970a-dd2e-402b-9c69-84503db4787e', @p17='2025-05-29T19:58:59.7514010Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='0', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27='2025-05-29T20:21:39.0745770Z' (Nullable = true) (DbType = DateTime), @p28='::1', @p29='Wilfried' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$inGFkGIadpCPU1I3TIVDWOhwQ5dLSKL3BEHqUHgu4OoXfryZScEe6' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-05-29T20:21:39.0749439Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39=NULL (DbType = Binary)], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND "Version" IS NULL
RETURNING "Version";
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND "Version" IS NULL
RETURNING "Version";
2025-05-30 00:21:39.084 +04:00 [ERR] Error during login for user "c59f970a-dd2e-402b-9c69-84503db4787e"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
2025-05-30 00:21:39.091 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-05-30 00:21:39.120 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-30 00:21:39.122 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 199.7739ms
2025-05-30 00:21:39.126 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 00:21:39.129 +04:00 [INF] Request POST /api/auth/login completed in 216ms with status 401 (Correlation ID: 99af50dc-44d1-4e7a-b5c0-46d825da85ce)
2025-05-30 00:21:39.135 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 238.9718ms
2025-05-30 00:50:12.975 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-30 00:50:13.005 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-30 00:50:13.011 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-05-30 00:50:13.362 +04:00 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-05-30 00:50:13.373 +04:00 [INF] LexAI Identity Service started successfully
2025-05-30 00:50:13.398 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-30 00:50:31.119 +04:00 [INF] Now listening on: https://localhost:59998
2025-05-30 00:50:31.120 +04:00 [INF] Now listening on: http://localhost:59999
2025-05-30 00:50:31.214 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-30 00:50:31.221 +04:00 [INF] Hosting environment: Development
2025-05-30 00:50:31.226 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-05-30 00:50:32.281 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-05-30 00:50:32.489 +04:00 [INF] Request GET / started with correlation ID 83141ab0-f151-4efc-90f2-8c6157a99caa
2025-05-30 00:50:32.614 +04:00 [INF] Request GET / completed in 118ms with status 404 (Correlation ID: 83141ab0-f151-4efc-90f2-8c6157a99caa)
2025-05-30 00:50:32.621 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 347.6009ms
2025-05-30 00:50:32.632 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-05-30 00:50:38.121 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.html - null null
2025-05-30 00:50:38.289 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.html - 200 null text/html;charset=utf-8 167.9864ms
2025-05-30 00:50:38.327 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-05-30 00:50:38.327 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.js - null null
2025-05-30 00:50:38.355 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-05-30 00:50:38.360 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.js - 200 null application/javascript;charset=utf-8 32.9368ms
2025-05-30 00:50:38.407 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 80.8504ms
2025-05-30 00:50:38.466 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 111.8605ms
2025-05-30 00:50:38.533 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - null null
2025-05-30 00:50:38.622 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 88.2178ms
2025-05-30 00:51:33.554 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/login - application/json 90
2025-05-30 00:51:33.594 +04:00 [INF] Request POST /api/Auth/login started with correlation ID 01e2551e-afbf-4dc5-a60c-2c5e5308f6f6
2025-05-30 00:51:33.603 +04:00 [INF] CORS policy execution failed.
2025-05-30 00:51:33.607 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-30 00:51:33.620 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 00:51:33.724 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 00:51:33.815 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-05-30 00:51:33.856 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-05-30 00:51:35.025 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-30 00:51:35.309 +04:00 [INF] Executed DbCommand (40ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 00:51:36.228 +04:00 [WRN] Failed login attempt for user "c59f970a-dd2e-402b-9c69-84503db4787e" - invalid password
2025-05-30 00:51:36.648 +04:00 [INF] Executed DbCommand (22ms) [Parameters=[@p15='316675bf-4bcd-4211-81c0-c33696f5bd00', @p0='FailedLogin' (Nullable = false), @p1='"Failed login attempt from ::1. Attempts: 1"' (DbType = Object), @p2='2025-05-29T20:51:36.2327551Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='c59f970a-dd2e-402b-9c69-84503db4787e', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-05-29T20:51:36.2331395Z' (DbType = DateTime), @p11='2025-05-29T20:51:36.3977440Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='c59f970a-dd2e-402b-9c69-84503db4787e' (Nullable = false), @p16=NULL (DbType = Binary), @p38='c59f970a-dd2e-402b-9c69-84503db4787e', @p17='2025-05-29T19:58:59.7514010Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='1', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27=NULL (DbType = DateTime), @p28=NULL, @p29='Wilfried' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$inGFkGIadpCPU1I3TIVDWOhwQ5dLSKL3BEHqUHgu4OoXfryZScEe6' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-05-29T20:51:36.3975778Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39='0x0000000000000000'], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND "Version" IS NULL
RETURNING "Version";
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND "Version" = @p39
RETURNING "Version";
2025-05-30 00:51:37.334 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 107
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-05-30 00:51:37.432 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-30 00:51:37.471 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 3735.7063ms
2025-05-30 00:51:37.475 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 00:51:37.480 +04:00 [INF] Request POST /api/Auth/login completed in 3880ms with status 401 (Correlation ID: 01e2551e-afbf-4dc5-a60c-2c5e5308f6f6)
2025-05-30 00:51:37.490 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/login - 401 null application/json; charset=utf-8 3936.4609ms
2025-05-30 01:07:09.600 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/register - application/json 303
2025-05-30 01:07:09.612 +04:00 [INF] Request POST /api/Auth/register started with correlation ID 4c0c1885-8e77-415e-869d-44caf9f08d2b
2025-05-30 01:07:09.617 +04:00 [INF] CORS policy execution failed.
2025-05-30 01:07:09.619 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-30 01:07:09.626 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-30 01:07:09.637 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 01:07:09.699 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-05-30 01:07:09.723 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-05-30 01:07:09.906 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 01:07:10.537 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@p0='e7404c47-cd3c-4139-a935-7542ecce14e5', @p1='2025-05-29T21:07:10.4990087Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Wilfried' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$MJhbRZnAhRYIPOK.4U/EBe.85pxo/4cQp2raMoiDvzxCuJC/rKywW' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='Lawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-05-29T21:07:10.4751464Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+230', @p24='+230000', @p25='4b0be5a4-c0f3-4046-994e-224e31db2a80', @p26='PasswordChanged' (Nullable = false), @p27='null' (DbType = Object), @p28='2025-05-29T21:07:10.4992106Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='e7404c47-cd3c-4139-a935-7542ecce14e5', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-05-29T21:07:10.4694707Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='4d494fea-5bd4-42bb-8e19-8bc19e465a3f', @p42='ProfileUpdated' (Nullable = false), @p43='null' (DbType = Object), @p44='2025-05-29T21:07:10.4992110Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='e7404c47-cd3c-4139-a935-7542ecce14e5', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-05-29T21:07:10.4740802Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='6c1aab82-0c03-4e37-a68f-0c5e3598bee7', @p58='PreferencesUpdated' (Nullable = false), @p59='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p60='2025-05-29T21:07:10.4992113Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='e7404c47-cd3c-4139-a935-7542ecce14e5', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-05-29T21:07:10.4751551Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='ef92a675-a9ea-4575-8220-458de9032be4', @p74='Created' (Nullable = false), @p75='"User created with role Lawyer"' (DbType = Object), @p76='2025-05-29T21:07:10.4992098Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='e7404c47-cd3c-4139-a935-7542ecce14e5', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-05-29T21:07:09.9547526Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING "Version";
2025-05-30 01:07:10.601 +04:00 [INF] User added successfully: "e7404c47-cd3c-4139-a935-7542ecce14e5"
2025-05-30 01:07:49.236 +04:00 [INF] User "e7404c47-cd3c-4139-a935-7542ecce14e5" registered successfully <NAME_EMAIL> and role "Lawyer"
2025-05-30 01:07:49.250 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "Lawyer"
2025-05-30 01:07:49.254 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-05-30 01:07:49.281 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 39637.3378ms
2025-05-30 01:07:49.289 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-30 01:07:49.292 +04:00 [INF] Request POST /api/Auth/register completed in 39675ms with status 201 (Correlation ID: 4c0c1885-8e77-415e-869d-44caf9f08d2b)
2025-05-30 01:07:49.295 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/register - 201 null application/json; charset=utf-8 39695.4252ms
2025-05-30 01:10:22.697 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.html - null null
2025-05-30 01:10:22.718 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.html - 200 null text/html;charset=utf-8 21.5ms
2025-05-30 01:10:22.789 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.js - null null
2025-05-30 01:10:22.791 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-05-30 01:10:22.843 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-05-30 01:10:22.844 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.js - 200 null application/javascript;charset=utf-8 55.64ms
2025-05-30 01:10:22.857 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 65.9611ms
2025-05-30 01:10:22.879 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.2817ms
2025-05-30 01:10:23.226 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - null null
2025-05-30 01:10:23.244 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 17.9943ms
2025-05-30 01:11:33.009 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/register - application/json 303
2025-05-30 01:11:33.018 +04:00 [INF] Request POST /api/Auth/register started with correlation ID 7f7bb13d-475b-412e-b2cc-a8410df77264
2025-05-30 01:11:33.026 +04:00 [INF] CORS policy execution failed.
2025-05-30 01:11:33.029 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-30 01:11:33.032 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-30 01:11:33.046 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 01:11:33.067 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-05-30 01:11:33.076 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-05-30 01:11:33.097 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 01:11:33.403 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='6f3d31ed-c015-4547-86fd-064e48ac432e', @p1='2025-05-29T21:11:33.3927415Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Wilfried' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$DmPf6KofKMnpCPHxGjsxmuV1RYZEFpTvWhgTyirJ4ZlQv/6VPu14u' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='LegalAssistant' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-05-29T21:11:33.3896709Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+237', @p24='+237000', @p25='24787eb8-deb8-44a9-ad88-e1bafd9f9728', @p26='ProfileUpdated' (Nullable = false), @p27='null' (DbType = Object), @p28='2025-05-29T21:11:33.3949385Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='6f3d31ed-c015-4547-86fd-064e48ac432e', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-05-29T21:11:33.3896583Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='72da9aea-e0df-4fd4-8444-fc9a550d5234', @p42='PreferencesUpdated' (Nullable = false), @p43='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p44='2025-05-29T21:11:33.3949452Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='6f3d31ed-c015-4547-86fd-064e48ac432e', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-05-29T21:11:33.3896733Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='885a3a80-922e-4155-ac3f-5ac2ca3ff14a', @p58='PasswordChanged' (Nullable = false), @p59='null' (DbType = Object), @p60='2025-05-29T21:11:33.3949353Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='6f3d31ed-c015-4547-86fd-064e48ac432e', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-05-29T21:11:33.3892746Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='a8853ae8-3235-4002-a19c-91ae9bc153fa', @p74='Created' (Nullable = false), @p75='"User created with role LegalAssistant"' (DbType = Object), @p76='2025-05-29T21:11:33.3948763Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='6f3d31ed-c015-4547-86fd-064e48ac432e', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-05-29T21:11:33.1143004Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING "Version";
2025-05-30 01:11:33.415 +04:00 [INF] User added successfully: "6f3d31ed-c015-4547-86fd-064e48ac432e"
2025-05-30 01:12:14.663 +04:00 [INF] User "6f3d31ed-c015-4547-86fd-064e48ac432e" registered successfully <NAME_EMAIL> and role "LegalAssistant"
2025-05-30 01:12:14.672 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "LegalAssistant"
2025-05-30 01:12:14.674 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-05-30 01:12:14.679 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 41623.2269ms
2025-05-30 01:12:14.682 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-30 01:12:14.684 +04:00 [INF] Request POST /api/Auth/register completed in 41658ms with status 201 (Correlation ID: 7f7bb13d-475b-412e-b2cc-a8410df77264)
2025-05-30 01:12:14.690 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/register - 201 null application/json; charset=utf-8 41680.9527ms
2025-05-30 01:13:31.953 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-30 01:13:32.008 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-30 01:13:32.015 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-05-30 01:13:32.486 +04:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-05-30 01:13:32.496 +04:00 [INF] LexAI Identity Service started successfully
2025-05-30 01:13:32.526 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-30 01:13:52.084 +04:00 [INF] Now listening on: https://localhost:59998
2025-05-30 01:13:52.088 +04:00 [INF] Now listening on: http://localhost:59999
2025-05-30 01:13:52.247 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-30 01:13:52.256 +04:00 [INF] Hosting environment: Development
2025-05-30 01:13:52.258 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-05-30 01:13:52.801 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-05-30 01:13:53.160 +04:00 [INF] Request GET / started with correlation ID 1e069706-534a-45cb-ab6e-835c4bb7233d
2025-05-30 01:13:53.329 +04:00 [INF] Request GET / completed in 159ms with status 404 (Correlation ID: 1e069706-534a-45cb-ab6e-835c4bb7233d)
2025-05-30 01:13:53.347 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 556.3372ms
2025-05-30 01:13:53.371 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-05-30 01:13:57.125 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.html - null null
2025-05-30 01:13:57.309 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.html - 200 null text/html;charset=utf-8 184.092ms
2025-05-30 01:13:57.340 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-05-30 01:13:57.341 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.js - null null
2025-05-30 01:13:57.358 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-05-30 01:13:57.369 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.js - 200 null application/javascript;charset=utf-8 28.1718ms
2025-05-30 01:13:57.376 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 36.0654ms
2025-05-30 01:13:57.450 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 91.1083ms
2025-05-30 01:13:57.549 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - null null
2025-05-30 01:13:57.646 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 97.6895ms
2025-05-30 01:14:11.871 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/register - application/json 310
2025-05-30 01:14:11.886 +04:00 [INF] Request POST /api/Auth/register started with correlation ID c14fe91b-2bd3-407c-9559-e2408aab64d5
2025-05-30 01:14:11.895 +04:00 [INF] CORS policy execution failed.
2025-05-30 01:14:11.898 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-30 01:14:11.906 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-30 01:14:11.954 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 01:14:12.068 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-05-30 01:14:12.098 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-05-30 01:14:12.929 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-30 01:14:13.119 +04:00 [INF] Executed DbCommand (26ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 01:14:14.774 +04:00 [INF] Executed DbCommand (65ms) [Parameters=[@p0='3bdbab2c-0be3-4e42-93b2-57868a258c80', @p1='2025-05-29T21:14:14.3766224Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Wilfried' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$aNnhy5f19V9B1gyJXul4RO/5b4TatmwpnKiRUWJlwIIcB6tB2hdOG' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='SeniorLawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-05-29T21:14:13.8583310Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+237', @p24='+237000', @p25='24981783-c683-4118-884c-4e56f5c4e71d', @p26='ProfileUpdated' (Nullable = false), @p27='null' (DbType = Object), @p28='2025-05-29T21:14:14.3789714Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='3bdbab2c-0be3-4e42-93b2-57868a258c80', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-05-29T21:14:13.8572833Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='30b4a728-1913-440e-99a3-eadbff8bca10', @p42='PreferencesUpdated' (Nullable = false), @p43='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p44='2025-05-29T21:14:14.3789808Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='3bdbab2c-0be3-4e42-93b2-57868a258c80', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-05-29T21:14:13.8583367Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='62c8cc6f-b040-4c27-b7f8-1f0c183f4fad', @p58='Created' (Nullable = false), @p59='"User created with role SeniorLawyer"' (DbType = Object), @p60='2025-05-29T21:14:14.3788502Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='3bdbab2c-0be3-4e42-93b2-57868a258c80', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-05-29T21:14:13.1805403Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='95c98fa1-5e02-426e-a004-e670fafaa01e', @p74='PasswordChanged' (Nullable = false), @p75='null' (DbType = Object), @p76='2025-05-29T21:14:14.3789530Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='3bdbab2c-0be3-4e42-93b2-57868a258c80', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-05-29T21:14:13.8514112Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING "Version";
2025-05-30 01:14:14.905 +04:00 [INF] User added successfully: "3bdbab2c-0be3-4e42-93b2-57868a258c80"
2025-05-30 01:14:42.676 +04:00 [INF] User "3bdbab2c-0be3-4e42-93b2-57868a258c80" registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-05-30 01:14:42.699 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-05-30 01:14:42.711 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-05-30 01:14:42.747 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 30784.1845ms
2025-05-30 01:14:42.750 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-30 01:14:42.753 +04:00 [INF] Request POST /api/Auth/register completed in 30863ms with status 201 (Correlation ID: c14fe91b-2bd3-407c-9559-e2408aab64d5)
2025-05-30 01:14:42.762 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/register - 201 null application/json; charset=utf-8 30891.361ms
2025-05-30 01:15:28.410 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/login - application/json 90
2025-05-30 01:15:28.457 +04:00 [INF] Request POST /api/Auth/login started with correlation ID c32bb921-dc98-4e94-909d-eb660dbfa383
2025-05-30 01:15:28.460 +04:00 [INF] CORS policy execution failed.
2025-05-30 01:15:28.463 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-30 01:15:28.468 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 01:15:28.473 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 01:15:28.487 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-05-30 01:15:28.497 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-05-30 01:15:28.548 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 01:16:00.123 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p15='15e77b78-fe9a-4918-9826-357f6d40ae55', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-05-29T21:15:28.7448666Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='3bdbab2c-0be3-4e42-93b2-57868a258c80', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-05-29T21:15:28.7448688Z' (DbType = DateTime), @p11='2025-05-29T21:15:51.5721821Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='3bdbab2c-0be3-4e42-93b2-57868a258c80' (Nullable = false), @p16=NULL (DbType = Binary), @p38='3bdbab2c-0be3-4e42-93b2-57868a258c80', @p17='2025-05-29T21:14:14.3766220Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='0', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27='2025-05-29T21:15:28.7446871Z' (Nullable = true) (DbType = DateTime), @p28='::1', @p29='Wilfried' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$aNnhy5f19V9B1gyJXul4RO/5b4TatmwpnKiRUWJlwIIcB6tB2hdOG' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-05-29T21:15:44.5047332Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39=NULL (DbType = Binary)], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND "Version" IS NULL
RETURNING "Version";
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND "Version" IS NULL
RETURNING "Version";
2025-05-30 01:16:00.403 +04:00 [ERR] Error during login for user "3bdbab2c-0be3-4e42-93b2-57868a258c80"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 297
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
2025-05-30 01:16:00.591 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 297
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-05-30 01:16:00.600 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-30 01:16:00.609 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 32129.1648ms
2025-05-30 01:16:00.612 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 01:16:00.613 +04:00 [INF] Request POST /api/Auth/login completed in 32154ms with status 401 (Correlation ID: c32bb921-dc98-4e94-909d-eb660dbfa383)
2025-05-30 01:16:00.616 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/login - 401 null application/json; charset=utf-8 32253.6278ms
2025-05-30 01:25:44.514 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-30 01:25:44.547 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-30 01:25:44.552 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-05-30 01:25:44.894 +04:00 [INF] Executed DbCommand (46ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-05-30 01:25:44.906 +04:00 [INF] LexAI Identity Service started successfully
2025-05-30 01:25:44.936 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-30 01:25:54.723 +04:00 [INF] Now listening on: https://localhost:59998
2025-05-30 01:25:54.725 +04:00 [INF] Now listening on: http://localhost:59999
2025-05-30 01:25:54.774 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-30 01:25:54.775 +04:00 [INF] Hosting environment: Development
2025-05-30 01:25:54.777 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-05-30 01:25:55.552 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-05-30 01:25:55.808 +04:00 [INF] Request GET / started with correlation ID c2d2d87d-11f2-4a0b-a0e8-97942fbfc4d2
2025-05-30 01:25:55.887 +04:00 [INF] Request GET / completed in 74ms with status 404 (Correlation ID: c2d2d87d-11f2-4a0b-a0e8-97942fbfc4d2)
2025-05-30 01:25:55.895 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 358.7388ms
2025-05-30 01:25:55.906 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-05-30 01:31:48.313 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.html - null null
2025-05-30 01:31:48.503 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.html - 200 null text/html;charset=utf-8 191.1925ms
2025-05-30 01:31:48.561 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-05-30 01:31:48.562 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.js - null null
2025-05-30 01:31:48.606 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-05-30 01:31:48.617 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.js - 200 null application/javascript;charset=utf-8 55.4331ms
2025-05-30 01:31:48.656 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 94.6858ms
2025-05-30 01:31:48.709 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 102.9118ms
2025-05-30 01:31:48.741 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - null null
2025-05-30 01:31:48.830 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 89.0669ms
2025-05-30 01:32:27.114 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/register - application/json 312
2025-05-30 01:32:27.124 +04:00 [INF] Request POST /api/Auth/register started with correlation ID 887b5963-dd04-44eb-bec6-ba31e249f6ac
2025-05-30 01:32:27.133 +04:00 [INF] CORS policy execution failed.
2025-05-30 01:32:27.135 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-30 01:32:27.139 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-30 01:32:27.177 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 01:32:27.251 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-05-30 01:32:27.269 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-05-30 01:32:28.074 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-30 01:32:28.317 +04:00 [INF] Executed DbCommand (32ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 01:32:37.160 +04:00 [INF] Executed DbCommand (24ms) [Parameters=[@p0='ee1b54b2-0f71-4741-a0d2-5126812d9b4b', @p1='2025-05-29T21:32:37.0795764Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin3' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Wilfried3' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$98bE1E85r8sfJSaAo6C7IuaRR7QUPae95fW3STJ2u/o3tSHpbPOYW' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='SeniorLawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-05-29T21:32:28.9105342Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+237', @p24='+237000', @p25='030b0e84-0b48-4712-b9c7-ea7a38b25049', @p26='ProfileUpdated' (Nullable = false), @p27='null' (DbType = Object), @p28='2025-05-29T21:32:37.0796467Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='ee1b54b2-0f71-4741-a0d2-5126812d9b4b', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-05-29T21:32:28.9097601Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='68611976-4030-49ae-9be9-b8198d0d4cf4', @p42='PasswordChanged' (Nullable = false), @p43='null' (DbType = Object), @p44='2025-05-29T21:32:37.0796464Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='ee1b54b2-0f71-4741-a0d2-5126812d9b4b', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-05-29T21:32:28.9055367Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='79db1229-576a-48ad-8408-7b024d539eef', @p58='PreferencesUpdated' (Nullable = false), @p59='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p60='2025-05-29T21:32:37.0796468Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='ee1b54b2-0f71-4741-a0d2-5126812d9b4b', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-05-29T21:32:28.9105388Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='a8d085ee-2fa3-44e5-b65e-613b1af5f89e', @p74='Created' (Nullable = false), @p75='"User created with role SeniorLawyer"' (DbType = Object), @p76='2025-05-29T21:32:37.0796458Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='ee1b54b2-0f71-4741-a0d2-5126812d9b4b', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-05-29T21:32:28.3837961Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING "Version";
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING "Version";
2025-05-30 01:32:37.193 +04:00 [INF] User added successfully: "ee1b54b2-0f71-4741-a0d2-5126812d9b4b"
2025-05-30 01:32:49.713 +04:00 [INF] User "ee1b54b2-0f71-4741-a0d2-5126812d9b4b" registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-05-30 01:32:49.719 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-05-30 01:32:49.728 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-05-30 01:32:49.764 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 22578.4002ms
2025-05-30 01:32:49.766 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-05-30 01:32:49.769 +04:00 [INF] Request POST /api/Auth/register completed in 22641ms with status 201 (Correlation ID: 887b5963-dd04-44eb-bec6-ba31e249f6ac)
2025-05-30 01:32:49.775 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/register - 201 null application/json; charset=utf-8 22660.9201ms
2025-05-30 01:34:08.595 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/login - application/json 90
2025-05-30 01:34:08.616 +04:00 [INF] Request POST /api/Auth/login started with correlation ID 5898444f-d53f-46ac-869b-e2c9d4862efa
2025-05-30 01:34:08.621 +04:00 [INF] CORS policy execution failed.
2025-05-30 01:34:08.623 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-30 01:34:08.631 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 01:34:08.645 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 01:34:08.659 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-05-30 01:34:08.675 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-05-30 01:34:08.843 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 01:34:09.414 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@p15='78fb7368-dea2-4ea5-8c89-72478f66a1fb', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-05-29T21:34:09.3825138Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='ee1b54b2-0f71-4741-a0d2-5126812d9b4b', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-05-29T21:34:09.3825178Z' (DbType = DateTime), @p11='2025-05-29T21:34:09.3934039Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='ee1b54b2-0f71-4741-a0d2-5126812d9b4b' (Nullable = false), @p16=NULL (DbType = Binary), @p38='ee1b54b2-0f71-4741-a0d2-5126812d9b4b', @p17='2025-05-29T21:32:37.0795760Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='0', @p22='Kevin3' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27='2025-05-29T21:34:09.3819619Z' (Nullable = true) (DbType = DateTime), @p28='::1', @p29='Wilfried3' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$98bE1E85r8sfJSaAo6C7IuaRR7QUPae95fW3STJ2u/o3tSHpbPOYW' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-05-29T21:34:09.3933999Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39='0x1C41B3D0E89A45F2'], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND "Version" IS NULL
RETURNING "Version";
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND "Version" = @p39
RETURNING "Version";
2025-05-30 01:34:10.228 +04:00 [ERR] Error during login for user "ee1b54b2-0f71-4741-a0d2-5126812d9b4b"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
2025-05-30 01:34:10.820 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto)
2025-05-30 01:34:10.830 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-30 01:34:10.850 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 2199ms
2025-05-30 01:34:10.854 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 01:34:10.857 +04:00 [INF] Request POST /api/Auth/login completed in 2236ms with status 401 (Correlation ID: 5898444f-d53f-46ac-869b-e2c9d4862efa)
2025-05-30 01:34:10.860 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/login - 401 null application/json; charset=utf-8 2265.6565ms
2025-05-30 01:40:31.577 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/login - application/json 90
2025-05-30 01:40:31.604 +04:00 [INF] Request POST /api/Auth/login started with correlation ID 44c670b3-9e2b-4bce-a130-2fc30e06a05d
2025-05-30 01:40:31.608 +04:00 [INF] CORS policy execution failed.
2025-05-30 01:40:31.610 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-30 01:40:31.612 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 01:40:31.615 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 01:40:31.620 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-05-30 01:40:31.624 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-05-30 01:40:31.665 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 01:40:31.970 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@p15='b51e94b1-d458-4701-8dc7-1e82c8285eb1', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-05-29T21:40:31.9585393Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='ee1b54b2-0f71-4741-a0d2-5126812d9b4b', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-05-29T21:40:31.9585432Z' (DbType = DateTime), @p11='2025-05-29T21:40:31.9596132Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='ee1b54b2-0f71-4741-a0d2-5126812d9b4b' (Nullable = false), @p16=NULL (DbType = Binary), @p38='ee1b54b2-0f71-4741-a0d2-5126812d9b4b', @p17='2025-05-29T21:32:37.0795760Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='0', @p22='Kevin3' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27='2025-05-29T21:40:31.9585124Z' (Nullable = true) (DbType = DateTime), @p28='::1', @p29='Wilfried3' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$98bE1E85r8sfJSaAo6C7IuaRR7QUPae95fW3STJ2u/o3tSHpbPOYW' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-05-29T21:40:31.9596105Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39='0x1C41B3D0E89A45F2'], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND "Version" IS NULL
RETURNING "Version";
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND "Version" = @p39
RETURNING "Version";
2025-05-30 01:40:32.711 +04:00 [ERR] Error during login for user "ee1b54b2-0f71-4741-a0d2-5126812d9b4b"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
2025-05-30 01:40:33.287 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto)
2025-05-30 01:40:33.296 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-30 01:40:33.298 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 1679.3996ms
2025-05-30 01:40:33.300 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 01:40:33.301 +04:00 [INF] Request POST /api/Auth/login completed in 1692ms with status 401 (Correlation ID: 44c670b3-9e2b-4bce-a130-2fc30e06a05d)
2025-05-30 01:40:33.302 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/login - 401 null application/json; charset=utf-8 1725.84ms
2025-05-30 01:45:33.952 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.html - null null
2025-05-30 01:45:33.973 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.html - 200 null text/html;charset=utf-8 20.9062ms
2025-05-30 01:45:34.031 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.js - null null
2025-05-30 01:45:34.039 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.js - 200 null application/javascript;charset=utf-8 8.0549ms
2025-05-30 01:45:34.196 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-05-30 01:45:34.198 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-05-30 01:45:34.207 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 11.7722ms
2025-05-30 01:45:34.264 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 66.2519ms
2025-05-30 01:45:34.664 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - null null
2025-05-30 01:45:34.700 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 35.5528ms
2025-05-30 01:46:20.101 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/login - application/json 89
2025-05-30 01:46:20.108 +04:00 [INF] Request POST /api/Auth/login started with correlation ID 992831b9-bf30-432d-ba11-7ac4a1dfe884
2025-05-30 01:46:20.111 +04:00 [INF] CORS policy execution failed.
2025-05-30 01:46:20.114 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-30 01:46:20.116 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 01:46:20.124 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 01:46:20.134 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-05-30 01:46:20.147 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-05-30 01:46:20.184 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 01:46:20.627 +04:00 [INF] Executed DbCommand (14ms) [Parameters=[@p15='a0b1144b-86e4-48af-a527-f04bce16087e', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-05-29T21:46:20.4475481Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='c59f970a-dd2e-402b-9c69-84503db4787e', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-05-29T21:46:20.4475575Z' (DbType = DateTime), @p11='2025-05-29T21:46:20.6110382Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='c59f970a-dd2e-402b-9c69-84503db4787e' (Nullable = false), @p16=NULL (DbType = Binary), @p17='72efc911-2500-4009-8c81-665b2c92ee49', @p18='2025-05-29T21:46:20.6110329Z' (DbType = DateTime), @p19=NULL, @p20=NULL (DbType = DateTime), @p21=NULL, @p22='2025-06-05T21:46:20.5162736Z' (DbType = DateTime), @p23='::1', @p24='False', @p25='False', @p26=NULL, @p27=NULL (DbType = DateTime), @p28=NULL, @p29='oYNYkNraIOAQkGrUw4VS+dKXYs2KHNF+F4C1HoYSyWABYmYGZeBeJnSByDjKWX3hBmzJmzjcmOwyj8V9BbCU/Q==' (Nullable = false), @p30=NULL (DbType = DateTime), @p31=NULL, @p32='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36', @p33='c59f970a-dd2e-402b-9c69-84503db4787e', @p37='c59f970a-dd2e-402b-9c69-84503db4787e', @p34='2025-05-29T21:46:20.4475311Z' (Nullable = true) (DbType = DateTime), @p35='::1', @p36='2025-05-29T21:46:20.6110363Z' (Nullable = true) (DbType = DateTime), @p38='0x0000000000000000'], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND "Version" IS NULL
RETURNING "Version";
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33)
RETURNING "Version";
UPDATE "Users" SET "LastLoginAt" = @p34, "LastLoginIpAddress" = @p35, "UpdatedAt" = @p36
WHERE "Id" = @p37 AND "Version" = @p38
RETURNING "Version";
2025-05-30 01:46:21.533 +04:00 [ERR] Error during login for user "c59f970a-dd2e-402b-9c69-84503db4787e"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.AddAsync(RefreshToken refreshToken, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\RefreshTokenRepository.cs:line 70
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken)
2025-05-30 01:46:22.249 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.AddAsync(RefreshToken refreshToken, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\RefreshTokenRepository.cs:line 70
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken)
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto)
2025-05-30 01:46:22.257 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-30 01:46:22.264 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 2135.0763ms
2025-05-30 01:46:22.274 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 01:46:22.276 +04:00 [INF] Request POST /api/Auth/login completed in 2165ms with status 401 (Correlation ID: 992831b9-bf30-432d-ba11-7ac4a1dfe884)
2025-05-30 01:46:22.280 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/login - 401 null application/json; charset=utf-8 2209.573ms
2025-05-30 01:47:21.889 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/Auth/login - application/json 90
2025-05-30 01:47:21.905 +04:00 [INF] Request POST /api/Auth/login started with correlation ID 1340d11b-1bcb-4366-b0ee-35bcf39eb4be
2025-05-30 01:47:21.911 +04:00 [INF] CORS policy execution failed.
2025-05-30 01:47:21.913 +04:00 [INF] Request origin https://localhost:59998 does not have permission to access the resource.
2025-05-30 01:47:21.916 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 01:47:21.920 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-05-30 01:47:21.928 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-05-30 01:47:21.929 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-05-30 01:47:21.941 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1."Version", u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r."Version", u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0."Version"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u."Version", u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-05-30 01:47:22.233 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p15='7538904d-25d6-4006-9f08-7b52fc35704d', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-05-29T21:47:22.2275758Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='ee1b54b2-0f71-4741-a0d2-5126812d9b4b', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-05-29T21:47:22.2275790Z' (DbType = DateTime), @p11='2025-05-29T21:47:22.2295933Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='ee1b54b2-0f71-4741-a0d2-5126812d9b4b' (Nullable = false), @p16=NULL (DbType = Binary), @p17='16466251-d17d-4ad7-9536-0217fc50cf4d', @p18='2025-05-29T21:47:22.2295881Z' (DbType = DateTime), @p19=NULL, @p20=NULL (DbType = DateTime), @p21=NULL, @p22='2025-06-05T21:47:22.2286358Z' (DbType = DateTime), @p23='::1', @p24='False', @p25='False', @p26=NULL, @p27=NULL (DbType = DateTime), @p28=NULL, @p29='y5tguIIDPPNi4bW6+KIQdm5AADCMRO3GpjMlsSBnlzFzOsDy8xCIO+DHvQUVTUWtw3vJGnC8CxMwO07ZIK6eUw==' (Nullable = false), @p30=NULL (DbType = DateTime), @p31=NULL, @p32='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36', @p33='ee1b54b2-0f71-4741-a0d2-5126812d9b4b', @p37='ee1b54b2-0f71-4741-a0d2-5126812d9b4b', @p34='2025-05-29T21:47:22.2275538Z' (Nullable = true) (DbType = DateTime), @p35='::1', @p36='2025-05-29T21:47:22.2295923Z' (Nullable = true) (DbType = DateTime), @p38='0x1C41B3D0E89A45F2'], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND "Version" IS NULL
RETURNING "Version";
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33)
RETURNING "Version";
UPDATE "Users" SET "LastLoginAt" = @p34, "LastLoginIpAddress" = @p35, "UpdatedAt" = @p36
WHERE "Id" = @p37 AND "Version" = @p38
RETURNING "Version";
2025-05-30 01:47:22.970 +04:00 [ERR] Error during login for user "ee1b54b2-0f71-4741-a0d2-5126812d9b4b"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.AddAsync(RefreshToken refreshToken, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\RefreshTokenRepository.cs:line 70
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken)
2025-05-30 01:47:23.602 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.AddAsync(RefreshToken refreshToken, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\RefreshTokenRepository.cs:line 70
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken)
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto)
2025-05-30 01:47:23.650 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-05-30 01:47:23.653 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 1726.0866ms
2025-05-30 01:47:23.659 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-05-30 01:47:23.661 +04:00 [INF] Request POST /api/Auth/login completed in 1750ms with status 401 (Correlation ID: 1340d11b-1bcb-4366-b0ee-35bcf39eb4be)
2025-05-30 01:47:23.665 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/Auth/login - 401 null application/json; charset=utf-8 1776.2726ms
