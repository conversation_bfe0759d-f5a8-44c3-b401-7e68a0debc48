using LexAI.DataPreprocessing.Domain.ValueObjects;

namespace LexAI.DataPreprocessing.Domain.Entities;

/// <summary>
/// Named entity found in document chunks
/// </summary>
public class NamedEntity
{
    /// <summary>
    /// Entity ID
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Chunk ID this entity belongs to
    /// </summary>
    public Guid ChunkId { get; set; }

    /// <summary>
    /// Entity text
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Entity type
    /// </summary>
    public EntityType Type { get; set; }

    /// <summary>
    /// Start position in chunk
    /// </summary>
    public int StartPosition { get; set; }

    /// <summary>
    /// End position in chunk
    /// </summary>
    public int EndPosition { get; set; }

    /// <summary>
    /// Confidence score (0-1)
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// Entity metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Creates a new named entity
    /// </summary>
    /// <param name="text">Entity text</param>
    /// <param name="type">Entity type</param>
    /// <param name="startPosition">Start position</param>
    /// <param name="endPosition">End position</param>
    /// <param name="confidence">Confidence score</param>
    /// <returns>Named entity</returns>
    public static NamedEntity Create(string text, EntityType type, int startPosition, int endPosition, double confidence)
    {
        return new NamedEntity
        {
            Text = text,
            Type = type,
            StartPosition = startPosition,
            EndPosition = endPosition,
            Confidence = confidence
        };
    }
}
