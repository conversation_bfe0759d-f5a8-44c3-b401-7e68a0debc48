using FluentValidation;
using LexAI.Identity.Application.Commands;
using LexAI.Shared.Domain.Enums;

namespace LexAI.Identity.Application.Validators;

/// <summary>
/// Validator for RegisterUserCommand
/// </summary>
public class RegisterUserCommandValidator : AbstractValidator<RegisterUserCommand>
{
    /// <summary>
    /// Initializes a new instance of the RegisterUserCommandValidator
    /// </summary>
    public RegisterUserCommandValidator()
    {
        RuleFor(x => x.Email)
            .NotEmpty()
            .WithMessage("Email is required")
            .EmailAddress()
            .WithMessage("Email must be a valid email address")
            .MaximumLength(255)
            .WithMessage("Email must not exceed 255 characters");

        RuleFor(x => x.FirstName)
            .NotEmpty()
            .WithMessage("First name is required")
            .MaximumLength(100)
            .WithMessage("First name must not exceed 100 characters")
            .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$")
            .WithMessage("First name can only contain letters, spaces, hyphens, and apostrophes");

        RuleFor(x => x.LastName)
            .NotEmpty()
            .WithMessage("Last name is required")
            .MaximumLength(100)
            .WithMessage("Last name must not exceed 100 characters")
            .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$")
            .WithMessage("Last name can only contain letters, spaces, hyphens, and apostrophes");

        RuleFor(x => x.PhoneNumber)
            .Matches(@"^[\+]?[1-9][\d]{0,15}$")
            .WithMessage("Phone number must be a valid international format")
            .When(x => !string.IsNullOrWhiteSpace(x.PhoneNumber));

        RuleFor(x => x.Password)
            .NotEmpty()
            .WithMessage("Password is required")
            .MinimumLength(8)
            .WithMessage("Password must be at least 8 characters long")
            .MaximumLength(128)
            .WithMessage("Password must not exceed 128 characters")
            .Matches(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]")
            .WithMessage("Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special character");

        RuleFor(x => x.Role)
            .NotEqual(UserRole.Administrator)
            .WithMessage("Administrator role cannot be selected during public registration")
            .IsInEnum()
            .WithMessage("Invalid role selected");

        RuleFor(x => x.PreferredLanguage)
            .NotEmpty()
            .WithMessage("Preferred language is required")
            .Must(BeValidLanguageCode)
            .WithMessage("Preferred language must be a valid language code (e.g., 'fr-FR', 'en-US')");

        RuleFor(x => x.TimeZone)
            .NotEmpty()
            .WithMessage("Time zone is required")
            .Must(BeValidTimeZone)
            .WithMessage("Time zone must be a valid IANA time zone identifier");

        RuleFor(x => x.AcceptTerms)
            .Equal(true)
            .WithMessage("Terms and conditions must be accepted");

        RuleFor(x => x.AcceptPrivacyPolicy)
            .Equal(true)
            .WithMessage("Privacy policy must be accepted");
    }

    /// <summary>
    /// Validates if the language code is valid
    /// </summary>
    /// <param name="languageCode">Language code to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    private static bool BeValidLanguageCode(string languageCode)
    {
        if (string.IsNullOrWhiteSpace(languageCode))
            return false;

        // Common language codes
        var validLanguageCodes = new[]
        {
            "fr-FR", "en-US", "en-GB", "es-ES", "de-DE", "it-IT", "pt-PT", "nl-NL",
            "sv-SE", "da-DK", "no-NO", "fi-FI", "pl-PL", "cs-CZ", "hu-HU", "ro-RO",
            "bg-BG", "hr-HR", "sk-SK", "sl-SI", "et-EE", "lv-LV", "lt-LT", "mt-MT",
            "el-GR", "ru-RU", "uk-UA", "be-BY", "mk-MK", "sr-RS", "bs-BA", "sq-AL",
            "tr-TR", "ar-SA", "he-IL", "fa-IR", "hi-IN", "zh-CN", "zh-TW", "ja-JP",
            "ko-KR", "th-TH", "vi-VN", "id-ID", "ms-MY", "tl-PH"
        };

        return validLanguageCodes.Contains(languageCode, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Validates if the time zone is valid
    /// </summary>
    /// <param name="timeZone">Time zone to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    private static bool BeValidTimeZone(string timeZone)
    {
        if (string.IsNullOrWhiteSpace(timeZone))
            return false;

        try
        {
            TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            return true;
        }
        catch (TimeZoneNotFoundException)
        {
            // Try IANA time zone format
            try
            {
                var timeZoneInfo = TimeZoneInfo.GetSystemTimeZones()
                    .FirstOrDefault(tz => tz.Id.Equals(timeZone, StringComparison.OrdinalIgnoreCase));
                return timeZoneInfo != null;
            }
            catch
            {
                return false;
            }
        }
        catch
        {
            return false;
        }
    }
}
