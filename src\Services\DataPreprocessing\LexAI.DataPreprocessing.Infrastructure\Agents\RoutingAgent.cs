using LexAI.DataPreprocessing.Application.DTOs;
using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Domain.Entities;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace LexAI.DataPreprocessing.Infrastructure.Agents;

/// <summary>
/// Routing agent implementation
/// </summary>
public class RoutingAgent : IRoutingAgent
{
    private readonly ILogger<RoutingAgent> _logger;

    /// <summary>
    /// Agent name
    /// </summary>
    public string AgentName => "RoutingAgent";

    /// <summary>
    /// Agent type
    /// </summary>
    public AgentType AgentType => AgentType.Routing;

    /// <summary>
    /// Supported vector databases
    /// </summary>
    public IEnumerable<VectorDatabaseType> SupportedDatabases => new[]
    {
        VectorDatabaseType.MongoDB,
        VectorDatabaseType.Qdrant,
        VectorDatabaseType.Weaviate,
        VectorDatabaseType.Pinecone
    };

    /// <summary>
    /// Initializes a new instance of the RoutingAgent
    /// </summary>
    /// <param name="logger">Logger</param>
    public RoutingAgent(ILogger<RoutingAgent> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Routes chunks to appropriate vector databases
    /// </summary>
    /// <param name="chunks">Chunks to route</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Routing result</returns>
    public async Task<RoutingResultDto> RouteChunksAsync(
        IEnumerable<DocumentChunk> chunks, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting routing for {ChunkCount} chunks", chunks.Count());

        var stopwatch = Stopwatch.StartNew();
        var result = new RoutingResultDto
        {
            AgentName = AgentName,
            Success = false
        };

        try
        {
            var chunkList = chunks.ToList();
            if (!chunkList.Any())
            {
                result.Errors.Add("No chunks provided for routing");
                return result;
            }

            var routedChunks = new List<RoutedChunkDto>();

            foreach (var chunk in chunkList)
            {
                // Determine the best domain for this chunk
                var primaryDomain = GetPrimaryDomain(chunk);
                
                // Get recommended database for this domain
                var targetDatabase = GetRecommendedDatabase(primaryDomain);
                var targetCollection = GetCollectionName(primaryDomain, targetDatabase);
                
                // Calculate routing confidence based on domain relevance
                var confidence = CalculateRoutingConfidence(chunk, primaryDomain);
                
                var routedChunk = new RoutedChunkDto
                {
                    ChunkId = chunk.Id,
                    TargetDatabase = targetDatabase,
                    TargetCollection = targetCollection,
                    RoutingReason = $"Domain: {primaryDomain}, Quality: {chunk.QualityScore:F2}, Importance: {chunk.ImportanceScore:F2}",
                    Confidence = confidence
                };

                routedChunks.Add(routedChunk);

                _logger.LogDebug("Routed chunk {ChunkId} to {Database}/{Collection} (confidence: {Confidence:F2})", 
                    chunk.Id, targetDatabase, targetCollection, confidence);
            }

            result.RoutedChunks = routedChunks;
            result.Success = true;

            stopwatch.Stop();
            result.RoutingTime = stopwatch.Elapsed;

            _logger.LogInformation("Routing completed for {ChunkCount} chunks in {Time}ms", 
                chunkList.Count, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            result.RoutingTime = stopwatch.Elapsed;
            result.Errors.Add($"Routing failed: {ex.Message}");
            
            _logger.LogError(ex, "Error during chunk routing");
            return result;
        }
    }

    /// <summary>
    /// Determines the best vector database for a domain
    /// </summary>
    /// <param name="domain">Legal domain</param>
    /// <returns>Recommended vector database</returns>
    public VectorDatabaseType GetRecommendedDatabase(LegalDomain domain)
    {
        // Route based on domain characteristics and performance requirements
        return domain switch
        {
            // High-frequency domains go to MongoDB for better integration
            LegalDomain.Commercial or LegalDomain.Labor or LegalDomain.Civil => VectorDatabaseType.MongoDB,
            
            // Specialized domains go to Qdrant for better performance
            LegalDomain.Constitutional or LegalDomain.International or LegalDomain.European => VectorDatabaseType.Qdrant,
            
            // Complex domains go to Weaviate for semantic capabilities
            LegalDomain.IntellectualProperty or LegalDomain.Technology or LegalDomain.Competition => VectorDatabaseType.Weaviate,
            
            // Default to MongoDB for general cases
            _ => VectorDatabaseType.MongoDB
        };
    }

    /// <summary>
    /// Gets the collection name for a domain
    /// </summary>
    /// <param name="domain">Legal domain</param>
    /// <param name="databaseType">Database type</param>
    /// <returns>Collection name</returns>
    public string GetCollectionName(LegalDomain domain, VectorDatabaseType databaseType)
    {
        var domainPrefix = domain.ToString().ToLowerInvariant();
        
        return databaseType switch
        {
            VectorDatabaseType.MongoDB => $"legal_{domainPrefix}_chunks",
            VectorDatabaseType.Qdrant => $"legal-{domainPrefix}",
            VectorDatabaseType.Weaviate => $"Legal{domain}",
            VectorDatabaseType.Pinecone => $"legal-{domainPrefix}-index",
            _ => $"legal_{domainPrefix}_default"
        };
    }

    private static LegalDomain GetPrimaryDomain(DocumentChunk chunk)
    {
        // Get the domain with highest relevance score
        if (chunk.DomainRelevance.Any())
        {
            return chunk.DomainRelevance.OrderByDescending(kvp => kvp.Value).First().Key;
        }

        // Fallback: analyze content for domain indicators
        var content = chunk.Content.ToLowerInvariant();
        
        // Commercial law indicators
        if (content.Contains("contrat") || content.Contains("société") || content.Contains("commercial"))
            return LegalDomain.Commercial;
            
        // Labor law indicators
        if (content.Contains("travail") || content.Contains("salarié") || content.Contains("emploi"))
            return LegalDomain.Labor;
            
        // Civil law indicators
        if (content.Contains("civil") || content.Contains("responsabilité") || content.Contains("dommage"))
            return LegalDomain.Civil;
            
        // Tax law indicators
        if (content.Contains("impôt") || content.Contains("fiscal") || content.Contains("taxe"))
            return LegalDomain.Tax;
            
        // Real estate indicators
        if (content.Contains("immobilier") || content.Contains("propriété") || content.Contains("bail"))
            return LegalDomain.RealEstate;
            
        // Family law indicators
        if (content.Contains("famille") || content.Contains("mariage") || content.Contains("divorce"))
            return LegalDomain.Family;
            
        // Default to Other if no clear domain is detected
        return LegalDomain.Other;
    }

    private static double CalculateRoutingConfidence(DocumentChunk chunk, LegalDomain primaryDomain)
    {
        var confidence = 0.5; // Base confidence
        
        // Higher confidence if domain relevance is available and high
        if (chunk.DomainRelevance.TryGetValue(primaryDomain, out var relevanceScore))
        {
            confidence = Math.Max(confidence, relevanceScore);
        }
        
        // Adjust based on chunk quality
        confidence += (chunk.QualityScore - 0.5) * 0.2;
        
        // Adjust based on chunk importance
        confidence += (chunk.ImportanceScore - 0.5) * 0.1;
        
        // Higher confidence for chunks with more content
        if (chunk.CharacterCount > 500)
            confidence += 0.1;
        else if (chunk.CharacterCount < 100)
            confidence -= 0.1;
            
        // Higher confidence if chunk has keywords
        if (chunk.Keywords.Count > 5)
            confidence += 0.05;
            
        // Higher confidence if chunk has named entities
        if (chunk.NamedEntities.Count > 2)
            confidence += 0.05;
        
        return Math.Max(0.0, Math.Min(1.0, confidence));
    }
}
