D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Application\obj\Debug\net9.0\LexAI.DataPreprocessing.Application.csproj.AssemblyReference.cache
D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Application\obj\Debug\net9.0\LexAI.DataPreprocessing.Application.GeneratedMSBuildEditorConfig.editorconfig
D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Application\obj\Debug\net9.0\LexAI.DataPreprocessing.Application.AssemblyInfoInputs.cache
D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Application\obj\Debug\net9.0\LexAI.DataPreprocessing.Application.AssemblyInfo.cs
D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Application\obj\Debug\net9.0\LexAI.DataPreprocessing.Application.csproj.CoreCompileInputs.cache
D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Application\obj\Debug\net9.0\LexAI.DataPreprocessing.Application.sourcelink.json
