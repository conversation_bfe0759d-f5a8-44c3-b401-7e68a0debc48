﻿# LexAI Test Runner Script
# This script runs all tests for the LexAI project

param(
    [switch]$Unit,
    [switch]$Integration,
    [switch]$All,
    [switch]$Coverage,
    [switch]$Watch,
    [string]$Filter = "",
    [string]$Project = ""
)

$ErrorActionPreference = "Stop"

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Blue = "Blue"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Show-Help {
    Write-ColorOutput "LexAI Test Runner" $Blue
    Write-ColorOutput "=================" $Blue
    Write-Host ""
    Write-ColorOutput "Usage:" $Yellow
    Write-Host "  .\scripts\run-tests.ps1 [OPTIONS]"
    Write-Host ""
    Write-ColorOutput "Options:" $Yellow
    Write-Host "  -Unit              Run only unit tests"
    Write-Host "  -Integration       Run only integration tests"
    Write-Host "  -All               Run all tests (default)"
    Write-Host "  -Coverage          Generate code coverage report"
    Write-Host "  -Watch             Run tests in watch mode"
    Write-Host "  -Filter <pattern>  Filter tests by name pattern"
    Write-Host "  -Project <name>    Run tests for specific project"
    Write-Host ""
    Write-ColorOutput "Examples:" $Yellow
    Write-Host "  .\scripts\run-tests.ps1 -Unit"
    Write-Host "  .\scripts\run-tests.ps1 -Integration"
    Write-Host "  .\scripts\run-tests.ps1 -All -Coverage"
    Write-Host "  .\scripts\run-tests.ps1 -Unit -Filter 'UserTests'"
    Write-Host "  .\scripts\run-tests.ps1 -Project 'LexAI.Identity.UnitTests'"
}

function Test-Prerequisites {
    Write-ColorOutput "🔍 Checking prerequisites..." $Blue
    
    # Check .NET
    try {
        $dotnetVersion = dotnet --version
        Write-ColorOutput "✅ .NET SDK: $dotnetVersion" $Green
    }
    catch {
        Write-ColorOutput "❌ .NET SDK is not installed" $Red
        exit 1
    }
    
    # Check Docker (for integration tests)
    if ($Integration -or $All) {
        try {
            $dockerVersion = docker --version
            Write-ColorOutput "✅ Docker: $dockerVersion" $Green
        }
        catch {
            Write-ColorOutput "❌ Docker is not installed (required for integration tests)" $Red
            exit 1
        }
    }
}

function Run-UnitTests {
    Write-ColorOutput "🧪 Running Unit Tests..." $Blue
    
    $testProjects = @(
        "tests/LexAI.Identity.UnitTests"
    )
    
    $totalPassed = 0
    $totalFailed = 0
    $totalSkipped = 0
    
    foreach ($project in $testProjects) {
        if ($Project -and $project -notlike "*$Project*") {
            continue
        }
        
        Write-ColorOutput "Running tests in $project..." $Yellow
        
        $testArgs = @("test", $project, "--verbosity", "normal", "--logger", "console")
        
        if ($Filter) {
            $testArgs += "--filter"
            $testArgs += $Filter
        }
        
        if ($Coverage) {
            $testArgs += "--collect:XPlat Code Coverage"
        }
        
        if ($Watch) {
            $testArgs += "--watch"
        }
        
        try {
            $result = & dotnet @testArgs
            
            # Parse test results (simplified)
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Tests passed in $project" $Green
                $totalPassed++
            } else {
                Write-ColorOutput "❌ Tests failed in $project" $Red
                $totalFailed++
            }
        }
        catch {
            Write-ColorOutput "❌ Error running tests in $project" $Red
            $totalFailed++
        }
    }
    
    return @{
        Passed = $totalPassed
        Failed = $totalFailed
        Skipped = $totalSkipped
    }
}

function Run-IntegrationTests {
    Write-ColorOutput "🧪 Running Integration Tests..." $Blue
    
    $testProjects = @(
        "tests/LexAI.Identity.IntegrationTests"
    )
    
    $totalPassed = 0
    $totalFailed = 0
    $totalSkipped = 0
    
    foreach ($project in $testProjects) {
        if ($Project -and $project -notlike "*$Project*") {
            continue
        }
        
        Write-ColorOutput "Running integration tests in $project..." $Yellow
        
        $testArgs = @("test", $project, "--verbosity", "normal", "--logger", "console")
        
        if ($Filter) {
            $testArgs += "--filter"
            $testArgs += $Filter
        }
        
        if ($Coverage) {
            $testArgs += "--collect:XPlat Code Coverage"
        }
        
        try {
            $result = & dotnet @testArgs
            
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Integration tests passed in $project" $Green
                $totalPassed++
            } else {
                Write-ColorOutput "❌ Integration tests failed in $project" $Red
                $totalFailed++
            }
        }
        catch {
            Write-ColorOutput "❌ Error running integration tests in $project" $Red
            $totalFailed++
        }
    }
    
    return @{
        Passed = $totalPassed
        Failed = $totalFailed
        Skipped = $totalSkipped
    }
}

function Generate-CoverageReport {
    Write-ColorOutput "📊 Generating Coverage Report..." $Blue
    
    try {
        # Install reportgenerator tool if not already installed
        dotnet tool install -g dotnet-reportgenerator-globaltool 2>$null
        
        # Find coverage files
        $coverageFiles = Get-ChildItem -Path "tests" -Filter "coverage.cobertura.xml" -Recurse
        
        if ($coverageFiles.Count -eq 0) {
            Write-ColorOutput "⚠️  No coverage files found. Run tests with -Coverage first." $Yellow
            return
        }
        
        $coverageArgs = @()
        foreach ($file in $coverageFiles) {
            $coverageArgs += "-reports:$($file.FullName)"
        }
        
        $coverageArgs += "-targetdir:coverage-report"
        $coverageArgs += "-reporttypes:Html;Cobertura"
        
        & reportgenerator @coverageArgs
        
        Write-ColorOutput "✅ Coverage report generated in coverage-report/" $Green
        Write-ColorOutput "📖 Open coverage-report/index.html to view the report" $Blue
    }
    catch {
        Write-ColorOutput "❌ Failed to generate coverage report: $_" $Red
    }
}

function Show-TestSummary {
    param($UnitResults, $IntegrationResults)
    
    Write-Host ""
    Write-ColorOutput "📊 Test Summary" $Blue
    Write-ColorOutput "===============" $Blue
    
    if ($UnitResults) {
        Write-Host ""
        Write-ColorOutput "Unit Tests:" $Yellow
        Write-Host "  Passed: $($UnitResults.Passed)" -ForegroundColor $Green
        Write-Host "  Failed: $($UnitResults.Failed)" -ForegroundColor $(if ($UnitResults.Failed -gt 0) { $Red } else { $Green })
        Write-Host "  Skipped: $($UnitResults.Skipped)" -ForegroundColor $Yellow
    }
    
    if ($IntegrationResults) {
        Write-Host ""
        Write-ColorOutput "Integration Tests:" $Yellow
        Write-Host "  Passed: $($IntegrationResults.Passed)" -ForegroundColor $Green
        Write-Host "  Failed: $($IntegrationResults.Failed)" -ForegroundColor $(if ($IntegrationResults.Failed -gt 0) { $Red } else { $Green })
        Write-Host "  Skipped: $($IntegrationResults.Skipped)" -ForegroundColor $Yellow
    }
    
    $totalPassed = ($UnitResults.Passed ?? 0) + ($IntegrationResults.Passed ?? 0)
    $totalFailed = ($UnitResults.Failed ?? 0) + ($IntegrationResults.Failed ?? 0)
    $totalSkipped = ($UnitResults.Skipped ?? 0) + ($IntegrationResults.Skipped ?? 0)
    
    Write-Host ""
    Write-ColorOutput "Overall:" $Blue
    Write-Host "  Total Passed: $totalPassed" -ForegroundColor $Green
    Write-Host "  Total Failed: $totalFailed" -ForegroundColor $(if ($totalFailed -gt 0) { $Red } else { $Green })
    Write-Host "  Total Skipped: $totalSkipped" -ForegroundColor $Yellow
    
    if ($totalFailed -eq 0) {
        Write-Host ""
        Write-ColorOutput "🎉 All tests passed!" $Green
    } else {
        Write-Host ""
        Write-ColorOutput "❌ Some tests failed. Please review the output above." $Red
    }
}

# Main script logic
if (-not $Unit -and -not $Integration -and -not $All) {
    $All = $true
}

# Change to project root directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = Split-Path -Parent $scriptPath
Set-Location $projectRoot

Write-ColorOutput "🧪 LexAI Test Runner" $Blue
Write-ColorOutput "Working directory: $(Get-Location)" $Blue
Write-Host ""

if ($Watch -and ($Integration -or $All)) {
    Write-ColorOutput "⚠️  Watch mode is not supported for integration tests" $Yellow
    $Integration = $false
    $All = $false
    $Unit = $true
}

Test-Prerequisites

$unitResults = $null
$integrationResults = $null

if ($Unit -or $All) {
    $unitResults = Run-UnitTests
}

if ($Integration -or $All) {
    $integrationResults = Run-IntegrationTests
}

if ($Coverage -and -not $Watch) {
    Generate-CoverageReport
}

Show-TestSummary -UnitResults $unitResults -IntegrationResults $integrationResults

# Exit with appropriate code
$totalFailed = ($unitResults.Failed ?? 0) + ($integrationResults.Failed ?? 0)
exit $totalFailed
