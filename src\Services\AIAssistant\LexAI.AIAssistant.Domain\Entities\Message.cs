using LexAI.AIAssistant.Domain.ValueObjects;
using LexAI.Shared.Domain.Common;

namespace LexAI.AIAssistant.Domain.Entities;

/// <summary>
/// Represents a message in a conversation
/// </summary>
public class Message : BaseEntity
{
    /// <summary>
    /// Conversation ID this message belongs to
    /// </summary>
    public Guid ConversationId { get; private set; }

    /// <summary>
    /// Message content
    /// </summary>
    public string Content { get; private set; } = string.Empty;

    /// <summary>
    /// Message role (User, Assistant, System)
    /// </summary>
    public MessageRole Role { get; private set; }

    /// <summary>
    /// Message type for categorization
    /// </summary>
    public MessageType Type { get; private set; }

    /// <summary>
    /// Message metadata
    /// </summary>
    public MessageMetadata Metadata { get; private set; } = null!;

    /// <summary>
    /// Number of tokens used for this message
    /// </summary>
    public int TokensUsed { get; private set; }

    /// <summary>
    /// Estimated cost for this message
    /// </summary>
    public decimal EstimatedCost { get; private set; }

    /// <summary>
    /// Processing time for AI responses
    /// </summary>
    public TimeSpan? ProcessingTime { get; private set; }

    /// <summary>
    /// Detected legal domain from message content
    /// </summary>
    public LegalDomain? DetectedDomain { get; private set; }

    /// <summary>
    /// Detected intent from message content
    /// </summary>
    public MessageIntent? DetectedIntent { get; private set; }

    /// <summary>
    /// Confidence score for AI responses (0-1)
    /// </summary>
    public double? ConfidenceScore { get; private set; }

    /// <summary>
    /// Citations and references used in the response
    /// </summary>
    public List<Citation> Citations { get; private set; } = new();

    /// <summary>
    /// Attachments associated with the message
    /// </summary>
    public List<MessageAttachment> Attachments { get; private set; } = new();

    /// <summary>
    /// Message status
    /// </summary>
    public MessageStatus Status { get; private set; }

    /// <summary>
    /// Error message if processing failed
    /// </summary>
    public string? ErrorMessage { get; private set; }

    /// <summary>
    /// Whether the message has been edited
    /// </summary>
    public bool IsEdited { get; private set; }

    /// <summary>
    /// Original content before editing
    /// </summary>
    public string? OriginalContent { get; private set; }

    /// <summary>
    /// User rating for AI responses (1-5)
    /// </summary>
    public int? UserRating { get; private set; }

    /// <summary>
    /// User feedback on the message
    /// </summary>
    public string? UserFeedback { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private Message() { }

    /// <summary>
    /// Creates a new user message
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="content">Message content</param>
    /// <param name="userId">User ID</param>
    /// <param name="type">Message type</param>
    /// <returns>New user message</returns>
    public static Message CreateUserMessage(Guid conversationId, string content, Guid userId, MessageType type = MessageType.Question)
    {
        if (conversationId == Guid.Empty)
            throw new ArgumentException("ConversationId cannot be empty", nameof(conversationId));

        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));

        if (userId == Guid.Empty)
            throw new ArgumentException("UserId cannot be empty", nameof(userId));

        var message = new Message
        {
            Id = Guid.NewGuid(),
            ConversationId = conversationId,
            Content = content.Trim(),
            Role = MessageRole.User,
            Type = type,
            Status = MessageStatus.Sent,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId.ToString(),
            TokensUsed = EstimateTokenCount(content),
            EstimatedCost = 0m // User messages don't cost anything
        };

        message.Metadata = MessageMetadata.Create(message.Id, userId);

        return message;
    }

    /// <summary>
    /// Creates a new assistant message
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="content">Message content</param>
    /// <param name="type">Message type</param>
    /// <param name="processingTime">Processing time</param>
    /// <param name="tokensUsed">Tokens used</param>
    /// <param name="estimatedCost">Estimated cost</param>
    /// <returns>New assistant message</returns>
    public static Message CreateAssistantMessage(
        Guid conversationId, 
        string content, 
        MessageType type = MessageType.Answer,
        TimeSpan? processingTime = null,
        int? tokensUsed = null,
        decimal estimatedCost = 0m)
    {
        if (conversationId == Guid.Empty)
            throw new ArgumentException("ConversationId cannot be empty", nameof(conversationId));

        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));

        var message = new Message
        {
            Id = Guid.NewGuid(),
            ConversationId = conversationId,
            Content = content.Trim(),
            Role = MessageRole.Assistant,
            Type = type,
            Status = MessageStatus.Sent,
            ProcessingTime = processingTime,
            TokensUsed = tokensUsed ?? EstimateTokenCount(content),
            EstimatedCost = estimatedCost,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "system"
        };

        message.Metadata = MessageMetadata.Create(message.Id);

        return message;
    }

    /// <summary>
    /// Creates a system message
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="content">Message content</param>
    /// <param name="type">Message type</param>
    /// <returns>New system message</returns>
    public static Message CreateSystemMessage(Guid conversationId, string content, MessageType type = MessageType.System)
    {
        if (conversationId == Guid.Empty)
            throw new ArgumentException("ConversationId cannot be empty", nameof(conversationId));

        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));

        var message = new Message
        {
            Id = Guid.NewGuid(),
            ConversationId = conversationId,
            Content = content.Trim(),
            Role = MessageRole.System,
            Type = type,
            Status = MessageStatus.Sent,
            TokensUsed = EstimateTokenCount(content),
            EstimatedCost = 0m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "system"
        };

        message.Metadata = MessageMetadata.Create(message.Id);

        return message;
    }

    /// <summary>
    /// Updates the message content
    /// </summary>
    /// <param name="content">New content</param>
    /// <param name="updatedBy">User who updated the message</param>
    public void UpdateContent(string content, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));

        if (Role != MessageRole.User)
            throw new InvalidOperationException("Only user messages can be edited");

        if (!IsEdited)
        {
            OriginalContent = Content;
            IsEdited = true;
        }

        Content = content.Trim();
        TokensUsed = EstimateTokenCount(Content);
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy;
    }

    /// <summary>
    /// Sets the detected domain and intent
    /// </summary>
    /// <param name="domain">Detected legal domain</param>
    /// <param name="intent">Detected message intent</param>
    /// <param name="confidenceScore">Confidence score</param>
    public void SetDetectedClassification(LegalDomain? domain, MessageIntent? intent, double? confidenceScore = null)
    {
        DetectedDomain = domain;
        DetectedIntent = intent;
        ConfidenceScore = confidenceScore;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Adds citations to the message
    /// </summary>
    /// <param name="citations">Citations to add</param>
    public void AddCitations(IEnumerable<Citation> citations)
    {
        if (citations == null)
            throw new ArgumentNullException(nameof(citations));

        Citations.AddRange(citations);
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Adds an attachment to the message
    /// </summary>
    /// <param name="attachment">Attachment to add</param>
    public void AddAttachment(MessageAttachment attachment)
    {
        if (attachment == null)
            throw new ArgumentNullException(nameof(attachment));

        Attachments.Add(attachment);
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Marks the message as processing
    /// </summary>
    public void MarkAsProcessing()
    {
        Status = MessageStatus.Processing;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Marks the message as sent
    /// </summary>
    /// <param name="processingTime">Processing time for AI messages</param>
    public void MarkAsSent(TimeSpan? processingTime = null)
    {
        Status = MessageStatus.Sent;
        ProcessingTime = processingTime;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Marks the message as failed
    /// </summary>
    /// <param name="errorMessage">Error message</param>
    public void MarkAsFailed(string errorMessage)
    {
        Status = MessageStatus.Failed;
        ErrorMessage = errorMessage;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Sets user rating and feedback
    /// </summary>
    /// <param name="rating">Rating (1-5)</param>
    /// <param name="feedback">Optional feedback text</param>
    /// <param name="ratedBy">User who provided the rating</param>
    public void SetUserRating(int rating, string? feedback, string ratedBy)
    {
        if (rating < 1 || rating > 5)
            throw new ArgumentException("Rating must be between 1 and 5", nameof(rating));

        if (Role != MessageRole.Assistant)
            throw new InvalidOperationException("Only assistant messages can be rated");

        UserRating = rating;
        UserFeedback = feedback?.Trim();
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = ratedBy;
    }

    /// <summary>
    /// Updates token usage and cost
    /// </summary>
    /// <param name="tokensUsed">Tokens used</param>
    /// <param name="estimatedCost">Estimated cost</param>
    public void UpdateTokenUsage(int tokensUsed, decimal estimatedCost)
    {
        TokensUsed = tokensUsed;
        EstimatedCost = estimatedCost;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Checks if the message is from a user
    /// </summary>
    /// <returns>True if message is from user</returns>
    public bool IsUserMessage()
    {
        return Role == MessageRole.User;
    }

    /// <summary>
    /// Checks if the message is from the assistant
    /// </summary>
    /// <returns>True if message is from assistant</returns>
    public bool IsAssistantMessage()
    {
        return Role == MessageRole.Assistant;
    }

    /// <summary>
    /// Checks if the message is a system message
    /// </summary>
    /// <returns>True if message is system message</returns>
    public bool IsSystemMessage()
    {
        return Role == MessageRole.System;
    }

    /// <summary>
    /// Gets the message preview (first 100 characters)
    /// </summary>
    /// <returns>Message preview</returns>
    public string GetPreview()
    {
        if (Content.Length <= 100)
            return Content;

        return Content.Substring(0, 97) + "...";
    }

    /// <summary>
    /// Estimates token count for a text
    /// </summary>
    /// <param name="text">Text to estimate</param>
    /// <returns>Estimated token count</returns>
    private static int EstimateTokenCount(string text)
    {
        // Simple estimation: ~4 characters per token
        return Math.Max(1, text.Length / 4);
    }
}
