{"format": 1, "restore": {"D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\ApiGateway\\LexAI.ApiGateway.csproj": {}}, "projects": {"D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\ApiGateway\\LexAI.ApiGateway.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\ApiGateway\\LexAI.ApiGateway.csproj", "projectName": "LexAI.ApiGateway", "projectPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\ApiGateway\\LexAI.ApiGateway.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\ApiGateway\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj": {"projectPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AspNetCore.HealthChecks.MongoDb": {"target": "Package", "version": "[8.1.0, )"}, "AspNetCore.HealthChecks.Npgsql": {"target": "Package", "version": "[8.0.2, )"}, "AspNetCore.HealthChecks.RabbitMQ": {"target": "Package", "version": "[8.0.2, )"}, "AspNetCore.HealthChecks.Redis": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"target": "Package", "version": "[9.0.0, )"}, "Ocelot": {"target": "Package", "version": "[23.3.4, )"}, "Ocelot.Cache.CacheManager": {"target": "Package", "version": "[23.3.4, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.2, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj", "projectName": "LexAI.Shared.Domain", "projectPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.9.2, )"}, "MediatR": {"target": "Package", "version": "[12.4.1, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj", "projectName": "LexAI.Shared.Infrastructure", "projectPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj": {"projectPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "MediatR": {"target": "Package", "version": "[12.4.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Mvc.Versioning": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[9.0.0, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.28.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.0, )"}, "RabbitMQ.Client": {"target": "Package", "version": "[6.8.1, )"}, "Serilog": {"target": "Package", "version": "[4.1.0, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.16, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.1.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}