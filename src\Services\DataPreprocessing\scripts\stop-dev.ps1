﻿# Script d'arrêt pour le développement local
param(
    [switch]$RemoveVolumes = $false,
    [switch]$RemoveImages = $false
)

Write-Host "🛑 Arrêt du service Data Preprocessing" -ForegroundColor Yellow

# Naviguer vers le répertoire du service
$serviceDir = "src/Services/DataPreprocessing"
if (!(Test-Path $serviceDir)) {
    Write-Error "Répertoire du service non trouvé : $serviceDir"
    exit 1
}

Set-Location $serviceDir

try {
    # Arrêter les conteneurs
    Write-Host "🐳 Arrêt des conteneurs..." -ForegroundColor Yellow
    
    if ($RemoveVolumes) {
        Write-Host "🗑️ Suppression des volumes (données perdues)..." -ForegroundColor Red
        docker-compose down -v --remove-orphans
    } else {
        docker-compose down --remove-orphans
    }

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Conteneurs arrêtés avec succès" -ForegroundColor Green
    } else {
        Write-Warning "⚠️ Erreur lors de l'arrêt des conteneurs"
    }

    # Supprimer les images si demandé
    if ($RemoveImages) {
        Write-Host "🗑️ Suppression des images Docker..." -ForegroundColor Red
        
        # Lister les images liées au projet
        $images = docker images --filter "reference=*lexai*" --filter "reference=*datapreprocessing*" -q
        
        if ($images) {
            docker rmi $images -f
            Write-Host "✅ Images supprimées" -ForegroundColor Green
        } else {
            Write-Host "ℹ️ Aucune image à supprimer" -ForegroundColor Cyan
        }
    }

    # Nettoyer les ressources inutilisées
    Write-Host "🧹 Nettoyage des ressources inutilisées..." -ForegroundColor Yellow
    docker system prune -f | Out-Null

    Write-Host ""
    Write-Host "✅ Arrêt terminé !" -ForegroundColor Green
    
    if ($RemoveVolumes) {
        Write-Host "⚠️ Les données des bases de données ont été supprimées" -ForegroundColor Red
    } else {
        Write-Host "ℹ️ Les données des bases de données sont conservées" -ForegroundColor Cyan
    }

    Write-Host ""
    Write-Host "📝 Pour redémarrer :" -ForegroundColor Cyan
    Write-Host "  .\scripts\start-dev.ps1" -ForegroundColor White
}
catch {
    Write-Error "❌ Erreur lors de l'arrêt : $_"
    exit 1
}
finally {
    # Retourner au répertoire racine
    Set-Location ../../..
}
