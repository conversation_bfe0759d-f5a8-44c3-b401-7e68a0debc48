using LexAI.Identity.Domain.Entities;
using System.Security.Claims;

namespace LexAI.Identity.Application.Interfaces;

/// <summary>
/// Service interface for JWT token operations
/// </summary>
public interface ITokenService
{
    /// <summary>
    /// Generates a JWT access token for the user
    /// </summary>
    /// <param name="user">User entity</param>
    /// <param name="additionalClaims">Additional claims to include in the token</param>
    /// <returns>JWT access token</returns>
    string GenerateAccessToken(User user, IEnumerable<Claim>? additionalClaims = null);

    /// <summary>
    /// Generates a refresh token for the user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="ipAddress">IP address where the token was created</param>
    /// <param name="userAgent">User agent of the client</param>
    /// <returns>Refresh token entity</returns>
    RefreshToken GenerateRefreshToken(Guid userId, string? ipAddress = null, string? userAgent = null);

    /// <summary>
    /// Validates a JWT access token
    /// </summary>
    /// <param name="token">JWT token to validate</param>
    /// <returns>ClaimsPrincipal if valid, null otherwise</returns>
    ClaimsPrincipal? ValidateAccessToken(string token);

    /// <summary>
    /// Extracts user ID from a JWT token
    /// </summary>
    /// <param name="token">JWT token</param>
    /// <returns>User ID if found, null otherwise</returns>
    Guid? GetUserIdFromToken(string token);

    /// <summary>
    /// Extracts claims from a JWT token
    /// </summary>
    /// <param name="token">JWT token</param>
    /// <returns>Collection of claims</returns>
    IEnumerable<Claim> GetClaimsFromToken(string token);

    /// <summary>
    /// Checks if a JWT token is expired
    /// </summary>
    /// <param name="token">JWT token</param>
    /// <returns>True if expired, false otherwise</returns>
    bool IsTokenExpired(string token);

    /// <summary>
    /// Gets the expiration time of a JWT token
    /// </summary>
    /// <param name="token">JWT token</param>
    /// <returns>Expiration time or null if invalid</returns>
    DateTime? GetTokenExpiration(string token);
}

/// <summary>
/// Service interface for password operations
/// </summary>
public interface IPasswordService
{
    /// <summary>
    /// Hashes a password using a secure algorithm
    /// </summary>
    /// <param name="password">Plain text password</param>
    /// <returns>Hashed password</returns>
    string HashPassword(string password);

    /// <summary>
    /// Verifies a password against its hash
    /// </summary>
    /// <param name="password">Plain text password</param>
    /// <param name="hash">Hashed password</param>
    /// <returns>True if password matches hash</returns>
    bool VerifyPassword(string password, string hash);

    /// <summary>
    /// Generates a secure random password
    /// </summary>
    /// <param name="length">Password length (minimum 8)</param>
    /// <param name="includeSpecialChars">Include special characters</param>
    /// <returns>Generated password</returns>
    string GeneratePassword(int length = 12, bool includeSpecialChars = true);

    /// <summary>
    /// Validates password strength
    /// </summary>
    /// <param name="password">Password to validate</param>
    /// <returns>Password validation result</returns>
    PasswordValidationResult ValidatePasswordStrength(string password);

    /// <summary>
    /// Generates a password reset token
    /// </summary>
    /// <returns>Password reset token</returns>
    string GeneratePasswordResetToken();

    /// <summary>
    /// Generates an email verification token
    /// </summary>
    /// <returns>Email verification token</returns>
    string GenerateEmailVerificationToken();
}

/// <summary>
/// Result of password validation
/// </summary>
public class PasswordValidationResult
{
    /// <summary>
    /// Indicates if the password is valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Password strength score (0-100)
    /// </summary>
    public int Score { get; set; }

    /// <summary>
    /// List of validation errors
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// List of suggestions for improvement
    /// </summary>
    public List<string> Suggestions { get; set; } = new();

    /// <summary>
    /// Password strength level
    /// </summary>
    public PasswordStrength Strength { get; set; }
}

/// <summary>
/// Password strength levels
/// </summary>
public enum PasswordStrength
{
    /// <summary>
    /// Very weak password
    /// </summary>
    VeryWeak = 0,

    /// <summary>
    /// Weak password
    /// </summary>
    Weak = 1,

    /// <summary>
    /// Fair password
    /// </summary>
    Fair = 2,

    /// <summary>
    /// Good password
    /// </summary>
    Good = 3,

    /// <summary>
    /// Strong password
    /// </summary>
    Strong = 4,

    /// <summary>
    /// Very strong password
    /// </summary>
    VeryStrong = 5
}
