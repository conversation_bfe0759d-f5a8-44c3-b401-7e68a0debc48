using LexAI.AIAssistant.Domain.ValueObjects;
using LexAI.Shared.Domain.Common;

namespace LexAI.AIAssistant.Domain.Entities;

/// <summary>
/// Represents a conversation between a user and the AI assistant
/// </summary>
public class Conversation : BaseEntity
{
    /// <summary>
    /// Conversation title
    /// </summary>
    public string Title { get; private set; } = string.Empty;

    /// <summary>
    /// User ID who owns this conversation
    /// </summary>
    public Guid UserId { get; private set; }

    /// <summary>
    /// Session ID for grouping related conversations
    /// </summary>
    public string SessionId { get; private set; } = string.Empty;

    /// <summary>
    /// Conversation context and metadata
    /// </summary>
    public ConversationContext Context { get; private set; } = null!;

    /// <summary>
    /// List of messages in the conversation
    /// </summary>
    public List<Message> Messages { get; private set; } = new();

    /// <summary>
    /// Conversation status
    /// </summary>
    public ConversationStatus Status { get; private set; }

    /// <summary>
    /// Legal domain focus of the conversation
    /// </summary>
    public LegalDomain? PrimaryDomain { get; private set; }

    /// <summary>
    /// Conversation tags for categorization
    /// </summary>
    public List<string> Tags { get; private set; } = new();

    /// <summary>
    /// Conversation summary (auto-generated)
    /// </summary>
    public string? Summary { get; private set; }

    /// <summary>
    /// Last activity timestamp
    /// </summary>
    public DateTime LastActivityAt { get; private set; }

    /// <summary>
    /// Total number of messages
    /// </summary>
    public int MessageCount { get; private set; }

    /// <summary>
    /// Total tokens used in this conversation
    /// </summary>
    public int TotalTokensUsed { get; private set; }

    /// <summary>
    /// Estimated cost of the conversation
    /// </summary>
    public decimal EstimatedCost { get; private set; }

    /// <summary>
    /// Whether the conversation is archived
    /// </summary>
    public bool IsArchived { get; private set; }

    /// <summary>
    /// Conversation rating by user (1-5)
    /// </summary>
    public int? UserRating { get; private set; }

    /// <summary>
    /// User feedback on the conversation
    /// </summary>
    public string? UserFeedback { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private Conversation() { }

    /// <summary>
    /// Creates a new conversation
    /// </summary>
    /// <param name="title">Conversation title</param>
    /// <param name="userId">User ID</param>
    /// <param name="sessionId">Session ID</param>
    /// <param name="context">Conversation context</param>
    /// <returns>New conversation instance</returns>
    public static Conversation Create(string title, Guid userId, string sessionId, ConversationContext? context = null)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));

        if (userId == Guid.Empty)
            throw new ArgumentException("UserId cannot be empty", nameof(userId));

        if (string.IsNullOrWhiteSpace(sessionId))
            throw new ArgumentException("SessionId cannot be empty", nameof(sessionId));

        var conversation = new Conversation
        {
            Id = Guid.NewGuid(),
            Title = title.Trim(),
            UserId = userId,
            SessionId = sessionId,
            Context = context ?? ConversationContext.CreateDefault(),
            Status = ConversationStatus.Active,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId.ToString(),
            LastActivityAt = DateTime.UtcNow,
            MessageCount = 0,
            TotalTokensUsed = 0,
            EstimatedCost = 0m,
            IsArchived = false
        };

        return conversation;
    }

    /// <summary>
    /// Adds a message to the conversation
    /// </summary>
    /// <param name="message">Message to add</param>
    public void AddMessage(Message message)
    {
        if (message == null)
            throw new ArgumentNullException(nameof(message));

        if (Status == ConversationStatus.Closed)
            throw new InvalidOperationException("Cannot add messages to a closed conversation");

        Messages.Add(message);
        MessageCount = Messages.Count;
        LastActivityAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;

        // Update token usage and cost
        TotalTokensUsed += message.TokensUsed;
        EstimatedCost += message.EstimatedCost;

        // Auto-detect primary domain from messages
        if (PrimaryDomain == null && message.DetectedDomain.HasValue)
        {
            PrimaryDomain = message.DetectedDomain;
        }

        // Auto-generate title from first user message if title is generic
        if (Messages.Count == 1 && message.Role == MessageRole.User && 
            (Title == "New Conversation" || Title.StartsWith("Conversation")))
        {
            Title = GenerateTitleFromMessage(message.Content);
        }
    }

    /// <summary>
    /// Updates the conversation title
    /// </summary>
    /// <param name="title">New title</param>
    /// <param name="updatedBy">User who updated the title</param>
    public void UpdateTitle(string title, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));

        Title = title.Trim();
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy;
    }

    /// <summary>
    /// Updates the conversation context
    /// </summary>
    /// <param name="context">New context</param>
    public void UpdateContext(ConversationContext context)
    {
        Context = context ?? throw new ArgumentNullException(nameof(context));
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Adds tags to the conversation
    /// </summary>
    /// <param name="tags">Tags to add</param>
    public void AddTags(params string[] tags)
    {
        foreach (var tag in tags.Where(t => !string.IsNullOrWhiteSpace(t)))
        {
            var normalizedTag = tag.Trim().ToLowerInvariant();
            if (!Tags.Contains(normalizedTag))
            {
                Tags.Add(normalizedTag);
            }
        }
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Removes tags from the conversation
    /// </summary>
    /// <param name="tags">Tags to remove</param>
    public void RemoveTags(params string[] tags)
    {
        foreach (var tag in tags.Where(t => !string.IsNullOrWhiteSpace(t)))
        {
            var normalizedTag = tag.Trim().ToLowerInvariant();
            Tags.Remove(normalizedTag);
        }
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Sets the conversation summary
    /// </summary>
    /// <param name="summary">Conversation summary</param>
    public void SetSummary(string summary)
    {
        Summary = summary?.Trim();
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Closes the conversation
    /// </summary>
    /// <param name="closedBy">User who closed the conversation</param>
    public void Close(string closedBy)
    {
        Status = ConversationStatus.Closed;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = closedBy;
    }

    /// <summary>
    /// Reopens the conversation
    /// </summary>
    /// <param name="reopenedBy">User who reopened the conversation</param>
    public void Reopen(string reopenedBy)
    {
        if (Status == ConversationStatus.Closed)
        {
            Status = ConversationStatus.Active;
            UpdatedAt = DateTime.UtcNow;
            UpdatedBy = reopenedBy;
            LastActivityAt = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Archives the conversation
    /// </summary>
    /// <param name="archivedBy">User who archived the conversation</param>
    public void Archive(string archivedBy)
    {
        IsArchived = true;
        Status = ConversationStatus.Archived;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = archivedBy;
    }

    /// <summary>
    /// Unarchives the conversation
    /// </summary>
    /// <param name="unarchivedBy">User who unarchived the conversation</param>
    public void Unarchive(string unarchivedBy)
    {
        IsArchived = false;
        Status = ConversationStatus.Active;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = unarchivedBy;
        LastActivityAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Sets user rating and feedback
    /// </summary>
    /// <param name="rating">Rating (1-5)</param>
    /// <param name="feedback">Optional feedback text</param>
    /// <param name="ratedBy">User who provided the rating</param>
    public void SetUserRating(int rating, string? feedback, string ratedBy)
    {
        if (rating < 1 || rating > 5)
            throw new ArgumentException("Rating must be between 1 and 5", nameof(rating));

        UserRating = rating;
        UserFeedback = feedback?.Trim();
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = ratedBy;
    }

    /// <summary>
    /// Gets the last N messages from the conversation
    /// </summary>
    /// <param name="count">Number of messages to retrieve</param>
    /// <returns>Last N messages</returns>
    public IEnumerable<Message> GetLastMessages(int count = 10)
    {
        return Messages
            .OrderByDescending(m => m.CreatedAt)
            .Take(count)
            .OrderBy(m => m.CreatedAt);
    }

    /// <summary>
    /// Gets messages by role
    /// </summary>
    /// <param name="role">Message role</param>
    /// <returns>Messages with the specified role</returns>
    public IEnumerable<Message> GetMessagesByRole(MessageRole role)
    {
        return Messages.Where(m => m.Role == role);
    }

    /// <summary>
    /// Checks if the conversation is active
    /// </summary>
    /// <returns>True if conversation is active</returns>
    public bool IsActive()
    {
        return Status == ConversationStatus.Active && !IsArchived;
    }

    /// <summary>
    /// Checks if the conversation has been idle for too long
    /// </summary>
    /// <param name="idleThreshold">Idle threshold timespan</param>
    /// <returns>True if conversation is idle</returns>
    public bool IsIdle(TimeSpan idleThreshold)
    {
        return DateTime.UtcNow - LastActivityAt > idleThreshold;
    }

    /// <summary>
    /// Gets the conversation duration
    /// </summary>
    /// <returns>Duration of the conversation</returns>
    public TimeSpan GetDuration()
    {
        return LastActivityAt - CreatedAt;
    }

    /// <summary>
    /// Gets the average response time for AI messages
    /// </summary>
    /// <returns>Average response time</returns>
    public TimeSpan GetAverageResponseTime()
    {
        var aiMessages = Messages.Where(m => m.Role == MessageRole.Assistant).ToList();
        if (aiMessages.Count == 0)
            return TimeSpan.Zero;

        var totalResponseTime = TimeSpan.Zero;
        var responseCount = 0;

        for (int i = 0; i < Messages.Count - 1; i++)
        {
            if (Messages[i].Role == MessageRole.User && Messages[i + 1].Role == MessageRole.Assistant)
            {
                totalResponseTime += Messages[i + 1].CreatedAt - Messages[i].CreatedAt;
                responseCount++;
            }
        }

        return responseCount > 0 ? TimeSpan.FromTicks(totalResponseTime.Ticks / responseCount) : TimeSpan.Zero;
    }

    private string GenerateTitleFromMessage(string content)
    {
        // Simple title generation - take first 50 characters
        var title = content.Length > 50 ? content.Substring(0, 47) + "..." : content;
        
        // Remove line breaks and extra spaces
        title = title.Replace('\n', ' ').Replace('\r', ' ');
        while (title.Contains("  "))
        {
            title = title.Replace("  ", " ");
        }

        return title.Trim();
    }
}
