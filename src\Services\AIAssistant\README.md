# 🤖 LexAI AI Assistant Service

Service d'assistant IA juridique avancé pour la plateforme LexAI. Utilise OpenAI GPT-4 et l'intégration avec le service Legal Research pour fournir des conseils juridiques intelligents et contextuels.

## 📋 Fonctionnalités

### ✅ Implémentées
- **Chat IA conversationnel** - Conversations naturelles avec l'assistant juridique
- **Analyse de documents** - Analyse automatique de contrats et documents juridiques
- **Recherche juridique intégrée** - Intégration avec le service Legal Research
- **Génération de documents** - Création automatique de documents juridiques
- **Gestion des conversations** - Historique et contexte des conversations
- **Modération de contenu** - Filtrage automatique du contenu inapproprié
- **Citations automatiques** - Références juridiques dans les réponses
- **Détection d'intention** - Classification automatique des requêtes
- **Support multilingue** - Français et anglais
- **Rate limiting** - Protection contre l'abus d'API

### 🚧 En cours de développement
- **Chat en temps réel** - WebSocket/SignalR pour chat instantané
- **Résumés de conversation** - Génération automatique de résumés
- **Templates de documents** - Modèles prédéfinis pour génération
- **Intégration vocale** - Support de la reconnaissance vocale

## 🏗️ Architecture

Le service suit le pattern **Clean Architecture** avec intégration IA avancée :

```
LexAI.AIAssistant/
├── Domain/                     # Entités métier et logique de domaine
│   ├── Entities/              # Conversation, Message
│   ├── ValueObjects/          # ConversationContext, MessageMetadata
│   └── Enums/                # MessageRole, ConversationStatus, AIModelType
├── Application/               # Use cases et services applicatifs
│   ├── Commands/             # SendMessageCommand, RateMessageCommand
│   ├── Queries/              # GetConversationQuery, GetMessagesQuery
│   ├── DTOs/                 # ChatRequestDto, ChatResponseDto
│   ├── Interfaces/           # IAIAssistantService, IConversationService
│   └── Validators/           # Validation FluentValidation
├── Infrastructure/            # Services d'infrastructure et IA
│   ├── Services/             # OpenAIAssistantService, MessageProcessingService
│   ├── Data/                 # Entity Framework DbContext
│   ├── Repositories/         # Implémentation des repositories
│   └── Integration/          # Intégration avec Legal Research
└── API/                      # Contrôleurs et configuration
    ├── Controllers/          # ChatController, ConversationController
    ├── Hubs/                # SignalR hubs pour temps réel
    └── Configuration/        # Configuration de l'API
```

## 🤖 Intelligence Artificielle

### Modèles utilisés
- **Chat** : OpenAI `gpt-4-turbo-preview` pour conversations
- **Analyse** : OpenAI `gpt-4` pour analyse de documents
- **Embeddings** : OpenAI `text-embedding-3-small` pour recherche sémantique
- **Modération** : OpenAI Moderation API pour filtrage de contenu

### Pipeline de traitement
1. **Content Moderation** : Vérification de la sécurité du contenu
2. **Intent Detection** : Classification de l'intention utilisateur
3. **Legal Research** : Recherche de documents pertinents si nécessaire
4. **Context Building** : Construction du contexte avec historique et sources
5. **AI Generation** : Génération de la réponse avec OpenAI
6. **Citation Extraction** : Extraction et formatage des citations
7. **Response Enhancement** : Ajout de questions de suivi et métadonnées

### Modes de conversation
- **Standard** : Conversation générale avec assistant juridique
- **Research** : Mode recherche approfondie avec sources multiples
- **Document Analysis** : Analyse spécialisée de documents
- **Legal Advice** : Conseil juridique avec avertissements appropriés
- **Quick Question** : Réponses rapides et concises

## 🚀 Démarrage Rapide

### Prérequis
- .NET 9 SDK
- PostgreSQL 16+ (stockage conversations)
- MongoDB 7+ (cache et métadonnées)
- Clé API OpenAI
- Service Legal Research en cours d'exécution
- Docker (optionnel)

### Installation

1. **Configurer les variables d'environnement**
```bash
# OpenAI
export OPENAI_API_KEY="your-openai-api-key"
export OPENAI_CHAT_MODEL="gpt-4-turbo-preview"

# Bases de données
export ConnectionStrings__PostgreSql="Host=localhost;Database=ai_assistant_db;Username=lexai_user;Password=lexai_password_2024!"
export ConnectionStrings__MongoDB="mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_conversations?authSource=admin"

# Services externes
export LegalResearch__BaseUrl="http://localhost:8082"
export LegalResearch__ApiKey="internal-service-key"
```

2. **Démarrer l'infrastructure**
```bash
# PostgreSQL
docker run -d --name postgres-ai \
  -e POSTGRES_DB=ai_assistant_db \
  -e POSTGRES_USER=lexai_user \
  -e POSTGRES_PASSWORD=lexai_password_2024! \
  -p 5434:5432 \
  postgres:16

# MongoDB
docker run -d --name mongodb-ai \
  -e MONGO_INITDB_ROOT_USERNAME=lexai_admin \
  -e MONGO_INITDB_ROOT_PASSWORD=lexai_mongo_password_2024! \
  -p 27019:27017 \
  mongo:7
```

3. **Appliquer les migrations**
```bash
.\scripts\manage-migrations.ps1 -Action add -Name "InitialCreate" -Service "AIAssistant"
.\scripts\manage-migrations.ps1 -Action update -Service "AIAssistant"
```

4. **Lancer le service**
```bash
dotnet run --project src/Services/AIAssistant/LexAI.AIAssistant.API
```

5. **Accéder à la documentation**
- API: http://localhost:8083
- Swagger: http://localhost:8083/swagger

## 📚 API Documentation

### Endpoints principaux

#### Chat et Conversations
- `POST /api/chat/message` - Envoyer un message à l'assistant
- `POST /api/chat/conversations/{id}/messages` - Continuer une conversation
- `POST /api/chat/messages/{id}/rate` - Noter une réponse

#### Analyse et Recherche
- `POST /api/chat/analyze-document` - Analyser un document juridique
- `POST /api/chat/legal-research` - Effectuer une recherche juridique
- `POST /api/chat/generate-document` - Générer un document juridique

#### Gestion des Conversations
- `GET /api/conversations` - Lister les conversations utilisateur
- `GET /api/conversations/{id}` - Détails d'une conversation
- `PUT /api/conversations/{id}` - Modifier une conversation
- `DELETE /api/conversations/{id}` - Supprimer une conversation

### Modèles de données

#### ChatRequestDto
```json
{
  "message": "Quelles sont les conditions de licenciement pour faute grave?",
  "userId": "guid",
  "sessionId": "string",
  "conversationId": "guid",
  "context": {
    "modelType": "GPT4Turbo",
    "mode": "Standard",
    "maxTokens": 4000,
    "temperature": 0.7,
    "includeLegalResearch": true,
    "includeCitations": true,
    "language": "fr",
    "jurisdiction": "France",
    "userRole": "Lawyer"
  },
  "includeLegalResearch": true,
  "includeCitations": true,
  "attachments": [],
  "metadata": {}
}
```

#### ChatResponseDto
```json
{
  "conversationId": "guid",
  "response": "Le licenciement pour faute grave est régi par l'article L1234-1 du Code du travail...",
  "messageId": "guid",
  "responseType": "Answer",
  "detectedIntent": "Advice",
  "detectedDomain": "Labor",
  "confidenceScore": 0.95,
  "citations": [
    {
      "id": "guid",
      "type": "LegalDocument",
      "title": "Code du travail - Article L1234-1",
      "url": "https://legifrance.gouv.fr/...",
      "source": "Légifrance",
      "publicationDate": "2008-05-01",
      "relevanceScore": 0.92,
      "excerpt": "Le licenciement pour faute grave..."
    }
  ],
  "relatedDocuments": [
    {
      "id": "guid",
      "title": "Jurisprudence Cour de Cassation - Faute grave",
      "summary": "Définition et critères de la faute grave...",
      "source": "Cour de Cassation",
      "relevanceScore": 0.88,
      "domain": "Labor"
    }
  ],
  "followUpQuestions": [
    "Quels sont les délais de préavis en cas de faute grave?",
    "Comment prouver une faute grave?",
    "Quelles sont les indemnités dues?"
  ],
  "processingTimeMs": 1250,
  "tokensUsed": 1850,
  "estimatedCost": 0.0185,
  "quality": "High",
  "isCached": false,
  "metadata": {
    "modelUsed": "gpt-4-turbo-preview",
    "researchPerformed": true,
    "citationsFound": 3
  }
}
```

## 🔧 Configuration

### appsettings.json
```json
{
  "ConnectionStrings": {
    "PostgreSql": "Host=localhost;Database=ai_assistant_db;Username=lexai_user;Password=lexai_password_2024!",
    "MongoDB": "mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_conversations?authSource=admin"
  },
  "OpenAI": {
    "ApiKey": "your-openai-api-key",
    "ChatModel": "gpt-4-turbo-preview",
    "EmbeddingModel": "text-embedding-3-small",
    "ModerationModel": "text-moderation-latest"
  },
  "LegalResearch": {
    "BaseUrl": "http://localhost:8082",
    "ApiKey": "internal-service-key",
    "TimeoutSeconds": 30
  },
  "Chat": {
    "MaxTokensPerMessage": 4000,
    "MaxMessagesPerConversation": 100,
    "DefaultTemperature": 0.7,
    "CacheExpirationMinutes": 30
  }
}
```

### Variables d'environnement
```bash
# OpenAI
OPENAI_API_KEY=your-openai-api-key
OPENAI_CHAT_MODEL=gpt-4-turbo-preview

# Bases de données
ConnectionStrings__PostgreSql=Host=localhost;Database=ai_assistant_db;Username=lexai_user;Password=lexai_password_2024!
ConnectionStrings__MongoDB=mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_conversations?authSource=admin

# Services
LegalResearch__BaseUrl=http://localhost:8082
LegalResearch__ApiKey=internal-service-key

# Chat
Chat__MaxTokensPerMessage=4000
Chat__DefaultTemperature=0.7
```

## 🧪 Tests

### Exécuter tous les tests
```bash
.\scripts\run-tests.ps1 -All -Project "AIAssistant"
```

### Tests unitaires
```bash
.\scripts\run-tests.ps1 -Unit -Project "AIAssistant"
```

### Tests d'intégration
```bash
.\scripts\run-tests.ps1 -Integration -Project "AIAssistant"
```

### Structure des tests
```
tests/
├── LexAI.AIAssistant.UnitTests/
│   ├── Domain/                    # Tests des entités et value objects
│   ├── Application/               # Tests des handlers et services
│   └── Infrastructure/            # Tests des services d'infrastructure
└── LexAI.AIAssistant.IntegrationTests/
    ├── Controllers/               # Tests des endpoints
    ├── Chat/                      # Tests de chat end-to-end
    └── Integration/               # Tests avec services externes
```

## 📊 Monitoring et Observabilité

### Métriques clés
- **Latence de réponse** - Temps de traitement des messages
- **Coût par conversation** - Coût OpenAI par interaction
- **Satisfaction utilisateur** - Score moyen des évaluations
- **Taux de succès** - Pourcentage de réponses réussies
- **Utilisation des modèles** - Distribution des modèles IA utilisés

### Health Checks
- `GET /health` - Santé du service
- Vérification PostgreSQL et MongoDB
- Test de connectivité OpenAI
- Validation du service Legal Research

### Logs structurés
- **Corrélation des conversations** avec ID unique
- **Métriques de coût** par utilisateur et conversation
- **Erreurs détaillées** avec contexte complet
- **Audit des interactions** pour amélioration

## 🔐 Sécurité

### Authentification
- **JWT Bearer tokens** requis pour tous les endpoints
- **Validation des rôles** (Lawyer, SeniorLawyer, Administrator)
- **Rate limiting** : 50 messages/minute par utilisateur

### Protection des données
- **Modération de contenu** automatique avec OpenAI
- **Anonymisation** des conversations dans les logs
- **Chiffrement** des données sensibles
- **Audit trail** complet des interactions

### Sécurité IA
- **Content filtering** pour prévenir les abus
- **Prompt injection protection** contre les attaques
- **Response validation** pour garantir la qualité
- **Cost monitoring** pour éviter les dépassements

## 🚀 Déploiement

### Docker
```bash
# Build de l'image
docker build -t lexai-ai-assistant:latest -f src/Services/AIAssistant/LexAI.AIAssistant.API/Dockerfile .

# Lancement du conteneur
docker run -p 8083:8083 \
  -e OPENAI_API_KEY=your-key \
  -e ConnectionStrings__PostgreSql=your-connection \
  lexai-ai-assistant:latest
```

### Docker Compose
```bash
# Démarrage complet avec infrastructure
docker-compose up -d ai-assistant-service
```

## 📈 Performance

### Optimisations implémentées
- **Cache intelligent** pour réponses fréquentes
- **Streaming responses** pour réponses longues
- **Connection pooling** optimisé
- **Async processing** pour toutes les opérations
- **Rate limiting** adaptatif

### Benchmarks
- **Chat simple** : ~1.5s (première fois), ~300ms (cache)
- **Analyse document** : ~3-8s selon la taille
- **Recherche juridique** : ~2-5s avec intégration
- **Throughput** : 100+ conversations simultanées

## 🤝 Contribution

### Standards de code
- **Clean Architecture** respectée
- **Tests obligatoires** (>85% couverture)
- **Documentation XML** complète
- **Validation** avec FluentValidation
- **Logging** structuré avec Serilog

### Workflow de développement
1. Créer une branche feature
2. Implémenter avec tests
3. Tester l'intégration IA
4. Vérifier les coûts OpenAI
5. Créer une Pull Request
6. Review et merge

## 📝 Changelog

### v1.0.0 (En cours)
- ✅ Chat IA conversationnel avec OpenAI GPT-4
- ✅ Analyse de documents juridiques
- ✅ Intégration avec Legal Research
- ✅ Génération de documents
- ✅ Gestion des conversations
- ✅ Modération de contenu
- ✅ Citations automatiques
- ✅ Tests complets et documentation

### Prochaines versions
- 🔄 Chat temps réel avec SignalR
- 🔄 Résumés automatiques de conversations
- 🔄 Templates de documents avancés
- 🔄 Support vocal et multimédia
