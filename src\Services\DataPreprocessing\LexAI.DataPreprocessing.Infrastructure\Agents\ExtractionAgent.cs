using LexAI.DataPreprocessing.Application.DTOs;
using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Domain.Entities;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace LexAI.DataPreprocessing.Infrastructure.Agents;

/// <summary>
/// Text extraction agent implementation
/// </summary>
public class ExtractionAgent : IExtractionAgent
{
    private readonly ITextExtractionService _textExtractionService;
    private readonly IFileStorageService _fileStorageService;
    private readonly ILogger<ExtractionAgent> _logger;

    /// <summary>
    /// Agent name
    /// </summary>
    public string AgentName => "ExtractionAgent";

    /// <summary>
    /// Agent type
    /// </summary>
    public AgentType AgentType => AgentType.Extraction;

    /// <summary>
    /// Supported MIME types
    /// </summary>
    public IEnumerable<string> SupportedMimeTypes => _textExtractionService.SupportedMimeTypes;

    /// <summary>
    /// Initializes a new instance of the ExtractionAgent
    /// </summary>
    /// <param name="textExtractionService">Text extraction service</param>
    /// <param name="fileStorageService">File storage service</param>
    /// <param name="logger">Logger</param>
    public ExtractionAgent(
        ITextExtractionService textExtractionService,
        IFileStorageService fileStorageService,
        ILogger<ExtractionAgent> logger)
    {
        _textExtractionService = textExtractionService;
        _fileStorageService = fileStorageService;
        _logger = logger;
    }

    /// <summary>
    /// Extracts text from a document
    /// </summary>
    /// <param name="document">Document to extract text from</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Extraction result</returns>
    public async Task<ExtractionResultDto> ExtractTextAsync(Document document, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting text extraction for document {DocumentId}: {FileName}", 
            document.Id, document.FileName);

        var stopwatch = Stopwatch.StartNew();
        var result = new ExtractionResultDto
        {
            AgentName = AgentName,
            Success = false
        };

        try
        {
            // Check if agent can handle this document type
            if (!CanHandle(document.MimeType))
            {
                var error = $"Unsupported MIME type: {document.MimeType}";
                result.Errors.Add(error);
                _logger.LogWarning("Cannot extract text from document {DocumentId}: {Error}", document.Id, error);
                return result;
            }

            // Retrieve file content
            var fileContent = await _fileStorageService.RetrieveFileAsync(document.StoragePath, cancellationToken);
            
            // Extract text using the text extraction service
            var extractionResult = await _textExtractionService.ExtractTextAsync(
                fileContent, 
                document.MimeType, 
                document.FileName, 
                cancellationToken);

            if (!extractionResult.Success)
            {
                result.Errors.AddRange(extractionResult.Errors);
                _logger.LogWarning("Text extraction failed for document {DocumentId}: {Errors}", 
                    document.Id, string.Join(", ", extractionResult.Errors));
                return result;
            }

            // Validate extracted text
            if (string.IsNullOrWhiteSpace(extractionResult.Text))
            {
                var error = "No text content extracted from document";
                result.Errors.Add(error);
                result.Warnings.Add("Document may be empty or contain only images");
                _logger.LogWarning("No text extracted from document {DocumentId}", document.Id);
                return result;
            }

            // Build extraction result
            result.ExtractedText = extractionResult.Text;
            result.Success = true;
            result.Confidence = CalculateExtractionConfidence(extractionResult);

            // Map metadata if available
            if (extractionResult.Metadata.Any())
            {
                result.Metadata = MapToDocumentMetadata(extractionResult.Metadata);
            }

            // Add quality warnings if needed
            AddQualityWarnings(result, extractionResult.Text);

            stopwatch.Stop();
            result.ExtractionTime = stopwatch.Elapsed;

            _logger.LogInformation("Text extraction completed for document {DocumentId}. " +
                "Length: {TextLength}, Confidence: {Confidence:F2}, Time: {Time}ms", 
                document.Id, result.ExtractedText.Length, result.Confidence, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            result.ExtractionTime = stopwatch.Elapsed;
            result.Errors.Add($"Extraction failed: {ex.Message}");
            
            _logger.LogError(ex, "Error extracting text from document {DocumentId}", document.Id);
            return result;
        }
    }

    /// <summary>
    /// Checks if the agent can handle the document
    /// </summary>
    /// <param name="mimeType">Document MIME type</param>
    /// <returns>True if agent can handle the document</returns>
    public bool CanHandle(string mimeType)
    {
        return _textExtractionService.CanHandle(mimeType);
    }

    /// <summary>
    /// Gets extraction confidence for a document
    /// </summary>
    /// <param name="document">Document to evaluate</param>
    /// <returns>Confidence score (0-1)</returns>
    public async Task<double> GetExtractionConfidenceAsync(Document document)
    {
        try
        {
            // Base confidence on MIME type support
            if (!CanHandle(document.MimeType))
                return 0.0;

            // Higher confidence for well-supported formats
            var confidence = document.MimeType.ToLowerInvariant() switch
            {
                "text/plain" => 0.95,
                "application/pdf" => 0.85,
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => 0.90,
                "application/msword" => 0.80,
                "text/html" => 0.75,
                "application/rtf" => 0.70,
                _ => 0.60
            };

            // Adjust based on file size (very small or very large files may have issues)
            if (document.FileSize < 1024) // Less than 1KB
                confidence *= 0.8;
            else if (document.FileSize > 50 * 1024 * 1024) // More than 50MB
                confidence *= 0.9;

            return Math.Max(0.0, Math.Min(1.0, confidence));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating extraction confidence for document {DocumentId}", document.Id);
            return 0.0;
        }
    }

    private static double CalculateExtractionConfidence(TextExtractionResult extractionResult)
    {
        var confidence = 0.5; // Base confidence

        // Higher confidence for longer text
        if (extractionResult.Text.Length > 1000)
            confidence += 0.2;
        else if (extractionResult.Text.Length > 100)
            confidence += 0.1;

        // Check for structured content indicators
        if (extractionResult.Text.Contains('\n') && extractionResult.Text.Contains('.'))
            confidence += 0.1;

        // Check for legal content indicators
        var legalIndicators = new[] { "article", "loi", "code", "tribunal", "contrat", "clause" };
        var indicatorCount = legalIndicators.Count(indicator => 
            extractionResult.Text.ToLowerInvariant().Contains(indicator));
        
        confidence += indicatorCount * 0.05;

        // Penalize if too many extraction errors
        if (extractionResult.Errors.Count > 0)
            confidence -= extractionResult.Errors.Count * 0.1;

        return Math.Max(0.0, Math.Min(1.0, confidence));
    }

    private static DocumentMetadataDto? MapToDocumentMetadata(Dictionary<string, object> metadata)
    {
        if (!metadata.Any())
            return null;

        var dto = new DocumentMetadataDto();

        if (metadata.TryGetValue("title", out var title) && title is string titleStr)
            dto.Title = titleStr;

        if (metadata.TryGetValue("author", out var author) && author is string authorStr)
            dto.Author = authorStr;

        if (metadata.TryGetValue("language", out var language) && language is string languageStr)
            dto.Language = languageStr;

        if (metadata.TryGetValue("createdAt", out var createdAt) && createdAt is DateTime createdAtDate)
            dto.CreatedAt = createdAtDate;

        if (metadata.TryGetValue("modifiedAt", out var modifiedAt) && modifiedAt is DateTime modifiedAtDate)
            dto.ModifiedAt = modifiedAtDate;

        if (metadata.TryGetValue("pageCount", out var pageCount) && pageCount is int pageCountInt)
            dto.PageCount = pageCountInt;

        if (metadata.TryGetValue("wordCount", out var wordCount) && wordCount is int wordCountInt)
            dto.WordCount = wordCountInt;

        if (metadata.TryGetValue("characterCount", out var charCount) && charCount is int charCountInt)
            dto.CharacterCount = charCountInt;

        // Add remaining metadata
        foreach (var kvp in metadata.Where(m => !IsStandardMetadata(m.Key)))
        {
            dto.AdditionalMetadata[kvp.Key] = kvp.Value;
        }

        return dto;
    }

    private static bool IsStandardMetadata(string key)
    {
        var standardKeys = new[] { "title", "author", "language", "createdAt", "modifiedAt", "pageCount", "wordCount", "characterCount" };
        return standardKeys.Contains(key, StringComparer.OrdinalIgnoreCase);
    }

    private static void AddQualityWarnings(ExtractionResultDto result, string extractedText)
    {
        // Check for potential quality issues
        if (extractedText.Length < 100)
        {
            result.Warnings.Add("Extracted text is very short, document may be mostly images or empty");
        }

        // Check for encoding issues
        if (extractedText.Contains("�") || extractedText.Contains("?"))
        {
            result.Warnings.Add("Possible character encoding issues detected");
        }

        // Check for OCR artifacts
        var ocrArtifacts = new[] { "|||", "___", "...", "   " };
        if (ocrArtifacts.Any(artifact => extractedText.Contains(artifact)))
        {
            result.Warnings.Add("Possible OCR artifacts detected, text may need manual review");
        }

        // Check for incomplete sentences
        var sentences = extractedText.Split('.', '!', '?').Where(s => !string.IsNullOrWhiteSpace(s)).ToArray();
        var incompleteSentences = sentences.Count(s => s.Trim().Length < 10);
        
        if (sentences.Length > 0 && (double)incompleteSentences / sentences.Length > 0.3)
        {
            result.Warnings.Add("High number of incomplete sentences detected");
        }

        // Check for mixed languages (basic check)
        var frenchWords = new[] { "le", "la", "les", "de", "du", "des", "et", "ou", "dans", "pour", "avec", "sur" };
        var englishWords = new[] { "the", "and", "or", "in", "for", "with", "on", "at", "by", "from", "to" };

        var lowerText = extractedText.ToLowerInvariant();
        var frenchCount = frenchWords.Count(word => lowerText.Contains($" {word} "));
        var englishCount = englishWords.Count(word => lowerText.Contains($" {word} "));

        if (frenchCount > 0 && englishCount > 0 && Math.Abs(frenchCount - englishCount) < 3)
        {
            result.Warnings.Add("Mixed language content detected");
        }
    }
}
