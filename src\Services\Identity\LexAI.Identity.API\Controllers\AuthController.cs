using LexAI.Identity.Application.Commands;
using LexAI.Identity.Application.DTOs;
using LexAI.Identity.Application.Queries;
using LexAI.Shared.Domain.Exceptions;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace LexAI.Identity.API.Controllers;

/// <summary>
/// Controller for authentication operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class AuthController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<AuthController> _logger;

    /// <summary>
    /// Initializes a new instance of the AuthController
    /// </summary>
    /// <param name="mediator">MediatR mediator</param>
    /// <param name="logger">Logger</param>
    public AuthController(IMediator mediator, ILogger<AuthController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Authenticates a user and returns access and refresh tokens
    /// </summary>
    /// <param name="loginDto">Login credentials</param>
    /// <returns>Authentication response with tokens and user information</returns>
    /// <response code="200">Login successful</response>
    /// <response code="400">Invalid credentials or validation errors</response>
    /// <response code="401">Authentication failed</response>
    /// <response code="423">Account is locked</response>
    [HttpPost("login")]
    [ProducesResponseType(typeof(AuthenticationResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status423Locked)]
    public async Task<ActionResult<AuthenticationResponseDto>> Login([FromBody] LoginDto loginDto)
    {
        _logger.LogInformation("Login attempt for email {Email}", loginDto.Email);

        var command = new LoginCommand
        {
            Email = loginDto.Email,
            Password = loginDto.Password,
            RememberMe = loginDto.RememberMe,
            IpAddress = GetClientIpAddress(),
            UserAgent = Request.Headers.UserAgent.ToString()
        };

        try
        {
            var result = await _mediator.Send(command);

            _logger.LogInformation("Login successful for email {Email}", loginDto.Email);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Login failed for email {Email}", loginDto.Email);

            return ex.Message.Contains("locked")
                ? StatusCode(423, new ProblemDetails { Title = "Account Locked", Detail = ex.Message })
                : Unauthorized(new ProblemDetails { Title = "Authentication Failed", Detail = "Invalid email or password" });
        }
    }

    /// <summary>
    /// Refreshes an access token using a refresh token
    /// </summary>
    /// <param name="refreshTokenDto">Refresh token request</param>
    /// <returns>New authentication response with fresh tokens</returns>
    /// <response code="200">Token refresh successful</response>
    /// <response code="400">Invalid refresh token</response>
    /// <response code="401">Refresh token expired or invalid</response>
    [HttpPost("refresh")]
    [ProducesResponseType(typeof(AuthenticationResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<AuthenticationResponseDto>> RefreshToken([FromBody] RefreshTokenDto refreshTokenDto)
    {
        _logger.LogInformation("Token refresh attempt");

        var command = new RefreshTokenCommand
        {
            RefreshToken = refreshTokenDto.RefreshToken,
            IpAddress = GetClientIpAddress(),
            UserAgent = Request.Headers.UserAgent.ToString()
        };

        try
        {
            var result = await _mediator.Send(command);

            _logger.LogInformation("Token refresh successful");
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Token refresh failed");
            return Unauthorized(new ProblemDetails { Title = "Token Refresh Failed", Detail = ex.Message });
        }
    }

    /// <summary>
    /// Logs out a user by revoking their refresh token(s)
    /// </summary>
    /// <param name="revokeAllTokens">Whether to revoke all user tokens or just the current one</param>
    /// <returns>Logout confirmation</returns>
    /// <response code="200">Logout successful</response>
    /// <response code="401">User not authenticated</response>
    [HttpPost("logout")]
    [Authorize]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult> Logout([FromQuery] bool revokeAllTokens = false)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        _logger.LogInformation("Logout attempt for user {UserId}", userId.Value);

        var command = new LogoutCommand
        {
            UserId = userId.Value,
            RevokeAllTokens = revokeAllTokens
        };

        try
        {
            var result = await _mediator.Send(command);

            _logger.LogInformation("Logout successful for user {UserId}", userId.Value);
            return Ok(new { message = "Logout successful", revokedAllTokens = revokeAllTokens });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Logout failed for user {UserId}", userId.Value);
            return Ok(new { message = "Logout completed with warnings" });
        }
    }

    /// <summary>
    /// Gets the current authenticated user's information
    /// </summary>
    /// <returns>Current user information</returns>
    /// <response code="200">User information retrieved successfully</response>
    /// <response code="401">User not authenticated</response>
    [HttpGet("me")]
    [Authorize]
    [ProducesResponseType(typeof(UserDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<UserDto>> GetCurrentUser()
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        _logger.LogInformation("Getting current user information for {UserId}", userId.Value);

        try
        {
            var query = new GetUserByIdQuery(userId.Value);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get current user information for {UserId}", userId.Value);
            return NotFound(new ProblemDetails { Title = "User Not Found", Detail = "Current user information not found" });
        }
    }

    /// <summary>
    /// Changes the current user's password
    /// </summary>
    /// <param name="changePasswordDto">Password change request</param>
    /// <returns>Password change confirmation</returns>
    /// <response code="200">Password changed successfully</response>
    /// <response code="400">Invalid current password or validation errors</response>
    /// <response code="401">User not authenticated</response>
    [HttpPost("change-password")]
    [Authorize]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult> ChangePassword([FromBody] ChangePasswordDto changePasswordDto)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (changePasswordDto.NewPassword != changePasswordDto.ConfirmPassword)
        {
            return BadRequest(new ProblemDetails { Title = "Validation Error", Detail = "New password and confirmation do not match" });
        }

        _logger.LogInformation("Password change attempt for user {UserId}", userId.Value);

        var command = new ChangePasswordCommand
        {
            UserId = userId.Value,
            CurrentPassword = changePasswordDto.CurrentPassword,
            NewPassword = changePasswordDto.NewPassword,
            ChangedBy = userId.Value.ToString()
        };

        try
        {
            var result = await _mediator.Send(command);

            _logger.LogInformation("Password changed successfully for user {UserId}", userId.Value);
            return Ok(new { message = "Password changed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Password change failed for user {UserId}", userId.Value);
            return BadRequest(new ProblemDetails { Title = "Password Change Failed", Detail = ex.Message });
        }
    }

    /// <summary>
    /// Initiates a password reset process
    /// </summary>
    /// <param name="forgotPasswordDto">Forgot password request</param>
    /// <returns>Password reset initiation confirmation</returns>
    /// <response code="200">Password reset email sent (if email exists)</response>
    /// <response code="400">Invalid email format</response>
    [HttpPost("forgot-password")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> ForgotPassword([FromBody] ForgotPasswordDto forgotPasswordDto)
    {
        _logger.LogInformation("Password reset request for email {Email}", forgotPasswordDto.Email);

        // Always return success for security reasons (don't reveal if email exists)
        return Ok(new { message = "If the email address exists in our system, you will receive a password reset link." });
    }

    /// <summary>
    /// Resets a user's password using a reset token
    /// </summary>
    /// <param name="resetPasswordDto">Password reset request</param>
    /// <returns>Password reset confirmation</returns>
    /// <response code="200">Password reset successfully</response>
    /// <response code="400">Invalid token or validation errors</response>
    [HttpPost("reset-password")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> ResetPassword([FromBody] ResetPasswordDto resetPasswordDto)
    {
        if (resetPasswordDto.NewPassword != resetPasswordDto.ConfirmPassword)
        {
            return BadRequest(new ProblemDetails { Title = "Validation Error", Detail = "New password and confirmation do not match" });
        }

        _logger.LogInformation("Password reset attempt for email {Email}", resetPasswordDto.Email);

        // Implementation would go here
        return Ok(new { message = "Password reset successfully" });
    }

    /// <summary>
    /// Registers a new user account (public registration)
    /// </summary>
    /// <param name="registerDto">User registration data</param>
    /// <returns>Created user information</returns>
    /// <response code="201">User registered successfully</response>
    /// <response code="400">Validation errors or user already exists</response>
    /// <response code="409">User with email already exists</response>
    [HttpPost("register")]
    [ProducesResponseType(typeof(UserDto), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status409Conflict)]
    public async Task<ActionResult<UserDto>> Register([FromBody] RegisterUserDto registerDto)
    {
        _logger.LogInformation("Registration attempt for email {Email}", registerDto.Email);

        // Validate password confirmation
        if (registerDto.Password != registerDto.ConfirmPassword)
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Validation Error",
                Detail = "Password and confirmation password do not match"
            });
        }

        var command = new RegisterUserCommand
        {
            Email = registerDto.Email,
            FirstName = registerDto.FirstName,
            LastName = registerDto.LastName,
            PhoneNumber = registerDto.PhoneNumber,
            Password = registerDto.Password,
            Role = registerDto.Role,
            PreferredLanguage = registerDto.PreferredLanguage,
            TimeZone = registerDto.TimeZone,
            AcceptTerms = registerDto.AcceptTerms,
            AcceptPrivacyPolicy = registerDto.AcceptPrivacyPolicy,
            IpAddress = GetClientIpAddress(),
            UserAgent = Request.Headers.UserAgent.ToString()
        };

        try
        {
            var result = await _mediator.Send(command);

            _logger.LogInformation("User registered successfully with email {Email} and role {Role}",
                registerDto.Email, registerDto.Role);

            return CreatedAtAction(
                nameof(GetCurrentUser),
                new { id = result.Id },
                result);
        }
        catch (EntityAlreadyExistsException)
        {
            _logger.LogWarning("Registration failed - user already exists with email {Email}", registerDto.Email);
            return Conflict(new ProblemDetails
            {
                Title = "User Already Exists",
                Detail = "A user with this email address already exists"
            });
        }
        catch (LexAI.Shared.Domain.Exceptions.InvalidDataException ex)
        {
            _logger.LogWarning(ex, "Registration failed - validation error for email {Email}", registerDto.Email);
            return BadRequest(new ProblemDetails
            {
                Title = "Validation Error",
                Detail = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Registration failed for email {Email}", registerDto.Email);
            return BadRequest(new ProblemDetails
            {
                Title = "Registration Failed",
                Detail = "An error occurred during registration. Please try again."
            });
        }
    }

    private Guid? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }

    private string? GetClientIpAddress()
    {
        return Request.Headers.ContainsKey("X-Forwarded-For")
            ? Request.Headers["X-Forwarded-For"].FirstOrDefault()?.Split(',').FirstOrDefault()?.Trim()
            : HttpContext.Connection.RemoteIpAddress?.ToString();
    }
}
