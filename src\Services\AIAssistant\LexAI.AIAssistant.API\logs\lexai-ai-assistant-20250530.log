2025-05-30 02:08:17.508 +04:00 [FTL] LexAI AI Assistant Service failed to start
System.IO.FileNotFoundException: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.ConfigurationManager.AddSource(IConfigurationSource source)
   at Microsoft.Extensions.Configuration.ConfigurationManager.Microsoft.Extensions.Configuration.IConfigurationBuilder.Add(IConfigurationSource source)
   at Microsoft.Extensions.Configuration.JsonConfigurationExtensions.AddJsonFile(IConfigurationBuilder builder, String path, Boolean optional, Boolean reloadOnChange)
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Program.cs:line 29
2025-05-30 02:15:49.561 +04:00 [FTL] LexAI AI Assistant Service failed to start
System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: MediatR.IRequestHandler`2[LexAI.AIAssistant.Application.Commands.SendMessageCommand,LexAI.AIAssistant.Application.DTOs.ChatResponseDto] Lifetime: Transient ImplementationType: LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler': Unable to resolve service for type 'LexAI.AIAssistant.Application.Interfaces.ILegalResearchIntegrationService' while attempting to activate 'LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService'.) (Error while validating the service descriptor 'ServiceType: MediatR.IRequestHandler`2[LexAI.AIAssistant.Application.Commands.RateMessageCommand,System.Boolean] Lifetime: Transient ImplementationType: LexAI.AIAssistant.Application.Commands.RateMessageCommandHandler': Unable to resolve service for type 'LexAI.AIAssistant.Application.Commands.IConversationRepository' while attempting to activate 'LexAI.AIAssistant.Application.Commands.RateMessageCommandHandler'.) (Error while validating the service descriptor 'ServiceType: LexAI.AIAssistant.Application.Interfaces.IAIAssistantService Lifetime: Scoped ImplementationType: LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService': Unable to resolve service for type 'LexAI.AIAssistant.Application.Interfaces.ILegalResearchIntegrationService' while attempting to activate 'LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService'.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: MediatR.IRequestHandler`2[LexAI.AIAssistant.Application.Commands.SendMessageCommand,LexAI.AIAssistant.Application.DTOs.ChatResponseDto] Lifetime: Transient ImplementationType: LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler': Unable to resolve service for type 'LexAI.AIAssistant.Application.Interfaces.ILegalResearchIntegrationService' while attempting to activate 'LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'LexAI.AIAssistant.Application.Interfaces.ILegalResearchIntegrationService' while attempting to activate 'LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Program.cs:line 208
 ---> (Inner Exception #1) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: MediatR.IRequestHandler`2[LexAI.AIAssistant.Application.Commands.RateMessageCommand,System.Boolean] Lifetime: Transient ImplementationType: LexAI.AIAssistant.Application.Commands.RateMessageCommandHandler': Unable to resolve service for type 'LexAI.AIAssistant.Application.Commands.IConversationRepository' while attempting to activate 'LexAI.AIAssistant.Application.Commands.RateMessageCommandHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'LexAI.AIAssistant.Application.Commands.IConversationRepository' while attempting to activate 'LexAI.AIAssistant.Application.Commands.RateMessageCommandHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #2) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: LexAI.AIAssistant.Application.Interfaces.IAIAssistantService Lifetime: Scoped ImplementationType: LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService': Unable to resolve service for type 'LexAI.AIAssistant.Application.Interfaces.ILegalResearchIntegrationService' while attempting to activate 'LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'LexAI.AIAssistant.Application.Interfaces.ILegalResearchIntegrationService' while attempting to activate 'LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

