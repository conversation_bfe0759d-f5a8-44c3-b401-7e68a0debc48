using LexAI.Shared.Domain.Common;

namespace LexAI.AIAssistant.Domain.ValueObjects;

/// <summary>
/// Represents an attachment associated with a message
/// </summary>
public class MessageAttachment : ValueObject
{
    /// <summary>
    /// Attachment ID
    /// </summary>
    public Guid Id { get; private set; }

    /// <summary>
    /// Original file name
    /// </summary>
    public string FileName { get; private set; } = string.Empty;

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long FileSize { get; private set; }

    /// <summary>
    /// MIME type of the file
    /// </summary>
    public string MimeType { get; private set; } = string.Empty;

    /// <summary>
    /// Attachment type
    /// </summary>
    public AttachmentType Type { get; private set; }

    /// <summary>
    /// File URL or storage path
    /// </summary>
    public string Url { get; private set; } = string.Empty;

    /// <summary>
    /// File content (for small files or text content)
    /// </summary>
    public string? Content { get; private set; }

    /// <summary>
    /// File hash for integrity verification
    /// </summary>
    public string? FileHash { get; private set; }

    /// <summary>
    /// Thumbnail URL for images/videos
    /// </summary>
    public string? ThumbnailUrl { get; private set; }

    /// <summary>
    /// File description or alt text
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// Whether the file has been processed
    /// </summary>
    public bool IsProcessed { get; private set; }

    /// <summary>
    /// Processing status message
    /// </summary>
    public string? ProcessingStatus { get; private set; }

    /// <summary>
    /// Extracted text content (for documents)
    /// </summary>
    public string? ExtractedText { get; private set; }

    /// <summary>
    /// File metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; private set; } = new();

    /// <summary>
    /// Upload timestamp
    /// </summary>
    public DateTime UploadedAt { get; private set; }

    /// <summary>
    /// User who uploaded the file
    /// </summary>
    public string UploadedBy { get; private set; } = string.Empty;

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private MessageAttachment() { }

    /// <summary>
    /// Creates a new message attachment
    /// </summary>
    /// <param name="fileName">Original file name</param>
    /// <param name="fileSize">File size in bytes</param>
    /// <param name="mimeType">MIME type</param>
    /// <param name="url">File URL or path</param>
    /// <param name="uploadedBy">User who uploaded the file</param>
    /// <returns>New message attachment</returns>
    public static MessageAttachment Create(
        string fileName,
        long fileSize,
        string mimeType,
        string url,
        string uploadedBy)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("File name cannot be empty", nameof(fileName));

        if (fileSize <= 0)
            throw new ArgumentException("File size must be positive", nameof(fileSize));

        if (string.IsNullOrWhiteSpace(mimeType))
            throw new ArgumentException("MIME type cannot be empty", nameof(mimeType));

        if (string.IsNullOrWhiteSpace(url))
            throw new ArgumentException("URL cannot be empty", nameof(url));

        if (string.IsNullOrWhiteSpace(uploadedBy))
            throw new ArgumentException("Uploaded by cannot be empty", nameof(uploadedBy));

        return new MessageAttachment
        {
            Id = Guid.NewGuid(),
            FileName = fileName.Trim(),
            FileSize = fileSize,
            MimeType = mimeType.Trim().ToLowerInvariant(),
            Type = DetermineAttachmentType(mimeType),
            Url = url.Trim(),
            UploadedAt = DateTime.UtcNow,
            UploadedBy = uploadedBy.Trim(),
            Metadata = new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// Creates an attachment with content
    /// </summary>
    /// <param name="fileName">Original file name</param>
    /// <param name="content">File content</param>
    /// <param name="mimeType">MIME type</param>
    /// <param name="uploadedBy">User who uploaded the file</param>
    /// <returns>New message attachment</returns>
    public static MessageAttachment CreateWithContent(
        string fileName,
        string content,
        string mimeType,
        string uploadedBy)
    {
        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));

        var attachment = Create(fileName, content.Length, mimeType, string.Empty, uploadedBy);
        attachment.Content = content;
        return attachment;
    }

    /// <summary>
    /// Sets the file hash
    /// </summary>
    /// <param name="hash">File hash</param>
    public void SetFileHash(string hash)
    {
        FileHash = hash?.Trim();
    }

    /// <summary>
    /// Sets the thumbnail URL
    /// </summary>
    /// <param name="thumbnailUrl">Thumbnail URL</param>
    public void SetThumbnailUrl(string thumbnailUrl)
    {
        ThumbnailUrl = thumbnailUrl?.Trim();
    }

    /// <summary>
    /// Sets the description
    /// </summary>
    /// <param name="description">File description</param>
    public void SetDescription(string description)
    {
        Description = description?.Trim();
    }

    /// <summary>
    /// Marks the file as processed
    /// </summary>
    /// <param name="status">Processing status</param>
    /// <param name="extractedText">Extracted text content</param>
    public void MarkAsProcessed(string? status = null, string? extractedText = null)
    {
        IsProcessed = true;
        ProcessingStatus = status?.Trim();
        ExtractedText = extractedText?.Trim();
    }

    /// <summary>
    /// Marks the file processing as failed
    /// </summary>
    /// <param name="errorMessage">Error message</param>
    public void MarkProcessingFailed(string errorMessage)
    {
        IsProcessed = false;
        ProcessingStatus = $"Failed: {errorMessage?.Trim()}";
    }

    /// <summary>
    /// Adds metadata
    /// </summary>
    /// <param name="key">Metadata key</param>
    /// <param name="value">Metadata value</param>
    public void AddMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Metadata key cannot be empty", nameof(key));

        Metadata[key] = value;
    }

    /// <summary>
    /// Gets metadata value
    /// </summary>
    /// <typeparam name="T">Value type</typeparam>
    /// <param name="key">Metadata key</param>
    /// <returns>Metadata value or default</returns>
    public T? GetMetadata<T>(string key)
    {
        if (string.IsNullOrWhiteSpace(key) || !Metadata.ContainsKey(key))
            return default;

        try
        {
            return (T)Metadata[key];
        }
        catch
        {
            return default;
        }
    }

    /// <summary>
    /// Checks if the attachment is an image
    /// </summary>
    /// <returns>True if image</returns>
    public bool IsImage()
    {
        return Type == AttachmentType.Image || MimeType.StartsWith("image/");
    }

    /// <summary>
    /// Checks if the attachment is a document
    /// </summary>
    /// <returns>True if document</returns>
    public bool IsDocument()
    {
        return Type == AttachmentType.PDF ||
               Type == AttachmentType.Word ||
               Type == AttachmentType.Text ||
               Type == AttachmentType.Spreadsheet;
    }

    /// <summary>
    /// Gets the file extension
    /// </summary>
    /// <returns>File extension</returns>
    public string GetFileExtension()
    {
        return Path.GetExtension(FileName).ToLowerInvariant();
    }

    /// <summary>
    /// Gets human-readable file size
    /// </summary>
    /// <returns>Formatted file size</returns>
    public string GetFormattedFileSize()
    {
        const long KB = 1024;
        const long MB = KB * 1024;
        const long GB = MB * 1024;

        if (FileSize >= GB)
            return $"{FileSize / (double)GB:F2} GB";

        if (FileSize >= MB)
            return $"{FileSize / (double)MB:F2} MB";

        if (FileSize >= KB)
            return $"{FileSize / (double)KB:F2} KB";

        return $"{FileSize} bytes";
    }

    /// <summary>
    /// Determines attachment type from MIME type
    /// </summary>
    /// <param name="mimeType">MIME type</param>
    /// <returns>Attachment type</returns>
    private static AttachmentType DetermineAttachmentType(string mimeType)
    {
        var mime = mimeType.ToLowerInvariant();

        if (mime.StartsWith("image/"))
            return AttachmentType.Image;

        if (mime == "application/pdf")
            return AttachmentType.PDF;

        if (mime.Contains("word") || mime.Contains("msword") ||
            mime == "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
            return AttachmentType.Word;

        if (mime.StartsWith("text/"))
            return AttachmentType.Text;

        if (mime.Contains("sheet") || mime.Contains("excel") ||
            mime == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            return AttachmentType.Spreadsheet;

        return AttachmentType.Other;
    }

    /// <summary>
    /// Gets the atomic values for value object equality
    /// </summary>
    /// <returns>Atomic values</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Id;
        yield return FileName;
        yield return FileSize;
        yield return MimeType;
        yield return Url;
    }
}
