using LexAI.LegalResearch.Application.Commands;
using LexAI.LegalResearch.Application.DTOs;
using LexAI.LegalResearch.Application.Interfaces;
using LexAI.LegalResearch.Domain.ValueObjects;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using System.Security.Claims;

namespace LexAI.LegalResearch.API.Controllers;

/// <summary>
/// Controller for legal research search operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[Produces("application/json")]
[EnableRateLimiting("SearchPolicy")]
public class SearchController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILegalSearchService _searchService;
    private readonly ILogger<SearchController> _logger;

    /// <summary>
    /// Initializes a new instance of the SearchController
    /// </summary>
    /// <param name="mediator">MediatR mediator</param>
    /// <param name="searchService">Legal search service</param>
    /// <param name="logger">Logger</param>
    public SearchController(
        IMediator mediator,
        ILegalSearchService searchService,
        ILogger<SearchController> logger)
    {
        _mediator = mediator;
        _searchService = searchService;
        _logger = logger;
    }

    /// <summary>
    /// Performs a legal research search
    /// </summary>
    /// <param name="request">Search request</param>
    /// <returns>Search results</returns>
    /// <response code="200">Search completed successfully</response>
    /// <response code="400">Invalid search request</response>
    /// <response code="401">User not authenticated</response>
    /// <response code="429">Rate limit exceeded</response>
    [HttpPost("search")]
    [ProducesResponseType(typeof(SearchResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status429TooManyRequests)]
    public async Task<ActionResult<SearchResponseDto>> Search([FromBody] SearchRequestDto request)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (string.IsNullOrWhiteSpace(request.Query))
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Search query cannot be empty" });
        }

        _logger.LogInformation("Search request from user {UserId}: {Query}", userId.Value, request.Query);

        try
        {
            // Set user context
            request.UserId = userId.Value;
            if (string.IsNullOrEmpty(request.SessionId))
            {
                request.SessionId = HttpContext.TraceIdentifier;
            }

            // Execute search command
            var command = new PerformSearchCommand { Request = request };
            var response = await _mediator.Send(command);

            _logger.LogInformation("Search completed for user {UserId}. Results: {ResultCount}, Time: {ExecutionTime}ms",
                userId.Value, response.TotalResults, response.ExecutionTimeMs);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid search request from user {UserId}", userId.Value);
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing search for user {UserId}", userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Search Error", Detail = "An error occurred while performing the search" });
        }
    }

    /// <summary>
    /// Performs a hybrid search combining semantic and keyword search
    /// </summary>
    /// <param name="request">Search request</param>
    /// <returns>Hybrid search results</returns>
    /// <response code="200">Hybrid search completed successfully</response>
    /// <response code="400">Invalid search request</response>
    /// <response code="401">User not authenticated</response>
    [HttpPost("hybrid-search")]
    [ProducesResponseType(typeof(SearchResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<SearchResponseDto>> HybridSearch([FromBody] SearchRequestDto request)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (string.IsNullOrWhiteSpace(request.Query))
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Search query cannot be empty" });
        }

        _logger.LogInformation("Hybrid search request from user {UserId}: {Query}", userId.Value, request.Query);

        try
        {
            // Set user context and method
            request.UserId = userId.Value;
            request.Method = SearchMethod.Hybrid;
            if (string.IsNullOrEmpty(request.SessionId))
            {
                request.SessionId = HttpContext.TraceIdentifier;
            }

            var response = await _searchService.HybridSearchAsync(request);

            _logger.LogInformation("Hybrid search completed for user {UserId}. Results: {ResultCount}, Time: {ExecutionTime}ms",
                userId.Value, response.TotalResults, response.ExecutionTimeMs);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid hybrid search request from user {UserId}", userId.Value);
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing hybrid search for user {UserId}", userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Search Error", Detail = "An error occurred while performing the hybrid search" });
        }
    }

    /// <summary>
    /// Finds documents similar to a given document
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="limit">Maximum number of similar documents (default: 10)</param>
    /// <returns>Similar documents</returns>
    /// <response code="200">Similar documents found</response>
    /// <response code="404">Document not found</response>
    /// <response code="401">User not authenticated</response>
    [HttpGet("similar/{documentId:guid}")]
    [ProducesResponseType(typeof(IEnumerable<SearchResultDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<IEnumerable<SearchResultDto>>> FindSimilarDocuments(
        Guid documentId,
        [FromQuery] int limit = 10)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (limit <= 0 || limit > 50)
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Limit must be between 1 and 50" });
        }

        _logger.LogInformation("Finding similar documents for document {DocumentId} by user {UserId}", documentId, userId.Value);

        try
        {
            var similarDocuments = await _searchService.FindSimilarDocumentsAsync(documentId, limit);

            _logger.LogInformation("Found {Count} similar documents for document {DocumentId}", 
                similarDocuments.Count(), documentId);

            return Ok(similarDocuments);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finding similar documents for document {DocumentId}", documentId);
            return StatusCode(500, new ProblemDetails { Title = "Search Error", Detail = "An error occurred while finding similar documents" });
        }
    }

    /// <summary>
    /// Gets search suggestions based on partial query
    /// </summary>
    /// <param name="q">Partial query text</param>
    /// <param name="limit">Maximum number of suggestions (default: 10)</param>
    /// <returns>Search suggestions</returns>
    /// <response code="200">Suggestions retrieved successfully</response>
    /// <response code="400">Invalid request</response>
    /// <response code="401">User not authenticated</response>
    [HttpGet("suggestions")]
    [ProducesResponseType(typeof(IEnumerable<string>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<IEnumerable<string>>> GetSearchSuggestions(
        [FromQuery] string q,
        [FromQuery] int limit = 10)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (string.IsNullOrWhiteSpace(q) || q.Length < 2)
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Query must be at least 2 characters long" });
        }

        if (limit <= 0 || limit > 20)
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Limit must be between 1 and 20" });
        }

        _logger.LogDebug("Getting search suggestions for partial query: {PartialQuery}", q);

        try
        {
            var suggestions = await _searchService.GetSearchSuggestionsAsync(q, limit);
            return Ok(suggestions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting search suggestions for partial query: {PartialQuery}", q);
            return StatusCode(500, new ProblemDetails { Title = "Suggestion Error", Detail = "An error occurred while getting search suggestions" });
        }
    }

    /// <summary>
    /// Analyzes a search query to extract intent and entities
    /// </summary>
    /// <param name="query">Query text to analyze</param>
    /// <returns>Query analysis result</returns>
    /// <response code="200">Query analyzed successfully</response>
    /// <response code="400">Invalid query</response>
    /// <response code="401">User not authenticated</response>
    [HttpPost("analyze")]
    [ProducesResponseType(typeof(QueryAnalysisDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<QueryAnalysisDto>> AnalyzeQuery([FromBody] string query)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (string.IsNullOrWhiteSpace(query))
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Query cannot be empty" });
        }

        _logger.LogDebug("Analyzing query: {Query}", query);

        try
        {
            var analysis = await _searchService.AnalyzeQueryAsync(query);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing query: {Query}", query);
            return StatusCode(500, new ProblemDetails { Title = "Analysis Error", Detail = "An error occurred while analyzing the query" });
        }
    }

    /// <summary>
    /// Provides feedback on search results
    /// </summary>
    /// <param name="queryId">Search query ID</param>
    /// <param name="feedback">User feedback</param>
    /// <returns>Feedback confirmation</returns>
    /// <response code="200">Feedback recorded successfully</response>
    /// <response code="400">Invalid feedback</response>
    /// <response code="404">Query not found</response>
    /// <response code="401">User not authenticated</response>
    [HttpPost("feedback/{queryId:guid}")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult> ProvideFeedback(Guid queryId, [FromBody] UserFeedbackDto feedback)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (feedback == null)
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Feedback cannot be null" });
        }

        _logger.LogInformation("Recording feedback for query {QueryId} from user {UserId}", queryId, userId.Value);

        try
        {
            var command = new ProvideSearchFeedbackCommand
            {
                QueryId = queryId,
                Feedback = feedback
            };

            var result = await _mediator.Send(command);

            if (result)
            {
                _logger.LogInformation("Feedback recorded successfully for query {QueryId}", queryId);
                return Ok(new { message = "Feedback recorded successfully" });
            }
            else
            {
                return NotFound(new ProblemDetails { Title = "Query Not Found", Detail = "The specified search query was not found" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording feedback for query {QueryId}", queryId);
            return StatusCode(500, new ProblemDetails { Title = "Feedback Error", Detail = "An error occurred while recording feedback" });
        }
    }

    /// <summary>
    /// Gets search analytics for the current user
    /// </summary>
    /// <param name="sessionId">Optional session ID filter</param>
    /// <returns>Search analytics</returns>
    /// <response code="200">Analytics retrieved successfully</response>
    /// <response code="401">User not authenticated</response>
    [HttpGet("analytics")]
    [ProducesResponseType(typeof(SearchAnalyticsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<SearchAnalyticsDto>> GetSearchAnalytics([FromQuery] string? sessionId = null)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        _logger.LogDebug("Getting search analytics for user {UserId}", userId.Value);

        try
        {
            var analytics = await _searchService.GetSearchAnalyticsAsync(userId.Value, sessionId);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting search analytics for user {UserId}", userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Analytics Error", Detail = "An error occurred while retrieving search analytics" });
        }
    }

    private Guid? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }
}
