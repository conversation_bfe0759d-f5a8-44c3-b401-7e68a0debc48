<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.Identity.Domain</name>
    </assembly>
    <members>
        <member name="T:LexAI.Identity.Domain.Entities.RefreshToken">
            <summary>
            Represents a refresh token for JWT authentication
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.RefreshToken.Token">
            <summary>
            The refresh token value
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.RefreshToken.UserId">
            <summary>
            The user ID this token belongs to
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.RefreshToken.User">
            <summary>
            The user this token belongs to
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.RefreshToken.ExpiresAt">
            <summary>
            When the token expires
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.RefreshToken.IsRevoked">
            <summary>
            Indicates if the token has been revoked
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.RefreshToken.RevokedAt">
            <summary>
            When the token was revoked
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.RefreshToken.RevokedBy">
            <summary>
            Who revoked the token
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.RefreshToken.RevocationReason">
            <summary>
            Reason for revocation
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.RefreshToken.IpAddress">
            <summary>
            IP address where the token was created
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.RefreshToken.UserAgent">
            <summary>
            User agent of the client that created the token
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.RefreshToken.IsActive">
            <summary>
            Indicates if the token is active (not expired and not revoked)
            </summary>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.RefreshToken.#ctor">
            <summary>
            Private constructor for EF Core
            </summary>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.RefreshToken.Create(System.Guid,System.DateTime,System.String,System.String)">
            <summary>
            Creates a new refresh token
            </summary>
            <param name="userId">User ID the token belongs to</param>
            <param name="expiresAt">When the token expires</param>
            <param name="ipAddress">IP address where the token was created</param>
            <param name="userAgent">User agent of the client</param>
            <returns>New refresh token instance</returns>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.RefreshToken.Revoke(System.String,System.String)">
            <summary>
            Revokes the refresh token
            </summary>
            <param name="revokedBy">Who is revoking the token</param>
            <param name="reason">Reason for revocation</param>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.RefreshToken.GenerateToken">
            <summary>
            Generates a cryptographically secure random token
            </summary>
            <returns>Random token string</returns>
        </member>
        <member name="T:LexAI.Identity.Domain.Entities.UserPermission">
            <summary>
            Represents a user permission in the system
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.UserPermission.UserId">
            <summary>
            The user ID this permission belongs to
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.UserPermission.User">
            <summary>
            The user this permission belongs to
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.UserPermission.Permission">
            <summary>
            The permission name/code
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.UserPermission.Resource">
            <summary>
            The resource this permission applies to (optional)
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.UserPermission.Action">
            <summary>
            The action this permission allows
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.UserPermission.GrantedAt">
            <summary>
            When this permission was granted
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.UserPermission.GrantedBy">
            <summary>
            Who granted this permission
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.UserPermission.ExpiresAt">
            <summary>
            When this permission expires (optional)
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.UserPermission.IsActive">
            <summary>
            Indicates if the permission is active
            </summary>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.UserPermission.#ctor">
            <summary>
            Private constructor for EF Core
            </summary>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.UserPermission.Create(System.Guid,System.String,System.String,System.String,System.String,System.Nullable{System.DateTime})">
            <summary>
            Creates a new user permission
            </summary>
            <param name="userId">User ID the permission belongs to</param>
            <param name="permission">Permission name/code</param>
            <param name="action">Action allowed</param>
            <param name="grantedBy">Who granted the permission</param>
            <param name="resource">Resource the permission applies to</param>
            <param name="expiresAt">When the permission expires</param>
            <returns>New user permission instance</returns>
        </member>
        <member name="T:LexAI.Identity.Domain.Entities.User">
            <summary>
            Represents a user in the LexAI system
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.Email">
            <summary>
            User's email address (unique identifier)
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.FirstName">
            <summary>
            User's first name
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.LastName">
            <summary>
            User's last name
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.PhoneNumber">
            <summary>
            User's phone number
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.PasswordHash">
            <summary>
            Hashed password
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.Role">
            <summary>
            User's role in the system
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.IsEmailVerified">
            <summary>
            Indicates if the user's email is verified
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.IsActive">
            <summary>
            Indicates if the user account is active
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.IsLocked">
            <summary>
            Indicates if the user account is locked
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.LockedAt">
            <summary>
            Date and time when the account was locked
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.FailedLoginAttempts">
            <summary>
            Number of failed login attempts
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.LastLoginAt">
            <summary>
            Date and time of the last login
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.LastLoginIpAddress">
            <summary>
            IP address of the last login
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.PreferredLanguage">
            <summary>
            User's preferred language
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.TimeZone">
            <summary>
            User's timezone
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.ProfilePictureUrl">
            <summary>
            User's profile picture URL
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.RefreshTokens">
            <summary>
            Collection of refresh tokens for this user
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.Permissions">
            <summary>
            Collection of user permissions
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.Entities.User.FullName">
            <summary>
            Full name of the user
            </summary>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.User.#ctor">
            <summary>
            Private constructor for EF Core
            </summary>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.User.Create(System.String,System.String,System.String,LexAI.Shared.Domain.Enums.UserRole,System.String)">
            <summary>
            Creates a new user
            </summary>
            <param name="email">User's email address</param>
            <param name="firstName">User's first name</param>
            <param name="lastName">User's last name</param>
            <param name="role">User's role</param>
            <param name="createdBy">ID of the user creating this user</param>
            <returns>New user instance</returns>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.User.SetPassword(System.String,System.String)">
            <summary>
            Sets the user's password
            </summary>
            <param name="password">Plain text password</param>
            <param name="updatedBy">ID of the user updating the password</param>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.User.VerifyPassword(System.String)">
            <summary>
            Verifies the provided password against the stored hash
            </summary>
            <param name="password">Password to verify</param>
            <returns>True if password is correct</returns>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.User.RecordSuccessfulLogin(System.String)">
            <summary>
            Records a successful login
            </summary>
            <param name="ipAddress">IP address of the login</param>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.User.RecordFailedLogin(System.String)">
            <summary>
            Records a failed login attempt
            </summary>
            <param name="ipAddress">IP address of the failed attempt</param>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.User.VerifyEmail(System.String)">
            <summary>
            Verifies the user's email
            </summary>
            <param name="verifiedBy">ID of the user performing the verification</param>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.User.Activate(System.String)">
            <summary>
            Activates the user account
            </summary>
            <param name="activatedBy">ID of the user performing the activation</param>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.User.Deactivate(System.String,System.String)">
            <summary>
            Deactivates the user account
            </summary>
            <param name="deactivatedBy">ID of the user performing the deactivation</param>
            <param name="reason">Reason for deactivation</param>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.User.UpdateProfile(System.String,System.String,System.String,System.String)">
            <summary>
            Updates user profile information
            </summary>
            <param name="firstName">New first name</param>
            <param name="lastName">New last name</param>
            <param name="phoneNumber">New phone number</param>
            <param name="updatedBy">ID of the user performing the update</param>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.User.ChangeRole(LexAI.Shared.Domain.Enums.UserRole,System.String)">
            <summary>
            Changes the user's role
            </summary>
            <param name="newRole">New role to assign</param>
            <param name="changedBy">ID of the user changing the role</param>
        </member>
        <member name="M:LexAI.Identity.Domain.Entities.User.UpdatePreferences(System.String,System.String,System.String)">
            <summary>
            Updates user preferences (language and timezone)
            </summary>
            <param name="preferredLanguage">Preferred language</param>
            <param name="timeZone">Time zone</param>
            <param name="updatedBy">ID of the user performing the update</param>
        </member>
        <member name="T:LexAI.Identity.Domain.ValueObjects.Email">
            <summary>
            Value object representing an email address
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.ValueObjects.Email.Value">
            <summary>
            The email address value
            </summary>
        </member>
        <member name="F:LexAI.Identity.Domain.ValueObjects.Email.EmailRegex">
            <summary>
            Regular expression for email validation
            </summary>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.Email.#ctor(System.String)">
            <summary>
            Private constructor for EF Core
            </summary>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.Email.Create(System.String)">
            <summary>
            Creates a new email value object
            </summary>
            <param name="email">Email address string</param>
            <returns>Email value object</returns>
            <exception cref="T:System.ArgumentException">Thrown when email is invalid</exception>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.Email.GetDomain">
            <summary>
            Gets the domain part of the email address
            </summary>
            <returns>Domain part of the email</returns>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.Email.GetLocalPart">
            <summary>
            Gets the local part of the email address (before @)
            </summary>
            <returns>Local part of the email</returns>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.Email.BelongsToDomain(System.String)">
            <summary>
            Checks if the email belongs to a specific domain
            </summary>
            <param name="domain">Domain to check</param>
            <returns>True if email belongs to the domain</returns>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.Email.GetAtomicValues">
            <summary>
            Gets the atomic values for equality comparison
            </summary>
            <returns>Collection of atomic values</returns>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.Email.op_Implicit(LexAI.Identity.Domain.ValueObjects.Email)~System.String">
            <summary>
            Implicit conversion from Email to string
            </summary>
            <param name="email">Email value object</param>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.Email.ToString">
            <summary>
            String representation of the email
            </summary>
            <returns>Email address string</returns>
        </member>
        <member name="T:LexAI.Identity.Domain.ValueObjects.PhoneNumber">
            <summary>
            Value object representing a phone number
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.ValueObjects.PhoneNumber.Value">
            <summary>
            The phone number value
            </summary>
        </member>
        <member name="P:LexAI.Identity.Domain.ValueObjects.PhoneNumber.CountryCode">
            <summary>
            The country code
            </summary>
        </member>
        <member name="F:LexAI.Identity.Domain.ValueObjects.PhoneNumber.PhoneRegex">
            <summary>
            Regular expression for phone number validation (international format)
            </summary>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.PhoneNumber.#ctor(System.String,System.String)">
            <summary>
            Private constructor for EF Core
            </summary>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.PhoneNumber.Create(System.String,System.String)">
            <summary>
            Creates a new phone number value object
            </summary>
            <param name="phoneNumber">Phone number string</param>
            <param name="countryCode">Country code (optional, defaults to +33 for France)</param>
            <returns>PhoneNumber value object</returns>
            <exception cref="T:System.ArgumentException">Thrown when phone number is invalid</exception>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.PhoneNumber.GetNationalFormat">
            <summary>
            Gets the national format of the phone number (without country code)
            </summary>
            <returns>National format phone number</returns>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.PhoneNumber.GetInternationalFormat">
            <summary>
            Gets the international format of the phone number
            </summary>
            <returns>International format phone number</returns>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.PhoneNumber.GetAtomicValues">
            <summary>
            Gets the atomic values for equality comparison
            </summary>
            <returns>Collection of atomic values</returns>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.PhoneNumber.op_Implicit(LexAI.Identity.Domain.ValueObjects.PhoneNumber)~System.String">
            <summary>
            Implicit conversion from PhoneNumber to string
            </summary>
            <param name="phoneNumber">PhoneNumber value object</param>
        </member>
        <member name="M:LexAI.Identity.Domain.ValueObjects.PhoneNumber.ToString">
            <summary>
            String representation of the phone number
            </summary>
            <returns>Phone number string</returns>
        </member>
    </members>
</doc>
